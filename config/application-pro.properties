# Server
server.port=${SERVER_PORT:9001}
server.servlet.context-path=/interface
server.compression.enabled=${SERVER_COMPRESSION_ENABLED:true}
server.compression.mime-types=${SERVER_COMPRESSION_MIME_TYPES:application/json,application/xml,text/html,text/xml,text/plain}
server.compression.min-response-size=${SERVER_COMPRESSION_MIN_RESPONSE_SIZE:1024}

# Logging
logging.config=classpath:logback-sentry.xml
logging.level.root=${LOGGING_LEVEL_ROOT:info}
logging.level.com.zaxxer.hikari=${LOGGING_LEVEL_HIKARICP:warn}
logging.level.com.bdtd.util.tdengine=${LOGGING_LEVEL_TDENGINE:info}
logging.level.com.bdtd.job=${LOGGING_LEVEL_JOB:info}
logging.level.com.bdtd.feign=${LOGGING_LEVEL_FEIGN:debug}
logging.level.com.bdtd.modules.camera.feign=${LOGGING_LEVEL_FEIGN:debug}
logging.level.com.bdtd.modules.*.dao=${LOGGING_LEVEL_DAO:info}
logging.level.com.bdtd.modules.*.mapper=${LOGGING_LEVEL_DAO:info}
logging.level.com.bdtd.modules.*.handler=${LOGGING_LEVEL_HANDLER:info}

# Spring åºç¡éç½®
spring.codec.max-in-memory-size=20MB

# Datasource
spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DATABASE}?useSSL=false&characterEncoding=utf-8&rewriteBatchedStatements=true&autoReconnect=true&useServerPrepStmts=false&stringtype=unspecified
spring.datasource.username=${POSTGRES_USERNAME}
spring.datasource.password=${POSTGRES_PASSWORD}
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.pool-name=hikaricp-pool
spring.datasource.hikari.minimum-idle=${DS_POOL_MIN_IDLE_SIZE:5}
spring.datasource.hikari.maximum-pool-size=${DS_POOL_MAX_POOL_SIZE:30}
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000

# JPA
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults = false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQL9Dialect

# MyBatis-Plus
mybatis-plus.type-aliases-package=com.bdtd.model
#mybatis.config-location=classpath:mybatis/mybatis-config.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.call-setters-on-nulls=true
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.mapper-locations=classpath:mybatis/mapper/**/*.xml
mybatis-plus.type-handlers-package=com.bdtd.util.sql

# Pagination
pagehelper.helperDialect=PostgreSQL
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql
pagehelper.page-size-zero=true

# TDengine
#taos.tdengine.connect-type=${TDENGINE_CONNECT_TYPE:restful}
taos.tdengine.host=${TDENGINE_HOST:tdengine}
taos.tdengine.port=${TDENGINE_PORT:6041}
taos.tdengine.username=${TDENGINE_USERNAME}
taos.tdengine.password=${TDENGINE_PASSWORD}
taos.tdengine.database=${TDENGINE_DATABASE:mineims}
## *********************************************************************************
#taos.tdengine.url=${TDENGINE_URL:*********************************************************************************}
#taos.tdengine.driver-class-name=${TDENGINE_DRIVER_CLASSNAME:com.taosdata.jdbc.TSDBDriver}
taos.tdengine.precision=${TDENGINE_PRECISION:ns}
taos.tdengine.max-sql-length=${TDENGINE_MAX_SQL_LENGTH:999999}
# è¿æ¥æ± éç½®
taos.tdengine.pool-enable=${TDENGINE_POOL_ENABLE:true}
taos.tdengine.pool-name=${TDENGINE_POOL_NAME:hikaricp-pool-td}
taos.tdengine.auto-commit=${TDENGINE_AUTO_COMMIT:true}
taos.tdengine.minimum-idle=${TDENGINE_MINIMUM_IDLE:10}
taos.tdengine.maximum-pool-size=${TDENGINE_MAXIMUM_POOL_SIZE:50}
taos.tdengine.idle-timeout=${TDENGINE_IDLE_TIMEOUT:30000}
taos.tdengine.connection-timeout=${TDENGINE_CONNECTION_TIMEOUT:30000}
taos.tdengine.max-lifetime=${TDENGINE_MAX_LIFETIME:1800000}
taos.tdengine.connection-test-query=${TDENGINE_CONNECTION_TEST_QUERY:show databases;}
# å¶ä»éç½®
# æ¯å¦å¼å¯æ¹éæåç»æé, é»è®¤ false
taos.tdengine.batch-fetch=${TDENGINE_BATCH_FETCH:false}
# åºç° SQL æ§è¡å¤±è´¥ï¼æ¯å¦ç»§ç»­, é»è®¤ false
taos.tdengine.batch-error-ignore=${TDENGINE_BATCH_ERROR_IGNORE:false}
# è¿æ¥è¶æ¶æ¶é´, é»è®¤ 5000, ä»å¨ REST è¿æ¥æ¶çæ
taos.tdengine.httpConnectTimeout=${TDENGINE_HTTP_CONNECT_TIMEOUT:5000}
# Socket è¶æ¶æ¶é´, é»è®¤ 5000, ä»å¨ REST è¿æ¥ä¸ batchfetch è®¾ç½®ä¸º false æ¶çæ
taos.tdengine.httpSocketTimeout=${TDENGINE_HTTP_SOCKET_TIMEOUT:5000}
# æ¶æ¯è¶æ¶æ¶é´, é»è®¤ 3000, ä»å¨ REST è¿æ¥ä¸ batchfetch è®¾ç½®ä¸º true æ¶çæ
taos.tdengine.messageWaitTimeout=${TDENGINE_MESSAGE_WAIT_TIMEOUT:3000}

# InfluxDB
spring.influxdb.url=${INFLUXDB_HOST}
spring.influxdb.port=${INFLUXDB_PORT}
spring.influxdb.username=${INFLUXDB_USERNAME}
spring.influxdb.password=${INFLUXDB_PASSWORD}
spring.influxdb.database=${INFLUXDB_DATABASE}

# RabbitMQ
spring.rabbitmq.addresses=${RABBITMQ_HOST}:${RABBITMQ_PORT}
spring.rabbitmq.host=${RABBITMQ_HOST}
spring.rabbitmq.port=${RABBITMQ_PORT}
spring.rabbitmq.username=${RABBITMQ_USERNAME}
spring.rabbitmq.password=${RABBITMQ_PASSWORD}
spring.rabbitmq.virtual-host=${RABBITMQ_VIRTUALHOST}
spring.rabbitmq.listener.type=direct
spring.rabbitmq.listener.concurrency=100
spring.rabbitmq.listener.max-concurrency=100
spring.rabbitmq.listener.prefetch=100
spring.rabbitmq.listener.direct.acknowledge-mode=manual
spring.rabbitmq.listener.simple.acknowledge-mode=manual

# æ¯å¦ä¸´æ¶ç¦æ­¢ RabbitMQ çæ¶è´¹
dev.rabbitmq.consumers.disable=${DEV_RABBITMQ_CONSUMERS_DISABLE:false}

# Redis
spring.redis.host=${REDIS_HOST}
spring.redis.password=${REDIS_PASSWORD}
spring.redis.port=${REDIS_PORT}
spring.redis.timeout=18000
spring.redis.jedis.pool.timeout=10000
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-active=500
spring.redis.jedis.pool.max-wait=18000

# æ¯å¦å¨æä»¶æåæ°è®¿é®æ¶ææ°å°è§£æå¤é¨åè¯·æ±
spring.servlet.multipart.resolve-lazily=false
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# å¾çä¸ä¼ è·¯å¾æ å° èªå®ä¹å±æ§
upload.picture.path=/home/<USER>/
upload.devicePicture.path=/home/<USER>/devicePicture/
web.filePath=/home/<USER>/

# Prometheus çæ§éç½®
#management.security.enabled=false
##prometheus+grafana+springboot2çæ§éæéç½®
#management.metrics.export.prometheus.enabled=true
#management.metrics.export.jmx.enabled=true
#management.endpoints.web.exposure.include='*'
#management.endpoints.web.base-path=/metrics
##prometheus+grafana+springboot2çæ§éæéç½®
management.endpoints.web.exposure.include=*
management.metrics.tags.application=DataMonitor

# æ°æ®æ¥å¥å¨é¾è·¯çæ§éç½®
access_monitor.ip=${ACCESS_MONITOR_IP:************}
access_monitor.port=${ACCESS_MONITOR_PORT:9090}
access_monitor.nodered=${ACCESS_MONITOR_NODERED:localhost:1880}
access_monitor.igs=${ACCESS_MONITOR_IGS:*************}

# æ°æ®æ¥å¥å®¡æ¹éç½®
dingding.group_tag_id=${DINGDING_GROUP_TAG_ID:-1}
dataroute.access.url=${DATAROUTE_ACCESS_URL:http://localhost:9004}
# http://localhost:9004, http://*************:9005
dataroute.approval.url=${DATAROUTE_APPROVAL_URL:http://localhost:9005}
dataroute.approval.minecode=${MINECODE:0}

# æ¥è¡¨å®æ¶ä»»å¡åå¶å¼å³
report.enabled=false
report.timer.hour=30 * * * * ?
report.timer.day=50 * * * * ?
report.timer.month=0 0 15 1 * ?

# å®æ¶ä»»å¡éç½®
scheduled.model-system-load.cron=${SCHEDULED_MODEL_SYSTEM_LOAD_CRON:0 0/5 * * * ?}
scheduled.model-system-alive-check.cron=${SCHEDULED_MODEL_SYSTEM_ALIVE_LOAD_CRON:0 0/5 * * * ?}
scheduled.model-system-alive-check.cron-minutes=${SCHEDULED_MODEL_SYSTEM_ALIVE_LOAD_CRON_MINUTES:5}
scheduled.model-system-alive-check.prepend-global-check=${SCHEDULED_MODEL_SYSTEM_ALIVE_LOAD_PREPEND_GLOBAL_CHECK:false}
# 1800000
scheduled.data-route-monitor.fixed-rate=${SCHEDULED_DATA_ROUTE_MONITOR_FIXED_RATE:}
scheduled.ding-approval.enabled=${SCHEDULED_DING_APPROVAL_ENABLED:false}
scheduled.ding-approval-sync.fixed-rate=240000
scheduled.ding-approval-sql.fixed-rate=300000
scheduled.date-route-access.fixed-rate=360000
scheduled.new-system-approval.fixed-rate=300000
scheduled.access-node-status-detect.check-mode=${SCHEDULED_ACCESS_NODE_STATUS_DETECT_CHECK_MODE:1}
scheduled.access-node-status-detect.fixed-delay=${SCHEDULED_ACCESS_NODE_STATUS_DETECT_FIXED_DELAY:900000}
scheduled.access-node-status-detect.init-delay=${SCHEDULED_ACCESS_NODE_STATUS_DETECT_INIT_DELAY:120000}
# 0 */10 * * * ?
scheduled.working-progress-settings-sync=${SCHEDULED_WORKING_PROGRESS_SETTINGS_SYNC:}
# new åæ­¥æè¿æ´æ°çå·¥ä½é¢, all å¨éåæ­¥
scheduled.working-progress-settings-sync-mode=${SCHEDULED_WORKING_PROGRESS_SETTINGS_SYNC_MODE:new}
# all å¨é, new å¢é
scheduled.working-progress-settings-api-push-mode=${SCHEDULED_WORKING_PROGRESS_SETTINGS_API_PUSH_MODE:new}
scheduled.position-calc-history-sync=${SCHEDULED_POSITION_CALC_HISTORY_SYNC:0 */5 * * * ?}
scheduled.position-calc-history-sync-cron-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_CRON_MINUTES:5}
scheduled.position-calc-history-sync-tolerance-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_TOLERANCE_MINUTES:0}
scheduled.position-calc-history-sync-max-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_MAX_MINUTES:60}
scheduled.position-calc-history-sync-url=${CJSC_POSITION_CALC_BIZ_URL:http://172.31.54.114:9092/ref/cardHistory/list}
scheduled.position-raw-history-sync=${SCHEDULED_POSITION_RAW_HISTORY_SYNC:0 */5 * * * ?}
scheduled.position-raw-history-sync-cron-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_CRON_MINUTES:5}
scheduled.position-raw-history-sync-tolerance-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_TOLERANCE_MINUTES:0}
scheduled.position-raw-history-sync-max-minutes=${SCHEDULED_POSITION_RAW_HISTORY_SYNC_MAX_MINUTES:30}
scheduled.position-raw-history-sync-url=${CJSC_POSITION_RAW_BIZ_URL:http://172.31.54.114:9092/ref/cardAllHistory/list}
scheduled.position-mine-heartbeat-sync=${SCHEDULED_POSITION_MINE_HEARTBEAT_SYNC:0 */5 * * * ?}
scheduled.position-mine-heartbeat-check-field=${SCHEDULED_POSITION_MINE_HEARTBEAT_CHECK_FIELD:update_time}
scheduled.position-mine-heartbeat-url=${CJSC_POSITION_MINE_HEARTBEAT_BIZ_URL:http://172.31.54.114:9092/sys/heartBeatInfo/list}
scheduled.progress-realtime-global-check=${SCHEDULED_PROGRESS_REALTIME_GLOBAL_CHECK:0 */5 * * * ?}
scheduled.progress-realtime-global-check-threshold=${SCHEDULED_PROGRESS_REALTIME_GLOBAL_CHECK_THRESHOLD:10}
scheduled.progress-realtime-global-check-notify-phone=${SCHEDULED_PROGRESS_REALTIME_GLOBAL_CHECK_NOTIFY_PHONE:15822780432|546L5pm65aiB}
scheduled.auto-alarm-msg-trash.cron=${SCHEDULED_AUTO_ALARM_MSG_TRASH_CRON:0 0 2 1 * ?}
scheduled.auto-alarm-msg-trash.keep-days=${SCHEDULED_AUTO_ALARM_MSG_TRASH_KEEP_DAYS:90}

# ä¸å¡éç½®
biz.mine_code=${MINECODE:100000}
# æ¨éå­ç³»ç»å¨çº¿ä¿¡æ¯å° mes
biz.sync-system-monitor-to-mes=${SYNC_SYSTEM_MONITOR_TO_MES:false}
# æ¨éå­ç³»ç»å¨çº¿ä¿¡æ¯å° é¢è­¦æ¥è­¦, 0ä¸æ¨éï¼1åå§æ¨éï¼ä¸å¤æ­å¨çº¿ç¶ææ¯å¦ååï¼2è¿æ»¤æ¨éï¼åªæç³»ç»ç¶æåçååçæåµä¸ææ¨é
biz.sync-system-monitor-to-push-msg=${SYNC_SYSTEM_MONITOR_TO_PUSH_MSG:2}
# ãåºå¼ãæ¨éå­ç³»ç»å¨çº¿ä¿¡æ¯å°é¢è­¦æ¥è­¦ï¼æ¯å¦åºäºé¢è­¦æ¥è­¦ä¾§çææ æ¥è­¦ç¶æ
biz.sync-to-push-msg-based-on-indicator-status=${SYNC_TO_PUSH_MSG_BASED_ON_INDICATOR_STATUS:false}
# åç¿è½æºéå¢ç«¯æ¥å£ æç®¡ä¸»é®ç±»å point_id, area_name
biz.ykny-api-tube-key=${BIZ_YKNY_API_TUBE_KEY:area_name}
# å·¥ä½é¢éç½®ä¸åå¿è·³æ¥å£å·¥ä½æ¨¡å¼: cache é»è®¤, direct
biz.working-face-setting-heartbeat-mode=${WORKING_FACE_SETTING_HEARTBEAT_MODE:cache}
# å·¥ä½é¢éç½®ä¸åæ¨¡å¼: rmq/api(é»è®¤)/all/none, rmq ä¸ºä¸å¡ä¾§ä¸ååæ´éç¥å°æ¬æå¡ï¼æ¬æå¡å»è°ç¨ä¸å¡ä¾§æ¥å£, api ä¸ºä¸å¡ä¾§åæ¬æå¡æ¥å£, all åæ¶å¯ç¨, none å³é­
biz.working-face-setting-sync-mode=${WORKING_FACE_SETTING_SYNC_MODE:api}
# åæå·¥ä½é¢ç¶ææ¶æ¯å¦èèå·¥ä½æ¨¡å¼ï¼é»è®¤åæ¬æ¯å¦å é¤ãçäº§ç¶æ
# å·¥ä½æ¨¡å¼å½±åæ¥å¥ç³»ç»ï¼model_systemï¼ç route_service å­æ®µï¼åªè¦ç»å®è¿å·¥ä½æ¨¡å¼ route_service æä¼èµå¼ 1
biz.working-face-state-include-work-mode=${WORKING_FACE_STATE_INCLUDE_WORK_MODE:false}
# éç¤å·¥ä½é¢å¯¹åºçç³»ç»å¯ç¨çå·¥ä½é¢ç¶æï¼åè§éå·åéå¤ä¸ª (åºå¼ï¼~~éç¤é¢ç¶æ: 1å¾å¼éã2çäº§ä¸­ã3å·²æåã4åé~~)
# éç¤é¢ç¶æåæ´ä¸ºï¼10è®¾è®¡ã11æè¿ã12è´¯éã13æ²»ç¾ã20çäº§ã30æåã40åé
biz.mining-face-working-state-list=${MINING_FACE_WORKING_STATE_LIST:2,20}
# æè¿å·¥ä½é¢å¯¹åºçç³»ç»ç¦ç¨çå·¥ä½é¢ç¶æï¼åè§éå·åéå¤ä¸ª (æè¿é¢ç¶æ: 1å¾æ½å·¥ã2æ½å·¥ä¸­ã3å·²æåã4åæ)
biz.driving-face-working-state-list=${DRIVING_FACE_WORKING_STATE_LIST:2}
# åå§æ°æ®åæ­¥æ¶ï¼æ°æ®ååºç°å¤å¤©çº¿çæ°æ®ï¼æ¯å¦ä¸¢å¼éææ°æ¶é´çæ ç­¾å¡æ°æ®
biz.position-raw-history.discard-not-newest=${POSITION_RAW_HISTORY_DISCARD_NOT_NEWEST:true}
## åå§æ°æ®åæ­¥æ¶ï¼æ°æ®ååºç°å¤å¤©çº¿çæ°æ®ï¼æ¯å¦ä»¥è¾æ°æ¶é´çæ ç­¾å¡è¿è¡å¤æ­
#biz.position-raw-history.wrong-channel-check-by-newest=${POSITION_RAW_HISTORY_WRONG_CHANNEL_CHECK_BY_NEWEST:true}
# åå§æ°æ®æ¥è¯¢åè®¸æ¶é´èå´ï¼å°æ¶ï¼
biz.position-raw-history.query-limit=${POSITION_RAW_HISTORY_QUERY_LIMIT:744}
# å¤©çº¿åè£å¤æ­æ¶é¿ï¼è¶è¿æ­¤æ¶é´ææ¨éæ¥è­¦ï¼ç§ï¼
biz.wrong-channel-warn-limit=${WRONG_CHANNEL_WARN_LIMIT:300}

# å¹³å°å¾®æå¡ url
base.biz.url=${BASE_BIZ_URL}
gis.biz.url=${GIS_BIZ_HOST}
mes.biz.url=${MES_BIZ_HOST}
push-msg.biz.url=${PUSH_MSG_URL:http://backend-push-msg:9001}
cjsc.biz.url=${CJSC_BIZ_URL:http://*************:8004}
cjsc.gis-base.url=${CJSC_GIS_BASE_URL:http://*************:21000/}

# ææå¾é¢è§é¾æ¥å°å
automation.topo.url_format=${AUTOMATION_TOPO_URL_FORMAT:http://*************/preview-topo/#/real/{TOPOCD}?zoom=false}
# ææå¤é¨é¾æ¥å°åæ ¼å¼
topo.external_url_format=${TOPO_EXTERNAL_URL_FORMAT:http://localhost/}
# æ§å¶å½ä»¤ä¸åå°å
point.single.url=${POINT_URL:http://rednode:1880}/api/device_control
point.multi.url=${POINT_URL:http://rednode:1880}/api/multi_control
# æ§å¶å½ä»¤ä¸ååºå®ç½åå Token
point.allowed.tokens=${POINT_ALLOWED_TOKENS:625e10d5573a45139cd98cd1e810f996}

# åç®¡å¹³å°ç¨æ·ç¸å³
# åç®¡å¹³å°å°å http://127.0.0.1 å¦ä¸éç½®åé»è®¤ä¸monitorå°åç¸å
blade.backend.url=${BLADE_BACKEND_URL}
# é¡¹ç®è·¯å¾
backend.context-path=/
# è·åå½åç»å½ç¨æ·
backend.url.currentuser=blade-user/currentUser
# æ ¹æ®ç¨æ·ååå¯ç è·åç¨æ·ä¿¡æ¯
backend.url.userinfo=blade-user/userinfo
# æ ¹æ® ç¨æ·id-list è·å ç¨æ·-list
backend.url.userlist=blade-user/userlist
# æ ¹æ®ç¨æ·idè·åç¨æ·ä¿¡æ¯
backend.url.userbyid=blade-user/userbyid

# è§é¢æ¥å¥éç½®
ai-algorithm.address=${AI_ALGORITHM_ADDR:-1}
ai-algorithm.port=${AI_ALGORITHM_PORT:8000}
# AI_ALGORITHM_URL http://*************:8000/v1.0
##ai-algorithm.url=${AI_ALGORITHM_URL:http://*************:8000/v1.0}
#ai-algorithm.videoUrl=${AI_ALGORITHM_VIDEO_URL:http://*************:8000/v1.0}
# http://video-server:9000
mos.video.url=${MOS_VIDEO_URL:}
ivs.sync=${IVSSYNC:false}
ivs.backend.url=${IVS_BACKEND_URL:-1}

# é¿éç­ä¿¡
# http://************:60021/interface/apidata
ali.address=
ali.appkey=test
ali.secret=123456
ali.interfaceName=sendApprovalAndConfirm.callback
ali.version=1.0

# è®¾å¤æ¨¡å
luoyang.url=${LUOYANG_URL:http://localhost/union}
