image: docker:latest

variables:
  DOCKER_IMAGE_TAG: $CI_COMMIT_REF_SLUG
  DOCKER_IMAGE_NAME: $CI_REGISTRY

services:
 - docker:dind

stages:
  - tags
  - build-push
 # - latest

before_script:
  - export DOCKER_IMAGE_NAME=$DOCKER_IMAGE_NAME/$CI_PROJECT_PATH

tags:
  stage: tags
  script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - export DOCKER_IMAGE_TAG=$CI_COMMIT_TAG
  - docker build -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
  - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
  only:
  - tags

build-push:
  stage: build-push
  script:
  #- docker login -u $LOCAL_CI_REGISTRY_USER -p $LOCAL_CI_REGISTRY_PASSWORD $LOCAL_CI_REGISTRY
  #- export DOCKER_IMAGE_NAME=$LOCAL_CI_REGISTRY/${CI_PROJECT_PATH#*/}
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  #- export DOCKER_IMAGE_NAME=$CI_REGISTRY/${CI_PROJECT_PATH#*/}
  - if [ $DOCKER_IMAGE_TAG  == "master" ]; then DOCKER_IMAGE_TAG="latest"; fi
  - echo $DOCKER_IMAGE_TAG
  - docker build -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
  - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
  only:
  - master
  - dev

