<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 数据库驱动jar -->
    <classPathEntry location="C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.2.5\postgresql-42.2.5.jar"/>

    <context id="DB2Tables" targetRuntime="MyBatis3">
        <!-- 去除注释  -->
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="org.postgresql.Driver"
                        connectionURL="***********************************"
                        userId="postgres"
                        password="pg-dev123*">
        </jdbcConnection>

        <!-- 默认false, Java type resolver will always use java.math.BigDecimal if the database column is of type DECIMAL or NUMERIC. -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成实体类 指定包名 以及生成的地址 （可以自定义地址，但是路径不存在不会自动创建  使用Maven生成在target目录下，会自动创建） -->
        <javaModelGenerator targetPackage="com.bdtd.model" targetProject="MAVEN">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成SQLMAP文件 -->
        <sqlMapGenerator targetPackage="com.bdtd.mybatis" targetProject="MAVEN">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- 生成Dao文件 可以配置 type="XMLMAPPER"生成xml的dao实现  context id="DB2Tables" 修改targetRuntime="MyBatis3"  -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.bdtd.dao" targetProject="MAVEN">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <!-- 对应数据库表 mysql可以加入主键自增 字段命名 忽略某字段等 -->
        <!--
        <table tableName="monitor_metrics" domainObjectName="MonitorMetrics" />
        <table tableName="monitor_fault" domainObjectName="MonitorFault" />
        <table tableName="monitor_status" domainObjectName="MonitorStatus" />
        <table tableName="monitor_control" domainObjectName="MonitorControl" />
        <table tableName="monitor_threshold" domainObjectName="MonitorThreshold" />
        <table tableName="ding_approval" domainObjectName="DingApproval"/>
        <table tableName="alarm" domainObjectName="MonitorAlarm" />
        <table tableName="structure_view" domainObjectName="Topology" /> -->
    </context>
</generatorConfiguration>