<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <typeAliases>
        <typeAlias alias="Integer" type="java.lang.Integer"/>
        <typeAlias alias="Long" type="java.lang.Long"/>
        <typeAlias alias="HashMap" type="java.util.HashMap"/>
        <typeAlias alias="LinkedHashMap" type="java.util.LinkedHashMap"/>
        <typeAlias alias="ArrayList" type="java.util.ArrayList"/>
        <typeAlias alias="LinkedList" type="java.util.LinkedList"/>
    </typeAliases>
    <typeHandlers>
        <!-- When using MyBatis 3.4 or later, you can simply add this artifact on your classpath
                and MyBatis will automatically register the provided type handlers.
                If you are using an older version, you need to add the type handlers
                to your mybatis-config.xml as follow: -->
        <!--
        <typeHandler handler="org.apache.ibatis.type.InstantTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalDateTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalDateTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.LocalTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.OffsetDateTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.OffsetTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.ZonedDateTimeTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.YearTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.MonthTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.YearMonthTypeHandler" />
        <typeHandler handler="org.apache.ibatis.type.JapaneseDateTypeHandler" /> -->
    </typeHandlers>
    <settings>
        <!--
        <setting name="mapUnderscoreToCamelCase" value="true"/> -->
        <setting name="callSettersOnNulls" value="true"/>
    </settings>
    <!--
    <typeAliases>
        <package name="com.bdtd.model"/>
    </typeAliases> -->
</configuration>