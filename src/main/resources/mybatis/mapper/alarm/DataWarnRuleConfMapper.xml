<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.alarm.mapper.DataWarnRuleConfMapper">

    <resultMap id="BaseResultMap" type="com.bdtd.modules.alarm.entity.DataWarnRuleConf">
        <id column="point_id" jdbcType="VARCHAR" property="pointId" />
        <id column="indicator_id" jdbcType="VARCHAR" property="indicatorId" />
        <result column="indicator_title" jdbcType="VARCHAR" property="indicatorTitle" />
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
        <result column="settig_type" jdbcType="VARCHAR" property="settigType" />
        <result column="rules" jdbcType="VARCHAR" property="rules" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <delete id="removeByDataWarnRuleConfVo" parameterType="com.bdtd.modules.alarm.dto.DataWarnRuleConfVo">
        delete
        from data_warn_rule_conf
        <where>
            <if test="dataWarnRuleConf.pointIdList != null and dataWarnRuleConf.pointIdList.size > 0">
                and point_id in
                <foreach collection="dataWarnRuleConf.pointIdList" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="dataWarnRuleConf.indicatorId != null and dataWarnRuleConf.indicatorId != ''">
                and indicator_id = #{dataWarnRuleConf.indicatorId,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>

    <update id="updateBatchByIndicatorId" parameterType="com.bdtd.modules.alarm.entity.DataWarnRuleConf">
        update data_warn_rule_conf
        <set>
            <if test="entity.rules != null">
                rules=#{entity.rules,jdbcType=VARCHAR},
                updated_at=now()
            </if>
        </set>
        where indicator_id = #{entity.indicatorId,jdbcType=VARCHAR}
    </update>
</mapper>
