<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.alarm.dao.AlarmHistoryMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.alarm.entity.AlarmHistoryEntity">
        <id column="point_id" jdbcType="VARCHAR" property="pointId"/>
        <id column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="system_id" jdbcType="VARCHAR" property="systemId"/>
        <result column="alarm_type" jdbcType="VARCHAR" property="alarmType"/>
        <result column="reserve_first" jdbcType="VARCHAR" property="reserveFirst"/>
        <result column="reserve_second" jdbcType="VARCHAR" property="reserveSecond"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    point_id, begin_time, system_id, alarm_type, end_time, updated_at, created_at, reserve_first,
    reserve_second, level
  </sql>
    <select id="selectByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmHistoryEntityExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from alarm_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from alarm_history
        where point_id = #{pointId,jdbcType=VARCHAR}
        and begin_time = #{beginTime,jdbcType=TIMESTAMP}
    </select>
    <delete id="deleteByPrimaryKey">
    delete from alarm_history
    where point_id = #{pointId,jdbcType=VARCHAR}
      and begin_time = #{beginTime,jdbcType=TIMESTAMP}
  </delete>
    <delete id="deleteByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmHistoryEntityExample">
        delete from alarm_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.bdtd.modules.alarm.entity.AlarmHistoryEntity">
    insert into alarm_history (point_id, begin_time, system_id,
      alarm_type, end_time,reserve_first, reserve_second,
      level)
    values (#{pointId,jdbcType=VARCHAR}, #{beginTime,jdbcType=TIMESTAMP}, #{systemId,jdbcType=VARCHAR},
      #{alarmType,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP},  #{reserveFirst,jdbcType=VARCHAR}, #{reserveSecond,jdbcType=VARCHAR},
      #{level,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective" parameterType="com.bdtd.modules.alarm.entity.AlarmHistoryEntity">
        insert into alarm_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointId != null">
                point_id,
            </if>
            <if test="beginTime != null">
                begin_time,
            </if>
            <if test="systemId != null">
                system_id,
            </if>
            <if test="alarmType != null">
                alarm_type,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="reserveFirst != null">
                reserve_first,
            </if>
            <if test="reserveSecond != null">
                reserve_second,
            </if>
            <if test="level != null">
                level,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointId != null">
                #{pointId,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="systemId != null">
                #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="alarmType != null">
                #{alarmType,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reserveFirst != null">
                #{reserveFirst,jdbcType=VARCHAR},
            </if>
            <if test="reserveSecond != null">
                #{reserveSecond,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmHistoryEntityExample"
            resultType="java.lang.Long">
        select count(*) from alarm_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update alarm_history
        <set>
            <if test="record.pointId != null">
                point_id = #{record.pointId,jdbcType=VARCHAR},
            </if>
            <if test="record.beginTime != null">
                begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.systemId != null">
                system_id = #{record.systemId,jdbcType=VARCHAR},
            </if>
            <if test="record.alarmType != null">
                alarm_type = #{record.alarmType,jdbcType=VARCHAR},
            </if>
            <if test="record.endTime != null">
                end_time = #{record.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.reserveFirst != null">
                reserve_first = #{record.reserveFirst,jdbcType=VARCHAR},
            </if>
            <if test="record.reserveSecond != null">
                reserve_second = #{record.reserveSecond,jdbcType=VARCHAR},
            </if>
            <if test="record.level != null">
                level = #{record.level,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update alarm_history
        set point_id = #{record.pointId,jdbcType=VARCHAR},
            begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
            system_id = #{record.systemId,jdbcType=VARCHAR},
            alarm_type = #{record.alarmType,jdbcType=VARCHAR},
            end_time = #{record.endTime,jdbcType=TIMESTAMP},
            reserve_first = #{record.reserveFirst,jdbcType=VARCHAR},
            reserve_second = #{record.reserveSecond,jdbcType=VARCHAR},
            level = #{record.level,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.alarm.entity.AlarmHistoryEntity">
        update alarm_history
        <set>
            <if test="systemId != null">
                system_id = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="alarmType != null">
                alarm_type = #{alarmType,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reserveFirst != null">
                reserve_first = #{reserveFirst,jdbcType=VARCHAR},
            </if>
            <if test="reserveSecond != null">
                reserve_second = #{reserveSecond,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
        </set>
        where point_id = #{pointId,jdbcType=VARCHAR}
        and begin_time = #{beginTime,jdbcType=TIMESTAMP}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bdtd.modules.alarm.entity.AlarmHistoryEntity">
    update alarm_history
    set system_id = #{systemId,jdbcType=VARCHAR},
      alarm_type = #{alarmType,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      reserve_first = #{reserveFirst,jdbcType=VARCHAR},
      reserve_second = #{reserveSecond,jdbcType=VARCHAR},
      level = #{level,jdbcType=INTEGER}
    where point_id = #{pointId,jdbcType=VARCHAR}
      and begin_time = #{beginTime,jdbcType=TIMESTAMP}
  </update>


    <select id="select" parameterType="String" resultType="java.util.Map">
    ${value}
  </select>

    <insert id="insertBatchAlarmHistory">
        insert into alarm_history ( point_id,system_id,alarm_type,
        begin_time,end_time,level,reserve_first,reserve_second)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.pointId,jdbcType=VARCHAR},#{item.systemId,jdbcType=VARCHAR},#{item.alarmType,jdbcType=VARCHAR},
            #{item.beginTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.level,jdbcType=INTEGER},
            #{item.reserveFirst,jdbcType=VARCHAR},#{item.reserveSecond,jdbcType=VARCHAR})
        </foreach>
        ON conflict(point_id,begin_time) do update set
        level= excluded.level,system_id= excluded.system_id,alarm_type= excluded.alarm_type,reserve_first=
        excluded.reserve_first,reserve_second= excluded.reserve_second
    </insert>
</mapper>