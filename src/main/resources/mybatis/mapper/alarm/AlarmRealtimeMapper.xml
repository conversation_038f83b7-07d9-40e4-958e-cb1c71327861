<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.alarm.dao.AlarmRealtimeMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.alarm.entity.AlarmRealtimeEntity">
    <id column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="alarm_type" jdbcType="VARCHAR" property="alarmType" />
    <result column="reserve_first" jdbcType="VARCHAR" property="reserveFirst" />
    <result column="reserve_second" jdbcType="VARCHAR" property="reserveSecond" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="timestamp" jdbcType="TIMESTAMP" property="timestamp" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    point_id, system_id, alarm_type, begin_time, updated_at, created_at, reserve_first,
    reserve_second, timestamp
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmRealtimeEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from alarm_realtime
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from alarm_realtime
    where point_id = #{pointId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from alarm_realtime
    where point_id = #{pointId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmRealtimeEntityExample">
    delete from alarm_realtime
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bdtd.modules.alarm.entity.AlarmRealtimeEntity">
    insert into alarm_realtime (point_id, system_id, alarm_type,
      begin_time,
      reserve_first, reserve_second, timestamp
      )
    values (#{pointId,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, #{alarmType,jdbcType=VARCHAR},
      #{beginTime,jdbcType=TIMESTAMP},
      #{reserveFirst,jdbcType=VARCHAR}, #{reserveSecond,jdbcType=VARCHAR}, #{timestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bdtd.modules.alarm.entity.AlarmRealtimeEntity">
    insert into alarm_realtime
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        point_id,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="alarmType != null">
        alarm_type,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="reserveFirst != null">
        reserve_first,
      </if>
      <if test="reserveSecond != null">
        reserve_second,
      </if>
      <if test="timestamp != null">
        timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="alarmType != null">
        #{alarmType,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveFirst != null">
        #{reserveFirst,jdbcType=VARCHAR},
      </if>
      <if test="reserveSecond != null">
        #{reserveSecond,jdbcType=VARCHAR},
      </if>
      <if test="timestamp != null">
        #{timestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bdtd.modules.alarm.dto.AlarmRealtimeEntityExample" resultType="java.lang.Long">
    select count(*) from alarm_realtime
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update alarm_realtime
    <set>
      <if test="record.pointId != null">
        point_id = #{record.pointId,jdbcType=VARCHAR},
      </if>
      <if test="record.systemId != null">
        system_id = #{record.systemId,jdbcType=VARCHAR},
      </if>
      <if test="record.alarmType != null">
        alarm_type = #{record.alarmType,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reserveFirst != null">
        reserve_first = #{record.reserveFirst,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveSecond != null">
        reserve_second = #{record.reserveSecond,jdbcType=VARCHAR},
      </if>
      <if test="record.timestamp != null">
        timestamp = #{record.timestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update alarm_realtime
    set point_id = #{record.pointId,jdbcType=VARCHAR},
    system_id = #{record.systemId,jdbcType=VARCHAR},
    alarm_type = #{record.alarmType,jdbcType=VARCHAR},
    begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
    reserve_first = #{record.reserveFirst,jdbcType=VARCHAR},
    reserve_second = #{record.reserveSecond,jdbcType=VARCHAR},
    timestamp = #{record.timestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.alarm.entity.AlarmRealtimeEntity">
    update alarm_realtime
    <set>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="alarmType != null">
        alarm_type = #{alarmType,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveFirst != null">
        reserve_first = #{reserveFirst,jdbcType=VARCHAR},
      </if>
      <if test="reserveSecond != null">
        reserve_second = #{reserveSecond,jdbcType=VARCHAR},
      </if>
      <if test="timestamp != null">
        timestamp = #{timestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where point_id = #{pointId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bdtd.modules.alarm.entity.AlarmRealtimeEntity">
    update alarm_realtime
    set system_id = #{systemId,jdbcType=VARCHAR},
      alarm_type = #{alarmType,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      timestamp = #{timestamp,jdbcType=TIMESTAMP},
      reserve_first = #{reserveFirst,jdbcType=VARCHAR},
      reserve_second = #{reserveSecond,jdbcType=VARCHAR},
      timestamp = #{timestamp,jdbcType=TIMESTAMP}
    where point_id = #{pointId,jdbcType=VARCHAR}
  </update>

  <insert id="upsertBatchAlarmRealtime">
    insert into alarm_realtime ( point_id,system_id,alarm_type,
    begin_time,reserve_first,reserve_second,timestamp)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.point_id,jdbcType=VARCHAR},#{item.system_id,jdbcType=VARCHAR},#{item.alarm_type,jdbcType=VARCHAR},
      #{item.begin_time,jdbcType=TIMESTAMP},#{item.reserve_first,jdbcType=VARCHAR},#{item.reserve_second,jdbcType=VARCHAR},
      #{item.timestamp,jdbcType=TIMESTAMP})
    </foreach>
    ON conflict(point_id) do update set
    system_id= excluded.system_id,alarm_type= excluded.alarm_type,begin_time= excluded.begin_time,reserve_first= excluded.reserve_first,
    reserve_second= excluded.reserve_second,timestamp= excluded.timestamp
  </insert>

  <select id="select" parameterType="String" resultType="java.util.Map">
    ${value}
  </select>

  <delete id="deleteBatchByPointIds" parameterType="java.util.List">
    delete from alarm_realtime  where point_id in
    <foreach item="pointId" collection="list" open="(" separator="," close=")">
      #{pointId,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <delete id="deleteBatchByPointIdAndBeginTime" parameterType="java.util.List">
    delete from alarm_realtime  where
    <foreach collection="list"  item="item" separator=" or " index="index">
      (point_id= #{item.pointId,jdbcType=VARCHAR} and begin_time= #{item.beginTime,jdbcType=TIMESTAMP})
    </foreach>
  </delete>

</mapper>
