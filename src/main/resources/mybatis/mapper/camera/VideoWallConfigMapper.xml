<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.camera.mapper.VideoWallConfigMapper">

    <select id="listByCameraInfo" resultType="com.bdtd.modules.camera.entity.VideoWallConfig">
        SELECT vwc.ID,
               vwc.camera_id,
               vwc."number",
               ci."name",
               ci.ip,
               ci.play_url
        FROM video_wall_config vwc
                 LEFT JOIN camera_info ci ON vwc.camera_id = ci.ID
        ORDER BY vwc."number" ASC
    </select>
</mapper>
