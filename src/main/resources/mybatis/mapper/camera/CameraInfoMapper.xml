<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.camera.mapper.CameraInfoMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="cameraInfoResultMap" type="com.bdtd.modules.camera.entity.CameraInfo">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="area_id" property="areaId"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="path" property="path"/>
        <result column="online" property="online"/>
        <result column="factory" property="factory"/>
        <result column="channel_id" property="channelPort"/>
        <result column="protocol_port" property="protocolPort"/>
        <result column="protocol_name" property="protocolName"/>
        <result column="ws_url" property="wsUrl"/>
        <result column="mos_camera_id" property="mosCameraId"/>
        <result column="device_port" property="devicePort"/>
        <result column="device_ip" property="deviceIp"/>
        <result column="play_url" property="playUrl"/>
<!--        <result column="audio_enable" property="audioEnable"/>-->
<!--        <result column="display_name" property="displayName"/>-->
    </resultMap>


    <select id="selectCameraByGroupId" resultMap="cameraInfoResultMap">
        select camera_info.*  from camera_info INNER JOIN camera_group_mapping on
            camera_info."id" = camera_group_mapping.camera_id where camera_group_mapping.group_id =#{groupId,jdbcType=INTEGER}
    </select>
</mapper>
