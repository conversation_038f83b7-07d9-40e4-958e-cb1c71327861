<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.camera.mapper.CameraAreaMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="cameraAreaResultMap" type="com.bdtd.modules.camera.entity.CameraArea">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="location_flag" property="locationFlag"/>
        <result column="map_id" property="mapId"/>
        <result column="num" property="num"/>
    </resultMap>


    <select id="selectCameraAreaPage" resultMap="cameraAreaResultMap">
        select * from camera_area where is_deleted = 0
    </select>

    <select id="findAreaByAreaName" resultMap="cameraAreaResultMap">
        select * from camera_area
        <where>
            <if test="areaName != null">
                name like CONCAT('%',#{areaName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>
