<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.camera.mapper.CameraGroupMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="cameraGroupResultMap" type="com.bdtd.modules.camera.entity.CameraGroup">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="parent_id" property="parentId"/>
        <!-- 多表关联映射 -->
        <collection property="cameras" ofType="com.bdtd.modules.camera.entity.CameraInfo">
            <id property="id" column="camera_id"/>
            <result property="name" column="camera_name"/>
        </collection>
    </resultMap>





    <select id="selectCameraByGroupId" resultMap="cameraGroupResultMap">
        select g.id,g.parent_id,g."name" ,c."id" as camera_id,c.name as camera_name from camera_group g
        left join camera_group_mapping m on g."id"=m.group_id
        left join camera_info c on m.camera_id =c.id
        <where>
            <if test="cameraName != null">
                c.name like %{cameraName}%
            </if>
            <if test="groupName != null">
                and g.name like %{groupName}%
            </if>
        </where>
    </select>
</mapper>
