<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.camera.mapper.LiveStreamDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="liveStreamDefinitionResultMap" type="com.bdtd.modules.camera.entity.LiveStreamDefinition">
        <result column="id" property="id"/> <!-- ID -->
        <result column="mine_code" property="mineCode"/> <!-- 煤矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 煤矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="title" property="title"/> <!-- 视频名称 -->
        <result column="install_pos" property="installPos"/> <!-- 摄像机安装位置 -->
        <result column="source_ip" property="sourceIp"/> <!-- 视频源IP -->
        <result column="sip" property="sip"/> <!-- SIP -->
        <result column="live_url" property="liveUrl"/> <!-- 播放地址 -->
        <result column="sort" property="sort"/> <!-- 排序 -->
        <result column="online_status" property="onlineStatus"/> <!-- 在线状态 -->
        <result column="online_status_text" property="onlineStatusText"/> <!-- 在线状态描述 -->
        <result column="memo" property="memo"/> <!-- 设备描述 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
        <result column="deleted_at" property="deletedAt"/> <!-- 删除时间 -->
    </resultMap>

    <select id="selectPagingBySystemId" resultMap="liveStreamDefinitionResultMap">
        select * from live_stream_definition
    </select>
</mapper>
