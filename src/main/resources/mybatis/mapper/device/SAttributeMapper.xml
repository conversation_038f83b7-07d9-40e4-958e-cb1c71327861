<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SAttributeMapper">
    <select id="selectPageByQuery" resultType="com.bdtd.modules.device.entity.SAttribute">
        select a.*,COALESCE(b.count,0) count from (
        select ca.*
        from s_attribute ca
        <where>
            1=1
            <if test="t.id!=null">
                and ca.id=#{t.id}
            </if>
            <if test="t.name!=null and t.name != ''">
                and ca.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.unit!=null and t.unit != ''">
                and ca.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.unitName!=null and t.unitName != ''">
                and ca.unit_name like concat('%',#{t.unitName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.attrTypeName!=null and t.attrTypeName != ''">
                and ca.attr_type_name like concat('%',#{t.attrTypeName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.monitorTypeInt!=null and t.monitorTypeInt != ''">
                and ca.monitor_type_int =#{t.monitorTypeInt}
            </if>
            <if test="t.flag!=null">
                and ca.flag =#{t.flag}
            </if>

        </where>
        order by ca.updated_at desc
        )a left join
        (
        SELECT count(1) as count,a.id FROM s_attribute a
        inner join s_mapping_model_attr m on a.id=m.attr_id
        inner join s_data_equipment mq on mq.model_id=m.model_id
        where a.id=m.attr_id GROUP BY a.id
        )b on a.id = b.id
    </select>

    <select id="selectPageByModelId" resultType="com.bdtd.modules.device.entity.SAttribute">
        SELECT s_attribute.* FROM
        (
        s_attribute INNER join s_mapping_model_attr on s_mapping_model_attr.attr_id =s_attribute.id
        )
        where s_mapping_model_attr.model_id=#{t.modelId}
        <if test="t.id!=null ">
            and s_attribute.id=t.id;
        </if>
        <if test="t.name!=null and t.name != ''">
            and s_attribute.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unit!=null  and t.unit != ''">
            and s_attribute.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.attrTypeName!=null  and t.attrTypeName != ''">
            and s_attribute.attr_type_name like concat('%',#{t.attrTypeName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.monitorTypeInt!=null  and t.monitorTypeInt != ''">
            and s_attribute.monitor_type_int =t.monitorTypeInt
        </if>
        order by s_attribute.id
    </select>

    <select id="selectQueryByModelId" resultType="com.bdtd.modules.device.entity.SAttribute">
        SELECT s_attribute.* FROM
        (
        s_attribute INNER join s_mapping_model_attr on s_mapping_model_attr.attr_id =s_attribute.id
        )
        where s_mapping_model_attr.model_id=#{t.modelId}
        <if test="t.id!=null">
            and s_attribute.id=t.id;
        </if>
        <if test="t.name!=null">
            and s_attribute.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unit!=null">
            and s_attribute.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unitName!=null and t.unitName != ''">
            and s_attribute.unit_name like concat('%',#{t.unitName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.attrTypeName!=null">
            and s_attribute.attr_type_name like concat('%',#{t.attrTypeName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.monitorTypeInt!=null">
            and s_attribute.monitor_type_int =t.monitorTypeInt
        </if>
        order by s_attribute.id
    </select>
    <select id="selectFilterPageByModelId" resultType="com.bdtd.modules.device.entity.SAttribute">
        select * from
        (
        SELECT s_attribute.* FROM
        (
          s_attribute INNER join s_mapping_model_attr on s_mapping_model_attr.attr_id =s_attribute.id
        )
        where s_mapping_model_attr.model_id=#{t.modelId}
        <if test="t.id!=null">
            and s_attribute.id=#{t.id};
        </if>
        <if test="t.name!=null and t.name != ''">
            and s_attribute.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unit!=null and t.unit != ''">
            and s_attribute.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unitName!=null and t.unitName != ''">
            and s_attribute.unit_name like concat('%',#{t.unitName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.attrTypeName!=null and t.attrTypeName != ''">
            and s_attribute.attr_type_name like concat('%',#{t.attrTypeName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.monitorTypeInt!=null and t.monitorTypeInt != ''">
            and s_attribute.monitor_type_int =#{t.monitorTypeInt}
        </if>
        order by s_attribute.id
        )c
        where c.id not in(
        select attr_id from s_mapping_model_attr where model_id = #{t.modelId}
        )
    </select>

    <select id="selectFilterQueryByModelId" resultType="com.bdtd.modules.device.entity.SAttribute">
        select * from
        (
        SELECT s_attribute.* FROM
        (
          s_attribute INNER join s_mapping_model_attr on s_mapping_model_attr.attr_id =s_attribute.id
        )
        where s_mapping_model_attr.model_id=#{t.modelId}
        <if test="t.id!=null">
            and s_attribute.id=#{t.id};
        </if>
        <if test="t.name!=null and t.name != ''">
            and s_attribute.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unit!=null and t.unit != ''">
            and s_attribute.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.unitName!=null and t.unitName != ''">
            and s_attribute.unit_name like concat('%',#{t.unitName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.attrTypeName!=null and t.attrTypeName != ''">
            and s_attribute.attr_type_name like concat('%',#{t.attrTypeName,jdbcType=VARCHAR}::text,'%')
        </if>
        <if test="t.monitorTypeInt!=null and t.monitorTypeInt != ''">
            and s_attribute.monitor_type_int =#{t.monitorTypeInt}
        </if>
        order by s_attribute.id
        )c
        where c.id not in(
        select attr_id from s_mapping_model_attr where model_id = #{t.modelId}
        )
    </select>
</mapper>
