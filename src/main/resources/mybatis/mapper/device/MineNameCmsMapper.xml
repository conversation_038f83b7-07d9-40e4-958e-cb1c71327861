<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.MineNameCmsMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.device.entity.MineNameCmsEntity">
    <id column="code" jdbcType="INTEGER" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="group_code" jdbcType="SMALLINT" property="groupCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    code, name, group_code
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_mine_name
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cms_mine_name
    where code = #{code,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from cms_mine_name
    where code = #{code,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntityExample">
    delete from cms_mine_name
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntity">
    insert into cms_mine_name (code, name, group_code
      )
    values (#{code,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{groupCode,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntity">
    insert into cms_mine_name
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="groupCode != null">
        group_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null">
        #{groupCode,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntityExample" resultType="java.lang.Long">
    select count(*) from cms_mine_name
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_mine_name
    <set>
      <if test="record.code != null">
        code = #{record.code,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.groupCode != null">
        group_code = #{record.groupCode,jdbcType=SMALLINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_mine_name
    set code = #{record.code,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      group_code = #{record.groupCode,jdbcType=SMALLINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntity">
    update cms_mine_name
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null">
        group_code = #{groupCode,jdbcType=SMALLINT},
      </if>
    </set>
    where code = #{code,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bdtd.modules.device.entity.MineNameCmsEntity">
    update cms_mine_name
    set name = #{name,jdbcType=VARCHAR},
      group_code = #{groupCode,jdbcType=SMALLINT}
    where code = #{code,jdbcType=INTEGER}
  </update>

    <select id="select" parameterType="String" resultType="com.bdtd.util.sql.MybatisMap">
        ${value}
    </select>
</mapper>