<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SDeviceModelTypeMapper">
    <select id="selectByQuery" resultType="com.bdtd.modules.device.entity.SDeviceModelType">
        select a.*,COALESCE(b.count,0) count from (
        select *
        from s_device_model_type
        <where>
            1=1
            <if test="t.id!=null">
                and id=#{t.id}
            </if>
            <if test="t.name!=null and t.name!='' ">
                and name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>

        </where>
        order by updated_at desc
        )a left join
        (
        select count(type_id),type_id  from s_device_model GROUP BY type_id
        )b on a.id = b.type_id
    </select>
</mapper>
