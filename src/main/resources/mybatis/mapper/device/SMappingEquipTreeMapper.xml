<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SMappingEquipTreeMapper">
    <select id="selectEquipPageByTreeId" parameterType="integer" resultType="com.bdtd.modules.device.entity.SDataEquipment">
    select e.* from s_data_equipment e left join s_mapping_equip_tree t on e.id=t.equip_id
        <where>
            t.tree_id =#{t.treeId}
            <if test="t.type !=null ">
                and e.type like concat('%',#{t.type,jdbcType=VARCHAR},'%')
            </if>
            <if test="t.modelName !=null ">
                and model_name like concat('%',#{t.modelName,jdbcType=VARCHAR},'%')
            </if>
            <if test="t.typeName !=null ">
                and g.type_name like concat('%',#{t.typeName,jdbcType=VARCHAR},'%')
            </if>
            <if test="t.name !=null">
                and t.name like concat('%',#{t.name,jdbcType=VARCHAR},'%')
            </if>
        </where>
</select>
</mapper>
