<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SAttributeTypeMapper">
    <select id="selectByQuery" resultType="com.bdtd.modules.device.entity.SAttributeType">
        select a.* ,COALESCE(b.count,0) count from (
        select cn.*
        from
        s_attribute_type cn
        <where>
            1=1
            <if test="t.id!=null ">
                and cn.id=#{t.id}
            </if>
            <if test="t.name!=null and t.name!='' ">
                and cn.name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.unitName!=null and t.unitName!='' ">
                and cn.unit_name like concat('%',#{t.unitName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.unit!=null and t.unit!='' ">
                and cn.unit like concat('%',#{t.unit,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.monitorTypeInt!=null ">
                and cn.monitor_type_int =#{t.monitorTypeInt}
            </if>
        </where>
        order by cn.updated_at desc
        )a
        left join (
        select count(attr_type_id),attr_type_id from s_attribute GROUP BY attr_type_id
        ) b on a.id=b.attr_type_id
    </select>
</mapper>
