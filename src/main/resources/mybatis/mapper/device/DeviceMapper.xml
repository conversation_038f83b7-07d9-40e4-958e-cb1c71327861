<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.DeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceResultMap" type="com.bdtd.modules.device.entity.Device">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="name"/>
        <result column="geometry_point" property="geometryPoint"/>
        <result column="geolocation_area_id" property="geolocationAreaId"/>
        <result column="system" property="system"/>
        <result column="tunnel_guid" property="tunnelGuid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="system_id" property="systemId"/>
    </resultMap>

    <select id="selectDevicePage" resultMap="deviceResultMap">
        select * from device
    </select>

</mapper>
