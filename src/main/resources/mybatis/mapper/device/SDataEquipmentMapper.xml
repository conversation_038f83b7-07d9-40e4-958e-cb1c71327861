<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SDataEquipmentMapper">
    <resultMap type="com.bdtd.modules.device.vo.SDataEquipmentAndAttrVo" id="resultMap">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="model_name" property="modelName"/>
        <result column="type_name" property="typeName"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="manufacturer_model" property="manufacturerModel"/>
        <result column="icon" property="icon"/>
        <result column="reserve" property="reserve"/>
        <result column="reserve" property="reserve"/>

        <collection property="sDataAttributes" ofType="com.bdtd.modules.device.vo.SDataAttributeRemoteVo">
            <id column="attr_id" property="id"/>
            <result column="attr_name" property="name"/>
            <result column="unit" property="unit"/>
            <result column="unit_name" property="unitName"/>
            <result column="monitor_type_int" property="monitorTypeInt"/>
            <result column="point_id" property="pointId"/>
            <result column="value" property="value"/>
            <result column="point_name" property="pointName"/>
            <result column="warn" property="warn"/>
        </collection>

    </resultMap>
    <resultMap type="com.bdtd.modules.device.vo.ChildEquipment" id="childEquipmentMap">
        <result column="name" property="name"/>
        <collection property="sDataAttributes" ofType="com.bdtd.modules.device.entity.SDataAttribute">
            <id column="id" property="id"/>
            <result column="attr_id" property="attrId"/>
            <result column="name" property="name"/>
            <result column="attr_name" property="attrName"/>
            <result column="attr_type_name" property="attrTypeName"/>
            <result column="attr_type_id" property="attrTypeId"/>
            <result column="equip_id" property="equipId"/>
            <result column="unit" property="unit"/>
            <result column="unit_id" property="unitId"/>
            <result column="unit_name" property="unitName"/>
            <result column="monitor_type_int" property="monitorTypeInt"/>
            <result column="point_id" property="pointId"/>
            <result column="value" property="value"/>
            <result column="point_name" property="pointName"/>
            <result column="warn" property="warn"/>
        </collection>

    </resultMap>

    <select id="selectByQuery" resultType="com.bdtd.modules.device.entity.SDataEquipment">
        select a.* ,COALESCE(b.count,0) count from (
        select * from s_data_equipment
        <where>
            1=1
            <if test="t.id!=null">
                and id=t.id;
            </if>
            <if test="t.name!=null">
                and name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.modelName!=null">
                and model_name like concat('%',#{t.modelName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.typeName!=null">
                and type_name like concat('%',#{t.typeName,jdbcType=VARCHAR}::text,'%')
            </if>
        </where>
        order by sort
        )
        a left join
        (
        select count(equip_id),equip_id from s_data_equipment c ,s_mapping_equip_tree eq
        where c.id=eq.equip_id GROUP BY equip_id
        ) b on a.id=b.equip_id
    </select>

    <select id="selectByTreeId" resultType="com.bdtd.modules.device.entity.SDataEquipment">
        select a.* ,COALESCE(b.count,0) count from (
        select e.* from s_data_equipment e left join s_mapping_equip_tree m on e.id=m.equip_id
        <where>
            m.tree_id=#{t.treeId}
            <if test="t.id!=null">
                and id=t.id;
            </if>
            <if test="t.name!=null and t.name != ''">
                and name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.modelName!=null and t.modelName != ''">
                and model_name like concat('%',#{t.modelName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.typeName!=null and t.typeName != ''">
                and type_name like concat('%',#{t.typeName,jdbcType=VARCHAR}::text,'%')
            </if>
        </where>
        order by sort
        )
        a left join
        (
        select count(equip_id),equip_id from s_data_equipment c ,s_mapping_model_equment eq
        where c.id=eq.equip_id GROUP BY equip_id
        ) b on a.id=b.equip_id
    </select>

    <select id="selectFilterPageByTreeId" resultType="com.bdtd.modules.device.entity.SDataEquipment">
        select a.* from (
        select e.* from s_data_equipment e
        <where>
            <if test="t.id!=null">
                and id=t.id;
            </if>
            <if test="t.name!=null and t.name != ''">
                and name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.modelName!=null and t.modelName != ''">
                and model_name like concat('%',#{t.modelName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.typeName!=null and t.typeName != ''">
                and type_name like concat('%',#{t.typeName,jdbcType=VARCHAR}::text,'%')
            </if>
        </where>
        order by sort
        ) a where a.id not in(
        select equip_id from s_mapping_equip_tree where tree_id = #{t.treeId}
        )
    </select>
    <select id="selectEquipAndAttrBySturctureIds" resultMap="resultMap">
        select e.*,da.id as attr_id,da.name attr_name ,da.unit,da.warn,da.point_id,da.monitor_type_int,v.name from (
        select id,name,structure_id,manufacturer,model_name,manufacturer_model,p_id,reserve from s_data_equipment
        <where>
            structure_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
        ) e
        left join s_data_attribute da on e.id=da.equip_id left join point_view v on da.point_id =v.point_id
    </select>


    <select id="selectEquipAndAttrByLibrayIds" resultMap="resultMap">
        select e.*,da.id as attr_id,da.name attr_name ,da.unit,da.warn,da.point_id,da.monitor_type_int,v.name from (
        select id,name,structure_id,manufacturer,model_name,manufacturer_model,p_id,reserve from s_data_equipment
        <where>
            library_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
        ) e
        left join s_data_attribute da on e.id=da.equip_id left join point_view v on da.point_id =v.point_id
    </select>

    <select id="selectEquipAndAttrBySturctureId" parameterType="string" resultMap="resultMap">
        select e.*,da.id as attr_id,da.name attr_name ,da.unit,da.warn,da.point_id,da.monitor_type_int,v.name,v.value from (
        select id,name,structure_id,manufacturer,model_name,manufacturer_model,p_id,reserve from s_data_equipment
        WHERE structure_id=#{id}
        ) e
        left join s_data_attribute da on e.id=da.equip_id left join point_view v on da.point_id =v.point_id
    </select>

    <select id="getEquipmentAndChildByPid" parameterType="integer" resultMap="childEquipmentMap">
        SELECT e.name,a.* from
        s_data_equipment e left join s_data_attribute a on e.id=a.equip_id
        where e.p_id=#{pid,jdbcType=INTEGER}
        ORDER BY e.id
    </select>
</mapper>
