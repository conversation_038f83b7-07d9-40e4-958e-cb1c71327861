<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SDeviceModelMapper">
    <select id="selectByQuery" resultType="com.bdtd.modules.device.entity.SDeviceModel">
        select a.* ,COALESCE(b.count,0) count from (
        select * from s_device_model
        <where>
            1=1
            <if test="t.id!=null">
                and id=t.id
            </if>

            <if test="t.name!=null and t.name != '' ">
                and name like concat('%',#{t.name,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.typeName!=null and t.typeName !='' ">
                and type_name like concat('%',#{t.typeName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="t.type!=null and t.type !='' ">
                and type like concat('%',#{t.type,jdbcType=VARCHAR}::text,'%')
            </if>
        </where>
        order by sort
        )
        a left join
        (
        select count(*),ma.model_id from s_device_model d,s_mapping_model_attr ma,s_attribute a where d.id=ma.model_id
        and ma.attr_id=a.id group by ma.model_id
        ) b on a.id=b.model_id
    </select>
</mapper>
