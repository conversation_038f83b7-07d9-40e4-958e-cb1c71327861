<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.DeviceModelConfMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.device.entity.DeviceModelConf">
    <result column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="business_code" jdbcType="VARCHAR" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="group_name" jdbcType="VARCHAR" property="groupCode" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
     point_id, attribute_id, device_id, business_id, company_code, group_code, created_at,
    updated_at
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.device.dto.DeviceModelConfExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_device_model_conf
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_device_model_conf
    where point_id = #{pointId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPointId" parameterType="java.lang.String">
    delete from data_device_model_conf
    where point_id = #{pointId,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.bdtd.modules.device.entity.DeviceModelConf">
    insert into data_device_model_conf ( point_id, attribute_id,
      device_id, business_id, company_code,
      group_code
      )
    values ( #{pointId,jdbcType=VARCHAR}, #{attributeId,jdbcType=VARCHAR},
      #{deviceId,jdbcType=VARCHAR}, #{businessId,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
      #{groupCode,jdbcType=VARCHAR}
      )
  </insert>



  <update id="updateByExampleSelective" parameterType="map">
    update data_device_model_conf
    <set>
      <if test="record.pointId != null">
        point_id = #{record.pointId,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeId != null">
        attribute_id = #{record.attributeId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.groupCode != null">
        group_code = #{record.groupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

</mapper>