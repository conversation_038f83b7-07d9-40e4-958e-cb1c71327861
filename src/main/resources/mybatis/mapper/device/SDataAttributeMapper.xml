<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.device.mapper.SDataAttributeMapper">

    <insert id="insertSdataAttr">
        INSERT INTO s_data_attribute
        (
        id,name,attr_type_id,unit,monitor_type_int,
        attr_type_name,point_id,equip_id,warn
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
               #{id}, #{item.name},#{item.attrTypeId},#{item.unit},#{item.monitorTypeInt},
                #{item.attrTypeName},#{item.pointId},#{item.equipId},#{item.warn}
            </trim>
        </foreach>
        on conflict(id) do update set
        name=excluded.name,
        attr_type_id=excluded.attr_type_id,
        unit=excluded.unit,
        monitor_type_int=excluded.monitor_type_int,
        attr_type_name=excluded.attr_type_name,
        point_id=excluded.point_id,
        equip_id=excluded.equip_id,
        warn=excluded.warn
    </insert>
    <insert id="insertBatch">
        INSERT INTO s_data_attribute
        (
        name,attr_type_id,unit,monitor_type_int,
        attr_type_name,point_id,equip_id,warn
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.name},#{item.attrTypeId},#{item.unit},#{item.monitorTypeInt},
                #{item.attrTypeName},#{item.pointId},#{item.equipId},#{item.warn}
            </trim>
        </foreach>
        on conflict(id) do update set
        name=excluded.name,
        attr_type_id=excluded.attr_type_id,
        unit=excluded.unit,
        monitor_type_int=excluded.monitor_type_int,
        attr_type_name=excluded.attr_type_name,
        point_id=excluded.point_id,
        equip_id=excluded.equip_id,
        warn=excluded.warn
    </insert>
    <select id="selectListWithPoint" parameterType="integer" resultType="com.bdtd.modules.device.entity.SDataAttribute">
        select a.*,p.name as point_name
        from s_data_attribute a left join point_view p on a.point_id = p.point_id
        where equip_id=#{equipId}
    </select>

    <select id="getRateOfUtilization" parameterType="string" resultType="com.bdtd.modules.device.entity.SDataAttribute">
        select
            id,
            name,
            attr_type_id,
            unit,
            monitor_type_int,
            equip_id,
            point_id
        from
            s_data_attribute sda
        where
            attr_type_id = #{attrTypeId}
    </select>
</mapper>
