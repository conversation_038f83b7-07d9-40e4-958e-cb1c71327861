<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.third.mapper.YknyUploadDataMapper">
    <select id="getYknySafetyRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            sd.point_id as SensorID,
            concat(sd.location, ' ', sd.sensor_type_name) as SensorName,
            sd.sensor_type_name as SensorType,
            round(cast(sr.value as numeric), 2) as MonitorValue,
            sd.unit_name as Unit,
            sr.alarm_status_name as Status,
            to_char(sr.data_time, 'YYYY-MM-DD HH24:MI:SS') as SensorTime
        from
            safety_realtime sr
                join safety_definition sd on
                sd.point_id = sr.point_id
        where sd.deleted_at is null
            and sr.data_time <![CDATA[ >= ]]>  (SELECT now()::timestamp + '-5 min')
    </select>

    <select id="getYknySafetyAlarmList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            sd.point_id as SensorID,
            concat(sd.location, ' ', sd.sensor_type_name) as SensorName,
            sd.sensor_type_name as SensorType,
            round(cast(sr.value as numeric), 2) as MonitorValue,
            sd.unit_name as Unit,
            sar.alarm_type_name as WarnType,
            to_char(sr.data_time, 'YYYY-MM-DD HH24:MI:SS') as SensorTime,
            to_char(sar.begin_time, 'YYYY-MM-DD HH24:MI:SS') as STime,
            case when sar.end_time is null or sar.end_time &lt; sar.begin_time then null else to_char(sar.end_time, 'YYYY-MM-DD HH24:MI:SS') end as ETime,
            round(cast(sar.max_value as numeric), 2) as maxvalue,
            case when sar.max_time is null then null else to_char(sar.max_time, 'YYYY-MM-DD HH24:MI:SS') end as MaxValueTime,
            round(cast(sar.min_value as numeric), 2) as minvalue,
            case when sar.min_time is null then null else to_char(sar.min_time, 'YYYY-MM-DD HH24:MI:SS') end as MinValueTime,
            round(cast(sar.avg_value as numeric), 2) as AvgValue
        from
            safety_alarm_realtime sar
                join safety_definition sd
                     on sd.point_id = sar.point_id
                join safety_realtime sr
                     on sd.point_id = sr.point_id
        where sd.deleted_at is null
    </select>

    <select id="getYknyPositionRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            pmc.well_count as PersonCountInWell,
            pe.card_id as CardID,
            pe.card_id as PersonID,
            pe.person_name as PersonName,
            pe.job as Duty,
            pe.department as Department,
            null::varchar as WorkPlace,
            to_char(pr.down_time, 'YYYY-MM-DD HH24:MI:SS') as TimeInWell,
            case when pr.up_time is null or pr.up_time &lt; pr.down_time then null else to_char(pr.up_time, 'YYYY-MM-DD HH24:MI:SS') end as TimeOutWell,
            pr.station_code as StationID,
            pr.station_name as StationName,
            pac.area_count as PersonCountInStation,
            case when pr.in_station_time is null then null else to_char(pr.in_station_time, 'YYYY-MM-DD HH24:MI:SS') end as TimeInStation,
            to_char(pr.data_time, 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            position_realtime pr
                join position_employee pe on pr.card_id  = pe.card_id
                join (select count(1) as well_count from position_realtime where ry_well_code = '1') pmc on 1 = 1
                join (select area_code, count(1) as area_count from position_realtime where ry_well_code = '1' group by area_code) pac on pr.area_code = pac.area_code
    </select>

    <select id="getYknyPositionOvertimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            to_char(ptr.data_time, 'YYYY-MM-DD HH24:MI:SS') as SnapTime,
            pe.card_id as CardID,
            pe.card_id as PersonID,
            pe.person_name as PersonName,
            pe.job as Duty,
            pe.department as Department,
            null::varchar as WorkPlace,
            to_char(ptr.down_time, 'YYYY-MM-DD HH24:MI:SS') as TimeInWell,
            null::timestamp as TimeOutWell,
            to_char(ptr.begin_time, 'YYYY-MM-DD HH24:MI:SS') as AlarmStartTime,
            case when ptr.end_time is null or ptr.end_time &lt; ptr.begin_time then null else to_char(ptr.end_time, 'YYYY-MM-DD HH24:MI:SS') end as AlarmEndTime,
            ptr.station_code as StationID,
            case when ptr.enter_station_time is null then null else to_char(ptr.enter_station_time, 'YYYY-MM-DD HH24:MI:SS') end as TimeInStation,
            ptr.station_name as StationName
        from
            position_timeout_realtime ptr
                join position_employee pe on ptr.card_id  = pe.card_id
    </select>

    <select id="getYknyPositionPersonLimitList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            to_char(por.data_time, 'YYYY-MM-DD HH24:MI:SS') as SnapTime,
            por.type as OverPersonType,
            por.personnel_quota as LimitPersonCount,
            por.current_person_total as PersonCount,
            por.area_person as AreaPerson,
            to_char(por.begin_time, 'YYYY-MM-DD HH24:MI:SS') as AlarmStartTime,
            case when por.end_time is null or por.end_time &lt; por.begin_time then null else to_char(por.end_time, 'YYYY-MM-DD HH24:MI:SS') end as AlarmEndTime,
            por.area_code as StationID,
            por.area_name as StationName,
            psc.area_count as PersonCountInStation
        from
            position_overman_realtime por
                join (select pr.area_code, count(1) as area_count from position_realtime pr where pr.ry_well_code = '1' group by pr.area_code) psc
                    on por.area_code = psc.area_code
        order by por.begin_time desc
    </select>

    <select id="getYknyHydrologyWaterList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            wod.point_id as StationID,
            wod.name as StationName,
            wod.area_name as Location,
            round(cast(wor.value as numeric), 2) as MonitorValue,
            wod.unit as Unit,
            wod."type" as StationType,
            case coalesce(wor.status, wor.state)
                when '0' then '正常'
                when 'good' then '正常'
                else coalesce(wor.status, wor.state)
                end as Status,
            to_char(wor."timestamp", 'YYYY-MM-DD HH24:MI:SS') as MonitorTime
        from
            water_observation_realtime wor
                join water_observation_definition wod on wor.point_id = wod.point_id
    </select>

    <select id="getYknyTubeRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            tmd.point_id as StationID,
            case
                when tmd.point_name is null then concat(tmd.area_name, ' ', tmd.sensor_type)
                else tmd.point_name
            end as StationName,
            tmd.sensor_type as StationType,
            tmd.area_name as AreaName,
            null::float8 as O2,
            null::float8 as N2,
            null::float8 as CH4,
            null::float8 as CO2,
            null::float8 as CO,
            null::float8 as C2H4,
            null::float8 as C2H6,
            null::float8 as C2H2,
            null::float8 as C3H8,
            null::float8 as WXB,
            tmr.value as Value,
            tmr.status as Analyst,
            to_char(tmr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as AnalystDate,
            tmr.state as Remark
        from
            tube_monitor_realtime tmr
                join tube_monitor_definition tmd on tmr.point_id = tmd.point_id
        order by tmd.point_id
    </select>

    <select id="getYknyPressureRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            bsd.monitoring_area_name as MonitorArea,
            1::int as StationType,
            bsd.point_id as StationID,
            bsd.sensor_type as StationName,
            msr.channel_no as Insmode,
            msr.monitor_value as MonitorValue,
            bsd.unit as Unit,
            bsd.monitoring_direction as MonitorDirection,
            msr.state as Status,
            msr.state as StatusDescribe,
            null::varchar as StatusParameter,
            null::varchar as StatusRecord,
            to_char(msr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            mine_stress_realtime msr
                join bracket_stress_definition bsd on msr.point_id = bsd.point_id
        union
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            rsd.monitoring_area_name as MonitorArea,
            2::int as StationType,
            rsd.point_id as StationID,
            rsd.sensor_type as StationName,
            msr.channel_no as Insmode,
            msr.monitor_value as MonitorValue,
            rsd.unit as Unit,
            rsd.monitoring_direction as MonitorDirection,
            msr.state as Status,
            msr.state as StatusDescribe,
            null::varchar as StatusParameter,
            null::varchar as StatusRecord,
            to_char(msr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            mine_stress_realtime msr
                join roof_stress_definition rsd on msr.point_id = rsd.point_id
        union
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            tdsd.monitoring_area_name as MonitorArea,
            3::int as StationType,
            tdsd.point_id as StationID,
            tdsd.sensor_type as StationName,
            msr.channel_no as Insmode,
            msr.monitor_value as MonitorValue,
            tdsd.unit as Unit,
            tdsd.monitoring_direction as MonitorDirection,
            msr.state as Status,
            msr.state as StatusDescribe,
            null::varchar as StatusParameter,
            null::varchar as StatusRecord,
            to_char(msr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            mine_stress_realtime msr
                join tunnel_distance_stress_definition tdsd on msr.point_id = tdsd.point_id
        union
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            asd.monitoring_area_name as MonitorArea,
            4::int as StationType,
            asd.point_id as StationID,
            asd.sensor_type as StationName,
            msr.channel_no as Insmode,
            msr.monitor_value as MonitorValue,
            asd.unit as Unit,
            asd.monitoring_direction as MonitorDirection,
            msr.state as Status,
            msr.state as StatusDescribe,
            null::varchar as StatusParameter,
            null::varchar as StatusRecord,
            to_char(msr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            mine_stress_realtime msr
                join anchor_stress_definition asd on msr.point_id = asd.point_id
    </select>

    <select id="getYknyMicroseismRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            mmr.id::varchar as ID,
            to_char(mmr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime,
            mmr.area_name as AREANAME,
            mmr.channel_count::varchar as CHCOUNT,
            mmr.sample_length::varchar as SAMPLENGTH,
            mmr.sample_req::varchar as SAMPFREQ,
            mmr.sensor_dir as SERSORDIR,
            mmr.install_mode as INSTALLWAY,
            mmr.sensitivity::varchar as SENSITIVITY,
            mmr.sensor_type as SENSORTYPE,
            mmr.centrum_x::varchar as X,
            mmr.centrum_y::varchar as Y,
            mmr.centrum_z::varchar as Z,
            mmr.energy::varchar as ENERGY,
            mmr.level::varchar as level,
            mmr.pos_desc as POSDESC,
            mmr.max_swing::varchar as MAXSWING,
            mmr.avg_swing::varchar as AVGSWING,
            mmr.basic_freq::varchar as BASICFREQ,
            mmr.trig_channel as TRIGCH,
            mmr.state_text as EXCSTATUS,
            mmr.memo as MEMO
        from
            mine_microseism_realtime mmr
        where 1 = 1
            <if test="startTime != null">
                and mmr."timestamp" &gt;= #{startTime, jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and mmr."timestamp" &lt; #{endTime, jdbcType=TIMESTAMP}
            </if>
        union
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            ssr.event_id as ID,
            to_char(ssr.data_time, 'YYYY-MM-DD HH24:MI:SS') as SnapTime,
            null::varchar as AREANAME,
            ssr.passageway_amount as CHCOUNT,
            null::varchar as SAMPLENGTH,
            null::varchar as SAMPFREQ,
            null::varchar as SERSORDIR,
            null::varchar as INSTALLWAY,
            null::varchar as SENSITIVITY,
            null::varchar as SENSORTYPE,
            ssr.x as X,
            ssr.y as Y,
            ssr.z as Z,
            ssr.energy as ENERGY,
            ssr.level as level,
            null::varchar as POSDESC,
            ssr.max_amplitude as MAXSWING,
            ssr.avg_amplitude as AVGSWING,
            ssr.dominant_frequency as BASICFREQ,
            null::varchar as TRIGCH,
            ssr.analysis_results as EXCSTATUS,
            ssr.analysis_results as MEMO
        from
            slight_shock_realtime ssr
        where 1 = 1
            <if test="startTime != null">
                and ssr."data_time" &gt;= #{startTime, jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and ssr."data_time" &lt; #{endTime, jdbcType=TIMESTAMP}
            </if>
    </select>

    <select id="getYknyTransportRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            atr.car_number_name as CarNumName,
            atr.location_number as LocationNumber,
            atd.sip_number as SipNumber,
            atr.driver_id as DriverID,
            atr.driver_name as DriverName,
            atr.x as x,
            atr.y as y,
            null::int as PeopleLocationNumbers,
            to_char(atr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime
        from
            auxiliary_transport_realtime atr
                join auxiliary_transport_definition atd on atr.location_number = atd.location_number
    </select>

    <select id="getYknyHoleStressRealtimeList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            hsd.point_id as StationID,
            hsd.point_name as StationNAME,
            hsd.monitoring_area_name as AREANAME,
            hsd.station_type as AREATYPE,
            hsd.tunnel_name as TUNNELNAME,
            to_char(msr."timestamp", 'YYYY-MM-DD HH24:MI:SS') as SnapTime,
            msr.monitor_value as STRESSVALUE,
            null::varchar as WarnParam,
            null::varchar as WarnRecord,
            hsd.description as EXCSTATUS
        from
            mine_stress_realtime msr
                join hole_stress_definition hsd on msr.point_id = hsd.point_id
    </select>


    <select id="getYknyModelSystemAliveStatisticsRealtimeList" resultType="java.util.Map">
        select *
        from
            (
                select
                    row_number() over (
                        partition by msas.system_id
                        order by msas.disconnection_time desc
                    ) rowid,
                    #{mineId, jdbcType=VARCHAR} as MineID,
                    #{mineName, jdbcType=VARCHAR} as MineName,
                    msas.id as Sort,
                    '综合监测' as SystemName,
                    msas.system_name as MonitorName,
                    case
                        when msas.recovery_time is not null then now()
                        else msas.disconnection_time
                    end as MonitorTime,
                    msa.time_limit as LimitMinutes,
                    case
                        when msas.recovery_time is not null then '正常'
                        else '断线'
                    end as status,
                    case
                        when ms.exchange  = 'ex_automation' then '综合自动化'
                        else '综合监测'
                    end as SystemName,
                    'Y' as  IsEnable
                from model_system_alive_statistics msas
                    left join model_system_alive msa on msas.system_id  = msa.system_id
                    left join model_system ms on msas.system_id  = ms.id
                where disconnection_time is not null
            ) temp
        where rowid = 1
    </select>

    <select id="getYknyModelSystemAliveStatisticsHistoryList" resultType="java.util.Map">
        select
            #{mineId, jdbcType=VARCHAR} as MineID,
            #{mineName, jdbcType=VARCHAR} as MineName,
            msas.id as Sort,
            '综合监测' as SystemName,
            msas.system_name as MonitorName,
            msas.disconnection_time as MonitorTime,
            msa.time_limit as LimitMinutes,
            case
                when msas.recovery_time is not null then '正常'
                else '断线'
            end as status,
            case
                when ms.exchange  = 'ex_automation' then '综合自动化'
                else '综合监测'
            end as SystemName,
            'Y' as  IsEnable
        from model_system_alive_statistics msas
            left join model_system_alive msa on msas.system_id  = msa.system_id
            left join model_system ms on msas.system_id  = ms.id
        where 1 = 1
        <if test="startTime != null">
            and msas.disconnection_time &gt;= #{startTime, jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and msas.disconnection_time &lt; #{endTime, jdbcType=TIMESTAMP}
        </if>
    </select>
</mapper>
