<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.automation.dao.DataAutomationDefinitionMapper">

    <select id="listWithValue" resultType="com.bdtd.modules.automation.entity.DataAutomationDefinition">
        select
            d.point_id,
            d.name,
            d.data_type,
            d.system_id,
            d.point_type,
            d.company_code,
            d.group_code,
            d.type,
            r.state,
            r.value,
            r.timestamp,
            d.created_at,
            d.updated_at
        from data_automation_definition d
            left join data_automation_realtime r on r.point_id = d.point_id
        <where>
            <if test="pointId != null and pointId != ''">
                and d.point_id = #{pointId}
            </if>
            <if test="systemId != null and systemId != ''">
                and d.system_id = #{systemId}
            </if>
            <if test="pointName != null and pointName != ''">
                and d.name like '%'||#{pointName}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
            <if test="startTime != null">
                and r.timestamp <![CDATA[ >= ]]>  #{startTime}
            </if>
            <if test="endTime != null">
                and r.timestamp <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
        order by r.value is null, r.timestamp desc
    </select>

    <select id="getWithValue" resultType="com.bdtd.modules.automation.entity.DataAutomationDefinition">
        select
            d.point_id,
            d.name,
            d.data_type,
            d.system_id,
            d.point_type,
            d.company_code,
            d.group_code,
            d.type,
            r.state,
            r.value,
            d.created_at,
            d.updated_at,
            r.timestamp
        from data_automation_definition d
            left join data_automation_realtime r on r.point_id = d.point_id
        <where>
            <if test="pointId != null and pointId != ''">
                and d.point_id = #{pointId}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
            <if test="startTime != null">
                and r.timestamp <![CDATA[ >= ]]>  #{startTime}
            </if>
            <if test="endTime != null">
                and r.timestamp <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
        order by r.timestamp desc
        limit 1
    </select>

    <select id="getDetail" resultType="com.bdtd.modules.automation.entity.DataAutomationDefinition">
        select
            d.point_id,
            d.name,
            d.data_type,
            d.system_id,
            d.point_type,
            d.company_code,
            d.group_code,
            d.type,
            d.created_at,
            d.updated_at
        from data_automation_definition d
        <where>
            <if test="pointId != null and pointId != ''">
                and d.point_id = #{pointId}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
        </where>
        limit 1
    </select>

    <select id="getPage" resultType="java.util.Map">
        select
            d.point_id,
            d.name,
            d.data_type,
            d.system_id,
            r.state,
            r.value,
            r.timestamp
        from data_automation_definition d
            left join data_automation_realtime r on r.point_id = d.point_id
        <where>
            <if test="entity.pointId != null and entity.pointId != ''">
                and d.point_id = #{entity.pointId}
            </if>
            <if test="entity.systemId != null and entity.systemId != ''">
                and d.system_id = #{entity.systemId}
            </if>
            <if test="entity.pointName != null and entity.pointName != ''">
                and d.name like '%'||#{entity.pointName}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
        </where>
        order by r.value is null, r.timestamp desc
    </select>
</mapper>
