<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.automation.dao.DataAutomationRealtimeMapper">

    <select id="selectListByPointId" resultType="com.bdtd.modules.automation.entity.DataAutomationRealtime">
        select r.point_id,
               r.value,
               d.name as point_name
        from data_automation_realtime r
                 left join data_automation_definition d on d.point_id = r.point_id
        where r.point_id = #{pointId}
    </select>

    <select id="selectByPointId" resultType="com.bdtd.modules.automation.entity.DataAutomationRealtime">
        select r.point_id,
               r.value,
               d.name as point_name
        from data_automation_realtime r
                 left join data_automation_definition d on d.point_id = r.point_id
        where r.point_id = #{pointId}
    </select>

    <select id="selectListWithName" resultType="com.bdtd.modules.automation.entity.DataAutomationRealtime">
        select
            r.point_id,
            r.value,
            d.name as point_name
        from data_automation_realtime r
            left join data_automation_definition d on d.point_id = r.point_id
        where 1=1
        <if test="arr != null and arr != ''">
            and r.point_id in
            <foreach collection="arr" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
        </if>
    </select>

    <select id="listWithPointId" resultType="com.bdtd.modules.automation.entity.DataAutomationRealtime">
        select
            r.point_id,
            r.value,
            d.name as point_name
        from data_automation_realtime r
            left join data_automation_definition d on d.point_id = r.point_id
        where 1=1
        <if test="alarmPointList != null and alarmPointList.size > 0">
            and r.point_id in
            <foreach collection="alarmPointList" item="item" index="i" open="(" separator="," close=")">#{item}
            </foreach>
        </if>
    </select>
</mapper>
