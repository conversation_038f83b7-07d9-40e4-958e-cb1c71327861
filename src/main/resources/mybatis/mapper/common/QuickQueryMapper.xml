<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.common.mapper.QuickQueryMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.common.entity.QuickQuery">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, point_id, created_at, updated_at,system_id,system_name,device_id,device_name
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.common.dto.QuickQueryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_quick_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_quick_query
    where id = #{id,jdbcType=INTEGER}
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from data_quick_query
    where id = #{id,jdbcType=INTEGER}
  </delete>





  <delete id="deleteByExample" parameterType="com.bdtd.modules.common.dto.QuickQueryExample">
    delete from data_quick_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>


  <insert id="insert" parameterType="com.bdtd.modules.common.entity.QuickQuery">
    insert into data_quick_query (type, point_id,
      created_at, updated_at,system_id,device_id,device_name,system_name)
    values (#{type,jdbcType=VARCHAR}, #{pointId,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{systemId,jdbcType=VARCHAR}
      , #{deviceId,jdbcType=VARCHAR}, #{deviceName,jdbcType=VARCHAR}, #{systemName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bdtd.modules.common.entity.QuickQuery">
    insert into data_quick_query
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="pointId != null">
        point_id,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceName != null">
        device_name,
      </if>
      <if test="systemName != null">
        system_name,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="pointId != null">
        #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null">
        #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        #{systemName,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bdtd.modules.common.dto.QuickQueryExample" resultType="java.lang.Long">
    select count(*) from data_quick_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_quick_query
    <set>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.pointId != null">
        point_id = #{record.pointId,jdbcType=VARCHAR},
      </if>
      <if test="record.systemId != null">
        system_id = #{record.systemId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceName != null">
        device_name = #{record.deviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.systemName != null">
        system_name = #{record.systemName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_quick_query
    set
      type = #{record.type,jdbcType=VARCHAR},
      point_id = #{record.pointId,jdbcType=VARCHAR},
      system_id = #{record.systemId,jdbcType=VARCHAR},
      device_id = #{record.deviceId,jdbcType=VARCHAR},
      device_name = #{record.deviceName,jdbcType=VARCHAR},
      system_name = #{record.systemName,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>



  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.common.entity.QuickQuery">
    update data_quick_query
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="pointId != null">
        point_id = #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null">
        device_name = #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        system_name = #{systemName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>