<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.common.mapper.StatusModelMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.common.entity.StatusModel">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap id="PointModelWarmingResultMap" type="com.bdtd.modules.common.dto.PointModelWarning">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="systemId" jdbcType="VARCHAR" property="systemId" />
    <result column="businessId" jdbcType="VARCHAR" property="businessId" />
    <result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
    <result column="attributeId" jdbcType="VARCHAR" property="attributeId" />
    <result column="systemName" jdbcType="VARCHAR" property="systemName" />
    <result column="businessName" jdbcType="VARCHAR" property="businessName" />
    <result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
    <result column="attributeName" jdbcType="VARCHAR" property="attributeName" />
    <result column="attributeTypeId" jdbcType="VARCHAR" property="attributeTypeId" />
    <result column="attributeTypeName" jdbcType="VARCHAR" property="attributeTypeName" />
    <result column="attributeUnit" jdbcType="VARCHAR" property="attributeUnit" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, note, point_id, value, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.common.dto.StatusModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from data_status
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.bdtd.modules.common.entity.StatusModel">
    insert into data_status (id, note, point_id, value)
    values (#{id,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, #{pointId,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR}
      )
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.common.entity.StatusModel">
    update data_status
    <set>
      <if test="note != null ">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="pointId != null ">
        point_id = #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="value != null ">
        value = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectPage"  parameterType="map" resultMap="PointModelWarmingResultMap">
    SELECT
    dp.id,
    dp.address,
    dp.label,
    dp.created_at AS createTime,
    dp.updated_at AS updateTime,
    dp.code,
    dp.system_id AS systemId,
    ms.NAME AS systemName,
    dp.business_id AS businessId,
    db.NAME AS businessName,
    dp.device_id AS deviceId,
    dd.NAME AS deviceName,
    dp.attribute_id AS attributeId,
    da.NAME AS attributeName ,
    mat.NAME AS attributeTypeName,
    mat.unit AS attributeUnit,
    mat.id AS attributeTypeId
    FROM (
    SELECT
    dw.point_id,
    point.id,
    point.address,
    point.label,
    point.created_at,
    point.updated_at,
    point.code,
    point.system_id,
    point.business_id,
    point.device_id,
    point.attribute_id
    FROM
    ( SELECT a.point_id FROM data_warming a GROUP BY a.point_id ) dw
    LEFT JOIN data_point point ON dw.point_id = point.CODE
    WHERE point.CODE IS NOT NULL
    <if test="pointId != null " >
      and point_id = #{pointId,jdbcType=VARCHAR}
    </if>
    <if test="address != null  ">
      and address = #{address,jdbcType=VARCHAR}
    </if>
    ) dp
    LEFT JOIN model_system ms ON dp.system_id = ms.id
    LEFT JOIN data_business db ON dp.business_id = db.id
    LEFT JOIN data_device dd ON dp.device_id = dd.id
    LEFT JOIN data_attribute da ON dp.attribute_id = da.id
    LEFT JOIN model_attribute_type mat ON mat.id = da.type_id order by dp.created_at desc
  </select>
</mapper>