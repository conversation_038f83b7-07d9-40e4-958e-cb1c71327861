<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.common.mapper.TreeMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.util.entity.Tree">
    <result column="parent" jdbcType="VARCHAR" property="parent" />
    <result column="child" jdbcType="VARCHAR" property="child" />
  </resultMap>

  <select id="select" parameterType="String" resultMap="BaseResultMap">
    select parent,child from tree
    <if test="child != null ">
      where child = #{child,jdbcType=VARCHAR}
    </if>
  </select>

</mapper>