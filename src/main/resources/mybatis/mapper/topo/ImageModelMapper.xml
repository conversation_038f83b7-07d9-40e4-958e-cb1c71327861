<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.topo.dao.ImageModelMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.topo.entity.ImageModel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fault_image" jdbcType="VARCHAR" property="faultImage" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="run_image" jdbcType="VARCHAR" property="runImage" />
    <result column="stop_image" jdbcType="VARCHAR" property="stopImage" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, fault_image, name, run_image, stop_image, type, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.topo.entity.ImageModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from model_image
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from model_image
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <!--  记得加上useGeneratedKeys和keyProperty配置即可，前者是指设置是否使用jdbc的getGenereatedKeys方法获取主
  键并赋值到keyProperty设置的属性中，后者即实体类主键字段（并且大小写要对应上）-->
  <insert id="insertReturnId" useGeneratedKeys="true" keyProperty="id" parameterType="com.bdtd.modules.topo.entity.ImageModel">
    insert into model_image (fault_image, name,
      run_image, stop_image, type)
    values (#{faultImage,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{runImage,jdbcType=VARCHAR}, #{stopImage,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR})
  </insert>


  <update id="updateByPrimaryKeySelective"  parameterType="com.bdtd.modules.topo.entity.ImageModel">
    update model_image
    <set>
      <if test="faultImage != null ">
        fault_image = #{faultImage,jdbcType=VARCHAR},
      </if>
      <if test="name != null ">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="runImage != null ">
        run_image = #{runImage,jdbcType=VARCHAR},
      </if>
      <if test="stopImage != null ">
        stop_image = #{stopImage,jdbcType=VARCHAR},
      </if>
      <if test=" type != null ">
        type = #{type,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>



  <delete id="deleteBatch" >
    delete from model_image where id in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")" >
      #{item}
    </foreach>
  </delete>

  <select id="selectBatch" resultMap="BaseResultMap" >
    select * from model_image where id in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")" >
      #{item}
    </foreach>
  </select>


</mapper>