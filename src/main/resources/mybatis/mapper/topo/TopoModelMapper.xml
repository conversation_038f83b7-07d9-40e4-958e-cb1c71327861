<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.topo.dao.TopoModelMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.topo.entity.TopoModel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="value" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="value" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="flag" jdbcType="INTEGER" property="flag" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="ref_url" jdbcType="VARCHAR" property="refUrl" />
  </resultMap>
  <resultMap id="BaseResultMapNCVC" type="com.bdtd.modules.topo.entity.TopoModelNCVC">
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="ref_url" jdbcType="VARCHAR" property="refUrl" />
  </resultMap>
  <resultMap id="BaseResultMapNVC" type="com.bdtd.modules.topo.entity.TopoModelNVC">
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="value" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="value" />
    <result column="ref_url" jdbcType="VARCHAR" property="refUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, code, value, version, flag, created_at, updated_at, ref_url
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.topo.entity.TopoModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_topo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from data_topo
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.bdtd.modules.topo.entity.TopoModel">
    insert into data_topo (name, code, value, version, flag, ref_url)
    values (
            #{name,jdbcType=VARCHAR},
            #{code,jdbcType=VARCHAR},
            #{value,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            #{version,jdbcType=INTEGER},
            #{flag,jdbcType=INTEGER},
            #{refUrl,jdbcType=VARCHAR}
    )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.topo.entity.TopoModel">
    update data_topo
    <set>
      <if test="name != null ">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null ">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="value != null ">
        value = #{value,jdbcType=jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=INTEGER},
      </if>
      <if test="refUrl != null">
        ref_url = #{refUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByExampleSelective" parameterType="map">
    update data_topo
    <set>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.flag != null">
        flag = #{record.flag,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="refUrl != null">
        ref_url = #{refUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="selectByFlag" resultMap="BaseResultMapNCVC">
    select name,code,version,created_at,ref_url FROM data_topo where flag=1
    <if test="name != null">
     and name like '%' || #{name,jdbcType=VARCHAR} || '%'
    </if>
    ORDER BY created_at DESC
  </select>
  <select id="selectByCodeAndFlag" parameterType="String" resultMap="BaseResultMapNVC">
    select name,value,code,version,ref_url FROM data_topo where flag=1 and code=#{code,jdbcType=VARCHAR} ORDER BY created_at DESC
  </select>
</mapper>