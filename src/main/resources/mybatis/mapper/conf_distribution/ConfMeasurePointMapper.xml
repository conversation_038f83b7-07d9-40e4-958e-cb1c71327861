<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.conf_distribution.mapper.ConfMeasurePointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="confMeasurePointResultMap" type="com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint">
        <result column="id" property="id"/>
        <result column="out_point_id" property="outPointId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="control_type" property="controlType"/>
        <result column="describe" property="describe"/>
        <result column="url" property="url"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="point_secret" property="pointSecret"/>
    </resultMap>

    <select id="selectWhitePointPages" resultType="com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint">
        select point.*
        from blade_conf_measure_point point
        where point.id not in (select black.point_id from blade_conf_black_list black where black.is_deleted = 0)
        <if test="param.name != null and param.name != ''">
            and point."name" like concat(concat('%', #{param.name}), '%')
        </if>
        <if test="param.type != null">
            and point."type" = #{param.type}
        </if>
          and point.is_deleted = 0
        order by point.create_time desc
    </select>

</mapper>
