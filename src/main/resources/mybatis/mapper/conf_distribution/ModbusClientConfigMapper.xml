<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.conf_distribution.mapper.ModbusClientConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="modbusClientConfigResultMap" type="com.bdtd.modules.conf_distribution.entity.ModbusClientConfig">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="scheme_id" property="schemeId"/>
        <result column="scheme_code" property="schemeCode"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="heartbeat_address" property="heartbeatAddress"/>
        <result column="plc_commu_status" property="plcCommuStatus"/>
        <result column="vfd_commu_address" property="vfdCommuAddress"/>
        <result column="vfd_commu_status" property="vfdCommuStatus"/>
    </resultMap>


    <select id="selectModbusClientConfigPage" resultMap="modbusClientConfigResultMap">
        select * from blade_modbus_client_config where is_deleted = 0
    </select>

</mapper>
