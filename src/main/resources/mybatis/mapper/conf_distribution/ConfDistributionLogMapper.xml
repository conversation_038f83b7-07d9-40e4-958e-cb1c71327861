<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.conf_distribution.mapper.ConfDistributionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="confDistributionLogResultMap" type="com.bdtd.modules.conf_distribution.entity.ConfDistributionLog">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="point" property="point"/>
        <result column="action" property="action"/>
        <result column="start_time_stamp" property="startTimeStamp"/>
        <result column="end_time_stamp" property="endTimeStamp"/>
        <result column="elapsed_time" property="elapsedTime"/>
        <result column="action_result" property="actionResult"/>
    </resultMap>

    <select id="selectConfDistributionLogPage" resultType="com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO">
        select
            bcdl.*,
            bcmp."name" as pointName,
            bcmp."describe"
        from
            blade_conf_distribution_log bcdl
            left join blade_conf_measure_point bcmp on bcdl.point_id = bcmp.out_point_id
        where
            bcdl.is_deleted = 0
        <if test="param.pointId != null and param.pointId != ''">
            and bcdl.point_id = #{param.pointId}
        </if>
        <if test="param.pointName != null and param.pointName != ''">
            and bcmp."name" like '%' || #{param.pointName} || '%'
        </if>
        <if test="param.describe != null and param.describe != ''">
            and bcmp."describe" like '%' || #{param.describe} || '%'
        </if>
        <if test="param.operator != null">
            and bcdl.operator = #{param.operator}
        </if>
        <if test="param.userName != null">
            and bcdl.user_name = #{param.userName}
        </if>
        <if test="param.elapsedTimeMin != null">
            and bcdl.elapsed_time &gt;= #{param.elapsedTimeMin}
        </if>
        <if test="param.elapsedTimeMax != null">
            and bcdl.elapsed_time &lt;= #{param.elapsedTimeMax}
        </if>
        order by bcdl.create_time desc
    </select>

    <select id="selectPageConditional" resultType="com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO">
        SELECT
            DISTINCT cdl.*, cmp."name" as pointName, cmp."type" as pointType, bu."name" as createUserName
        FROM
            blade_conf_distribution_log as cdl
            LEFT JOIN (SELECT * FROM blade_conf_measure_point WHERE is_deleted = 0) as cmp on cdl.point_id = cmp.out_point_id
            LEFT JOIN (SELECT * FROM blade_user WHERE is_deleted = 0) as bu on cdl.create_user = bu."id"
        <where>
            cdl.is_deleted = 0
            <if test="param.pointId != null and param.pointId != ''">
                cdl.point_id = #{param.pointId}
            </if>
            <if test="param.pointName != null and param.pointName != ''">
                and cmp."name" like concat(concat('%', #{param.pointName}), '%')
            </if>
            <if test="param.pointType != null">
                and cmp."type" = #{param.pointType}
            </if>
            <if test="param.createUser != null">
                and cdl.create_user = #{param.createUser}
            </if>
            <if test="param.actionResult != null">
                and cdl.action_result = #{param.actionResult}
            </if>
            <if test="param.createUserName != null and param.createUserName != ''">
                and bu."name" like concat(concat('%', #{param.createUserName}), '%')
            </if>
            <if test="param.startTimeStamp != null">
                and cdl.start_time_stamp &gt;= #{param.startTimeStamp}
            </if>
            <if test="param.endTimeStamp != null">
                and cdl.start_time_stamp &lt;= #{param.endTimeStamp}
            </if>
        </where>
        order by cdl.start_time_stamp desc
    </select>

</mapper>
