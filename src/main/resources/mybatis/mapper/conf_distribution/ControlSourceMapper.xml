<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.conf_distribution.mapper.AccessLinkTroubleMapper">

    <select id="selectPage" resultType="com.bdtd.modules.conf_distribution.entity.ControlSource">
        SELECT
            business_type,
            troubleshooting_suggestions,
            id,
            ip,
            port,
            user_name,
            pass_word,
            security_policy
        from control_source
        <where>
            <if test="ip!=null and ip!=''">
                and ip like '%'|| #{ip} ||'%'
            </if>
            <if test="port != null and port !=''">
                and cast(port as varchar) like '%'|| #{port} ||'%'
            </if>
            <if test="userName != null and userName !=''">
                and user_name like '%'|| #{userName} ||'%'
            </if>
        </where>
    </select>

</mapper>