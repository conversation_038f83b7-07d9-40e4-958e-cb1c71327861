<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.ModelSystemAlarmMapper">

    <select id="selectByName" resultType="java.lang.Integer">
        select count(1)
        from model_system_alarm
        where indictor_id = #{indictorId}
          and point_id = #{pointId}
          and alarm_value = #{alarmValue}
    </select>

</mapper>
