<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.MQStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.monitor.entity.MQStatisticsEntity">
    <id column="routing_key" jdbcType="VARCHAR" property="routingKey" />
    <result column="mq_receive" jdbcType="INTEGER" property="mqReceive" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    routing_key, mq_receive, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.monitor.dto.MQStatisticsEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mq_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <!--
  <select id="selectAll" resultMap="BaseResultMap">
    select * from mq_statistics
  </select> -->

  <select id="selectAll" parameterType="java.lang.String"  resultType="com.bdtd.modules.monitor.entity.MQStatisticsEntity">
    select
    <include refid="Base_Column_List" />
    from mq_statistics
    WHERE routing_key = #{routingKey,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from mq_statistics
    where routing_key = #{routingKey,jdbcType=VARCHAR}
  </delete>

  <select id="getStatisticsById" parameterType="java.lang.String"  resultType="com.bdtd.modules.monitor.entity.MQStatisticsEntity">
    select
    <include refid="Base_Column_List" />
    from mq_statistics WHERE  routing_key = #{routingKey,jdbcType=VARCHAR}
  </select>
</mapper>