<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.MqSuspendMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.monitor.entity.MqSuspend">
        <id column="routing_key" jdbcType="VARCHAR" property="routingKey"/>
        <id column="suspend_time" jdbcType="TIMESTAMP" property="suspendTime"/>
        <result column="recovery_time" jdbcType="TIMESTAMP" property="recoveryTime"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="duration" jdbcType="BIGINT" property="duration"/>
    </resultMap>

    <insert id="upsert">
        insert into mq_suspend(routing_key, suspend_time, recovery_time, duration)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.routingKey}, #{item.suspendTime}, #{item.recoveryTime}, #{item.duration})
        </foreach>
        ON conflict(routing_key, suspend_time) do update set
            recovery_time = excluded.recovery_time,
            duration=excluded.duration
    </insert>
</mapper>