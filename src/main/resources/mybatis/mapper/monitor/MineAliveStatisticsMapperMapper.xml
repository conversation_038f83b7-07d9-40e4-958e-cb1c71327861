<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.MineAliveStatisticsMapper">

    <select id="getNowBreakageList"
            resultType="com.bdtd.modules.monitor.entity.MineAliveStatistics"
            parameterType="com.bdtd.modules.monitor.dto.MineAliveQueryCriteria">
        select *
        from (
            select
                row_number() over (
                    partition by mas.mine_code
                    order by mas.disconnection_time desc
                ) rowid,
                mas.id,
                mas.group_code,
                mas.mine_code,
                mas.disconnection_time,
                mas.recovery_time,
                mas.disconnection_duration,
                mas.interrupt_check_time,
                mas.recovery_check_time,
                mas.interrupt_check_duration,
                mas.disconnection_details,
                mas.disconnection_clause,
                mas.recovery_measures,
                mas.processing_person,
                mas.processing_time,
                mas.created_at,
                mas.updated_at
            from mine_alive_statistics mas
            where disconnection_time is not null
            <if test="query.companyCode != null and query.companyCode != ''">
                and group_code = #{query.companyCode}
            </if>
            <if test="query.mineCode != null and query.mineCode != ''">
                and mine_code = #{query.mineCode}
            </if>
        ) temp
        where rowid = 1
        order by updated_at desc
    </select>

    <select id="getStatisticsList"
            resultType="com.bdtd.modules.monitor.dto.MineAliveStateVo"
            parameterType="com.bdtd.modules.monitor.dto.MineAliveQueryCriteria">
        select
            mine_code,
            (array_agg(group_code))[1] as group_code,
            COUNT(*) disconnectionNumberSum,
            COUNT(*) disconnectionCount,
            sum(disconnection_duration) disconnectionDurationSum,
            max(disconnection_duration) disconnectionNumberMax,
            max(disconnection_duration) disconnectionDurationMax,
            min(disconnection_duration) disconnectionNumberMin,
            min(disconnection_duration) disconnectionDurationMin,
            ROUND(avg(disconnection_duration), 0) disconnectionNumberAvg,
            ROUND(avg(disconnection_duration), 0) disconnectionDurationAvg,
            ROUND(avg(disconnection_duration), 0) disconnectionDurationAvg,
            sum(duration_check_time) interruptCheckDurationSum,
            max(duration_check_time) interruptCheckDurationMax,
            min(duration_check_time) interruptCheckDurationMin,
            ROUND(avg(duration_check_time), 0) interruptCheckDurationAvg
        from (
            select
                mas.mine_code,
                mas.group_code,
                case
                    when mas.recovery_time is not null then trunc(mas.disconnection_duration::numeric)
                    else trunc(extract(epoch from (now()- mas.disconnection_time))::numeric)
                end as disconnection_duration,
                case
                    when mas.recovery_check_time is not null then trunc(mas.interrupt_check_duration::numeric)
                    else trunc(extract(epoch from (now()- mas.interrupt_check_time))::numeric)
                end as duration_check_time
            from mine_alive_statistics mas
            <where>
                <if test="entity.companyCode != null and entity.companyCode != ''">
                    and group_code = #{entity.companyCode}
                </if>
                <if test="entity.mineCode != null and entity.mineCode != ''">
                    and mine_code = #{entity.mineCode}
                </if>
                <if test="entity.startTime != null and entity.startTime != ''">
                    AND mas.disconnection_time <![CDATA[ >= ]]> #{entity.startTime}
                </if>
                <if test="entity.endTime != null and entity.endTime != ''">
                    AND mas.disconnection_time <![CDATA[ <= ]]> #{entity.endTime}
                </if>
                <if test="entity.startCheckTime != null and entity.startCheckTime != ''">
                    AND mas.interrupt_check_time <![CDATA[ >= ]]> #{entity.startCheckTime}
                </if>
                <if test="entity.endCheckTime != null and entity.endCheckTime != ''">
                    AND mas.interrupt_check_time <![CDATA[ <= ]]> #{entity.endCheckTime}
                </if>
            </where>
            order by mas.disconnection_time desc
        ) temp
        group by mine_code
    </select>

    <select id="getInterruptionDurationTotal" resultType="java.lang.Long">
        select
            sum (
                case
                    when recovery_time is null then EXTRACT(EPOCH FROM (now() - disconnection_time))
                    else EXTRACT(EPOCH FROM (recovery_time - disconnection_time))
                end
            ) as duration_total
        from mine_alive_statistics
        ${ew.customSqlSegment}
    </select>

    <select id="getInterruptionCheckDurationTotal" resultType="java.lang.Long">
        select
            sum (
                case
                    when recovery_check_time is null then EXTRACT(EPOCH FROM (now() - interrupt_check_duration))
                    else EXTRACT(EPOCH FROM (recovery_data_time - interrupt_check_time))
                end
            ) as check_duration_total
        from mine_alive_statistics
        ${ew.customSqlSegment}
    </select>

</mapper>
