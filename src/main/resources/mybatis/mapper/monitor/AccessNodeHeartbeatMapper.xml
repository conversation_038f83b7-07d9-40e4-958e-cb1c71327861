<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.AccessNodeHeartbeatMapper">

    <insert id="upsertStateResult">
        INSERT INTO data_access_node_heartbeat (
            node_id,
            hb_time,
            update_time,
            "offline",
            memo,
            created_at,
            updated_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.nodeId,jdbcType=VARCHAR},
                #{item.hbTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.offline,jdbcType=INTEGER},
                #{item.memo,jdbcType=VARCHAR},
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            )
        </foreach>
        on conflict(node_id) do update set
            hb_time = excluded.hb_time,
            update_time = excluded.update_time,
            "offline" = excluded."offline",
            memo = excluded.memo,
            updated_at = coalesce(excluded.updated_at, CURRENT_TIMESTAMP)
    </insert>

    <update id="updateParentNodeState">
        INSERT INTO data_access_node_heartbeat (
            node_id,
            hb_time,
            update_time,
            "offline",
            memo,
            created_at,
            updated_at
        )
        select
            node_id,
            hb_time,
            update_time,
            case when children_count = normal_count then 0 else 1 end as "offline",
            concat(children_count, '个子节点, ', normal_count, '个检测正常') as memo,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        from (
            select
                dan.parent_id as node_id,
                max(danh.hb_time) as hb_time,
                max(danh.update_time) as update_time,
                count(*) as children_count,
                sum(case when danh.offline = 0 then 1 else 0 end) as normal_count
            from data_access_node_heartbeat danh
                join data_access_node dan on danh.node_id = dan.id and dan.parent_id != '0'
            group by dan.parent_id
        ) m
        on conflict(node_id) do update set
            hb_time = excluded.hb_time,
            update_time = excluded.update_time,
            "offline" = excluded."offline",
            memo = excluded.memo,
            updated_at = coalesce(excluded.updated_at, CURRENT_TIMESTAMP)
    </update>

</mapper>
