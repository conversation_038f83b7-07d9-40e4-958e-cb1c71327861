<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.ModelSystemAliveStatisticsMapper">

    <select
        id="getNowBreakageList"
        parameterType="com.bdtd.modules.monitor.dto.ModelSystemAliveQueryCriteria"
        resultType="com.bdtd.modules.monitor.entity.ModelSystemAliveStatistics"
    >
        select *
        from (
            select
                row_number() over (
                    partition by msas.system_id
                    order by msas.disconnection_time desc
                ) rowid,
                msas.id,
                msas.system_id,
                msas.system_name,
                msas.disconnection_time,
                msas.recovery_time,
                msas.disconnection_duration,
                msas.interrupt_check_time,
                msas.recovery_check_time,
                msas.interrupt_check_duration,
                msas.processing_people,
                msas.processing_time,
                msas.disconnection_cause,
                msas.recovery_error,
                msas.insert_db_time,
                msas.update_db_time
            from model_system_alive_statistics msas
                left join model_system ms on msas.system_id = ms.id
                left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
            where disconnection_time is not null
        ) temp
        where rowid = 1
        <if test="query.systemId != null and query.systemId != ''">
            and system_id like '%'||#{query.systemId}||'%'
        </if>
        <if test="query.systemName != null and query.systemName != ''">
            and system_name like '%'||#{query.systemName}||'%'
        </if>
        ORDER BY update_db_time DESC
    </select>

    <select
        id="getAliveStatisticsList"
        parameterType="com.bdtd.modules.monitor.dto.ModelSystemAliveQueryCriteria"
        resultType="com.bdtd.modules.monitor.dto.ModelSystemAliveStatisticsDto"
    >
        select
            system_id,
            (array_agg(system_name))[1] as system_name,
            (array_agg(system_type))[1] as system_type,
            (array_agg(mine_id))[1] as mine_id,
            (array_agg(group_code))[1] as group_code,
            COUNT (*) disconnectionNumberSum,
            COUNT (*) disconnectionCount,
            sum(duration_time) disconnectionDurationSum,
            max(duration_time) disconnectionNumberMax,
            max(duration_time) disconnectionDurationMax,
            min(duration_time) disconnectionNumberMin,
            min(duration_time) disconnectionDurationMin,
            ROUND(avg(duration_time), 0) disconnectionNumberAvg,
            ROUND(avg(duration_time), 0) disconnectionDurationAvg,
            sum(duration_check_time) interruptCheckDurationSum,
            max(duration_check_time) interruptCheckDurationMax,
            min(duration_check_time) interruptCheckDurationMin,
            ROUND(avg(duration_check_time), 0) interruptCheckDurationAvg
        from (
            select
                msas.system_id,
                <choose>
                    <when test='query.sid != null and query.sid == "990"'>
                        coalesce(wfpd.working_face_name, msas.system_name) as system_name,
                    </when>
                    <otherwise>
                        msas.system_name,
                    </otherwise>
                </choose>
                wfpd.working_face_type as system_type,
                msas.mine_id,
                ms.group_code,
                case
                    when msas.recovery_time is not null then trunc(msas.disconnection_duration::numeric)
                    else trunc(extract(epoch from (now()- msas.disconnection_time))::numeric)
                end as duration_time,
                case
                    when msas.recovery_check_time is not null then trunc(msas.interrupt_check_duration::numeric)
                    else trunc(extract(epoch from (now()- msas.interrupt_check_time))::numeric)
                end as duration_check_time
            from model_system_alive_statistics msas
                left join model_system ms on msas.system_id = ms.id
                left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
            <where>
                <if test="query.groupCode != null and query.groupCode != ''">
                    and ms.group_code = #{query.groupCode}
                </if>
                <if test="query.mineId != null and query.mineId != ''">
                    and msas.mine_id = #{query.mineId}
                </if>
                <if test="query.sid != null and query.sid != ''">
                    AND msas.system_id like #{query.sid}||'%'
                </if>
                <if test="query.systemId != null and query.systemId != ''">
                    and msas.system_id = #{query.systemId}
                </if>
                <if test="query.systemName != null and query.systemName != ''">
                    and ms.name like '%'||#{query.systemName}||'%'
                </if>
                <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                    and ms.source_system_code = #{query.sourceSystemCode}
                </if>
                <if test="query.systemType != null and query.systemType != ''">
                    and wfpd.working_face_type = #{query.systemType}
                </if>
                <if test="query.startTime != null and query.startTime != ''">
                    AND msas.disconnection_time <![CDATA[ >= ]]> #{query.startTime}
                </if>
                <if test="query.endTime != null and query.endTime != ''">
                    AND msas.disconnection_time <![CDATA[ <= ]]> #{query.endTime}
                </if>
                <if test="query.processingPeople != null and query.processingPeople != ''">
                    and msas.processing_people = #{query.processingPeople}
                </if>
                <if test="query.isHandle != null and query.isHandle == 1">
                    and msas.processing_time is not null
                </if>
                <if test="query.isHandle != null and query.isHandle == 0">
                    and msas.processing_time is null
                </if>
            </where>
            order by msas.disconnection_time DESC
        ) temp
        group by system_id
    </select>

    <!--
    /* getOfflineRatioPage */
    with eval_details as (
        select
            work_face_id,
            work_face_type,
            case
                when begin_time < TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                else begin_time
            end as begin_time,
            case
                when end_time is null or end_time > TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
                else end_time
            end as end_time
        from working_face_event
        where record_type = 'state'
            and event_type = 'life_cycle'
            and (value = '生产' or value = '生产中' or value = '施工中')
            and (
                   begin_time <= TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and (end_time is null or end_time > TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))
                or begin_time between TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
            )
    )
    select *
    from (
        select
            ms.group_code,
            ms.mine_code,
            cmg."name" as group_name,
            cmn."name" as mine_name,
            ms.id as system_id,
            ms."name" as system_name,
            ms.category as system_type,
            ms.source_system_code,
            ms.flag,
            case when ms.flag = '1' then '正常' else '停止接入' end as flag_text,
            /* 考核时长, 为空意味不符合考核条件 */
            eval_stat.state_duration as eval_duration,
            /* 中断时长, 为空意味没有中断记录, 中断时长计为 0 */
            int_stat.total_exact_int as interrupt_duration,
            int_counter.int_count as interrupt_count,
            /* 离线率, null无效, 0, 1, 0~1的小数 */
            case
                /* 考核时长作为分母, 计算离线率 */
                when eval_stat.state_duration is not null then
                    case
                        when int_stat.total_exact_int is null then 0
                        when int_stat.total_exact_int > extract(epoch from (TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) then 1
                        else round((int_stat.total_exact_int / extract(epoch from (TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))))::numeric, 5)
                    end
                when eval_stat.state_duration is null and '0' = '1' then
                    /* 如果没有符合条件的考核时长, 是否使用查询时长作为分母 */
                    case
                        when int_stat.total_exact_int is null then 0
                        when int_stat.total_exact_int > extract(epoch from (TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) then 1
                        else round((int_stat.total_exact_int / extract(epoch from (TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))))::numeric, 5)
                    end
                else null
            end as offline_ratio
        from
            /* 考虑到通用性, 主表应该是 model_system */
            model_system ms
            /* 可选,  可以不返回二级公司、煤矿名称 */
            left join cms_mine_name cmn on ms.mine_code = cmn.code
            left join cms_mine_group cmg on ms.group_code = cmg.code
            /* 工作面定义信息 */
            left join working_face_progress_definition wfpd on wfpd.system_code = ms.id
            /* 工作面子系统, 获取考核时长, 没有符合条件采用查询时长 */
            left join (
                select
                    work_face_id,
                    work_face_type,
                    sum(extract(epoch from (end_time - begin_time))) as state_duration
                from eval_details
                group by work_face_id, work_face_type
            ) eval_stat on ms.source_system_code = eval_stat.work_face_id and ms.category = eval_stat.work_face_type
            /* 子系统中断时长 */
            left join (
                select
                    system_id,
                    sum(exact_duration) as total_exact_int
                from (
                    select
                        msas.system_id,
                        case
                            /* 关联到考核时长, 使用考试时长对应的时间范围 */
                            when ed.work_face_id is not null then
                                extract(epoch from (
                                    case
                                        when msas.recovery_time is null or msas.recovery_time > ed.end_time then ed.end_time
                                        else msas.recovery_time
                                    end
                                    -
                                    case
                                        when msas.disconnection_time < ed.begin_time then ed.begin_time
                                        else msas.disconnection_time
                                    end
                                ))
                            else
                                /* 未关联到考核时长, 使用查询时间范围 */
                                extract(epoch from (
                                    case
                                        when msas.recovery_time is null or msas.recovery_time > TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
                                        else msas.recovery_time
                                    end
                                    -
                                    case
                                        when msas.disconnection_time < TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                        else msas.disconnection_time
                                    end
                                ))
                        end as exact_duration
                    from model_system_alive_statistics msas
                        join model_system ms on msas.system_id = ms.id
                        left join eval_details ed on ms.source_system_code = ed.work_face_id
                            and ms.category = ed.work_face_type
                            and (
                                   msas.disconnection_time <= ed.begin_time and (msas.recovery_time is null or msas.recovery_time > ed.begin_time)
                                or msas.disconnection_time between ed.begin_time and ed.end_time
                            )
                    where
                        /* 断线记录查询条件冗余, 减少数据量 */
                        (
                               msas.disconnection_time <= TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and (msas.recovery_time is null or msas.recovery_time > TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))
                            or msas.disconnection_time between TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
                        )
                        /* 子系统查询条件冗余, 减少数据量 */
                        and ms.group_code = '10040'
                        and ms.mine_code = '370882035910'
                        and ms.sid = '990'
                        and ms.id like '990%'
                        and ms.id = '9901767471299072024577'
                        and ms."name" like '%切眼%'
                        and ms.source_system_code = '1767471299072024577'
                        and ms.category = 'ewf'
                ) int_details
                group by system_id
            ) int_stat on ms.id = int_stat.system_id
            left join (
                select
                    system_id,
                    count(1) as int_count
                from model_system_alive_statistics
                where (
                       disconnection_time <= TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and (recovery_time is null or recovery_time > TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))
                    or disconnection_time between TO_TIMESTAMP('2024-03-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('2024-03-25 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
                )
                group by system_id
            ) int_counter on ms.id = int_counter.system_id
        where 1=1
            /* 子系统查询条件 */
            and ms.group_code = '10040'
            and ms.mine_code = '370882035910'
            and ms.sid = '990'
            and ms.id like '990%'
            and ms.id = '9901767471299072024577'
            and ms."name" like '%切眼%'
            and ms.source_system_code = '1767471299072024577'
            and ms.category = 'ewf'
            /* 是否符合考核条件: 在指定时间段内有 生产/施工中 状态持续 */
            and eval_stat.state_duration is not null
            /* 测试条件 */
            /* and eval_stat.state_duration < 0 */
            /* and int_stat.total_exact_int < 0 */
    ) m
    -->
    <sql id="offlineRatioQuery">
        select
            group_code,
            mine_code,
            group_name,
            mine_name,
            group_sort,
            mine_sort,
            system_id,
            system_name,
            system_type,
            source_system_code,
            "flag",
            flag_text,
            case
                when eval_duration is not null then eval_duration
            <if test="query.queryTimeFallback == null or query.queryTimeFallback == 1">
                when eval_duration is null and true then
            </if>
            <if test="query.queryTimeFallback != null and query.queryTimeFallback != 1">
                when eval_duration is null and false then
            </if>
                    extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')))::numeric
                else null
            end as eval_duration,
            case when interrupt_duration is null then 0 else interrupt_duration end as interrupt_duration,
            case when interrupt_count is null then 0 else interrupt_count end as interrupt_count,
            offline_ratio
        from (
            with eval_details as (
                select
                    work_face_id,
                    work_face_type,
                    case
                        when begin_time &lt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                        else begin_time
                    end as begin_time,
                    case
                        when end_time is null or end_time &gt; TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                        else end_time
                    end as end_time
                from working_face_event
                where record_type = 'state'
                    and event_type = 'life_cycle'
                    and (value = '生产' or value = '生产中' or value = '施工中')
                    and (
                           begin_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (end_time is null or end_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                        or begin_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                    )
            )
            select
                ms.group_code,
                ms.mine_code,
                cmg."name" as group_name,
                cmn."name" as mine_name,
                cmg."sort" as group_sort,
                cmn."sort" as mine_sort,
                ms."id" as system_id,
                ms."name" as system_name,
                ms.category as system_type,
                ms.source_system_code,
                ms."flag",
                case when ms."flag" = '1' then '正常' else '停止接入' end as flag_text,
                eval_stat.state_duration as eval_duration,
                int_stat.total_exact_int as interrupt_duration,
                int_counter.int_count as interrupt_count,
                case
                    when eval_stat.state_duration is not null then
                        case
                            when int_stat.total_exact_int is null then 0
                            when int_stat.total_exact_int &gt; eval_stat.state_duration then 1
                            else round((int_stat.total_exact_int / eval_stat.state_duration)::numeric, 5)
                        end
                <if test="query.queryTimeFallback == null or query.queryTimeFallback == 1">
                    when eval_stat.state_duration is null and true then
                </if>
                <if test="query.queryTimeFallback != null and query.queryTimeFallback != 1">
                    when eval_stat.state_duration is null and false then
                </if>
                        case
                            when int_stat.total_exact_int is null then 0
                            when int_stat.total_exact_int &gt; extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))) then 1
                            else round((int_stat.total_exact_int / extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))))::numeric, 5)
                        end
                    else null
                end as offline_ratio
            from model_system ms
                join cms_mine_name cmn on ms.mine_code = cmn.code
                join cms_mine_group cmg on ms.group_code = cmg.code
            <if test="query.systemStatus != null and query.systemStatus != ''">
                left join working_face_progress_definition wfpd on wfpd.system_code = ms.id
            </if>
                left join (
                    select
                        work_face_id,
                        work_face_type,
                        sum(extract(epoch from (end_time - begin_time))) as state_duration
                    from eval_details
                    group by work_face_id, work_face_type
                ) eval_stat on ms.source_system_code = eval_stat.work_face_id and ms.category = eval_stat.work_face_type
                left join (
                    select
                        system_id,
                        sum(exact_duration) as total_exact_int
                    from (
                        select
                            msas.system_id,
                            case
                                when ed.work_face_id is not null then
                                    extract(epoch from (
                                        case
                                            when msas.recovery_time is null or msas.recovery_time &gt; ed.end_time then ed.end_time
                                            else msas.recovery_time
                                        end
                                        -
                                        case
                                            when msas.disconnection_time &lt; ed.begin_time then ed.begin_time
                                            else msas.disconnection_time
                                        end
                                    ))
                                else
                                    extract(epoch from (
                                        case
                                            when msas.recovery_time is null or msas.recovery_time &gt; TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                                            else msas.recovery_time
                                        end
                                        -
                                        case
                                            when msas.disconnection_time &lt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                                            else msas.disconnection_time
                                        end
                                    ))
                            end as exact_duration
                        from model_system_alive_statistics msas
                            join model_system ms on msas.system_id = ms.id
                            left join eval_details ed on ms.source_system_code = ed.work_face_id
                                and ms.category = ed.work_face_type
                                and (
                                       msas.disconnection_time &lt;= ed.begin_time and (msas.recovery_time is null or msas.recovery_time &gt; ed.begin_time)
                                    or msas.disconnection_time between ed.begin_time and ed.end_time
                                )
                        where
                            (
                                   msas.disconnection_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (msas.recovery_time is null or msas.recovery_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                                or msas.disconnection_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                            )
                            <if test="query.groupCode != null and query.groupCode != ''">
                            and ms.group_code = #{query.groupCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.mineCode != null and query.mineCode != ''">
                            and ms.mine_code = #{query.mineCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.sid != null and query.sid != ''">
                            and ms.sid = #{query.sid,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemId != null and query.systemId != ''">
                            and ms.id = #{query.systemId,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemName != null and query.systemName != ''">
                            and ms."name" like concat('%',#{query.systemName,jdbcType=VARCHAR}::text,'%')
                            </if>
                            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                            and ms.source_system_code = #{query.sourceSystemCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemType != null and query.systemType != ''">
                            and ms.category = #{query.systemType,jdbcType=VARCHAR}
                            </if>
                    ) int_details
                    group by system_id
                ) int_stat on ms.id = int_stat.system_id
                left join (
                    select
                        system_id,
                        count(1) as int_count
                    from model_system_alive_statistics
                    where (
                           disconnection_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (recovery_time is null or recovery_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                        or disconnection_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                    )
                    group by system_id
                ) int_counter on ms.id = int_counter.system_id
            <where>
                <if test="query.groupCode != null and query.groupCode != ''">
                and ms.group_code = #{query.groupCode,jdbcType=VARCHAR}
                </if>
                <if test="query.mineCode != null and query.mineCode != ''">
                and ms.mine_code = #{query.mineCode,jdbcType=VARCHAR}
                </if>
                <if test="query.sid != null and query.sid != ''">
                and ms.sid = #{query.sid,jdbcType=VARCHAR}
                </if>
                <if test="query.systemId != null and query.systemId != ''">
                and ms.id = #{query.systemId,jdbcType=VARCHAR}
                </if>
                <if test="query.systemName != null and query.systemName != ''">
                and ms."name" like concat('%',#{query.systemName,jdbcType=VARCHAR}::text,'%')
                </if>
                <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                and ms.source_system_code = #{query.sourceSystemCode,jdbcType=VARCHAR}
                </if>
                <if test="query.systemType != null and query.systemType != ''">
                and ms.category = #{query.systemType,jdbcType=VARCHAR}
                </if>
                <if test="query.systemStatus != null and query.systemStatus != ''">
                and wfpd.work_state = #{query.systemStatus,jdbcType=VARCHAR}
                </if>
                <if test="query.ignoreUnnecessary != null and query.ignoreUnnecessary == 1">
                and eval_stat.state_duration is not null
                </if>
            </where>
        ) m
    </sql>
    <select id="getOfflineRatioPage" resultType="com.bdtd.modules.monitor.dto.ModelSystemOfflineRatioDto">
        <include refid="offlineRatioQuery"/>
    </select>
    <select id="getOfflineRatioAll" resultType="com.bdtd.modules.monitor.dto.ModelSystemOfflineRatioDto">
        <include refid="offlineRatioQuery"/>
        order by group_sort desc, mine_sort desc, system_name
    </select>
    <select id="getOfflineRatioCount" resultType="java.lang.Long">
        with eval_details as (
            select
                  work_face_id
                , work_face_type
            <if test="query.ignoreUnnecessary != null and query.ignoreUnnecessary == 1">
                , case
                    when begin_time &lt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                    else begin_time
                end as begin_time
                , case
                    when end_time is null or end_time &gt; TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                    else end_time
                end as end_time
            </if>
            from working_face_event
            where record_type = 'state'
                and event_type = 'life_cycle'
                and (value = '生产' or value = '生产中' or value = '施工中')
                and (
                       begin_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (end_time is null or end_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                    or begin_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                )
        )
        select count(1)
        from model_system ms
        <if test="query.systemStatus != null and query.systemStatus != ''">
            left join working_face_progress_definition wfpd on wfpd.system_code = ms.id
        </if>
        <if test="query.ignoreUnnecessary != null and query.ignoreUnnecessary == 1">
            left join (
                select
                      work_face_id
                    , work_face_type
                    , sum(extract(epoch from (end_time - begin_time))) as state_duration
                from eval_details
                group by work_face_id, work_face_type
            ) eval_stat on ms.source_system_code = eval_stat.work_face_id and ms.category = eval_stat.work_face_type
        </if>
        <where>
            <if test="query.groupCode != null and query.groupCode != ''">
            and ms.group_code = #{query.groupCode,jdbcType=VARCHAR}
            </if>
            <if test="query.mineCode != null and query.mineCode != ''">
            and ms.mine_code = #{query.mineCode,jdbcType=VARCHAR}
            </if>
            <if test="query.sid != null and query.sid != ''">
            and ms.sid = #{query.sid,jdbcType=VARCHAR}
            </if>
            <if test="query.systemId != null and query.systemId != ''">
            and ms.id = #{query.systemId,jdbcType=VARCHAR}
            </if>
            <if test="query.systemName != null and query.systemName != ''">
            and ms."name" like concat('%',#{query.systemName,jdbcType=VARCHAR}::text,'%')
            </if>
            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
            and ms.source_system_code = #{query.sourceSystemCode,jdbcType=VARCHAR}
            </if>
            <if test="query.systemType != null and query.systemType != ''">
            and ms.category = #{query.systemType,jdbcType=VARCHAR}
            </if>
            <if test="query.systemStatus != null and query.systemStatus != ''">
            and wfpd.work_state = #{query.systemStatus,jdbcType=VARCHAR}
            </if>
            <if test="query.ignoreUnnecessary != null and query.ignoreUnnecessary == 1">
            and eval_stat.state_duration is not null
            </if>
        </where>
    </select>

    <select id="getAlivePage" resultType="com.bdtd.modules.monitor.entity.ModelSystemAliveStatistics">
        select
            msas.id,
            msas.system_id,
            <choose>
                <when test='query.sid != null and query.sid == "990"'>
                    coalesce(wfpd.working_face_name, msas.system_name) as system_name,
                </when>
                <otherwise>
                    msas.system_name,
                </otherwise>
            </choose>
            msas.mine_id,
            msas.disconnection_time,
            msas.recovery_time,
            msas.disconnection_duration,
            msas.interrupt_check_time,
            msas.recovery_check_time,
            msas.interrupt_check_duration,
            msas.processing_people,
            msas.processing_time,
            msas.disconnection_cause,
            msas.recovery_error,
            msas.insert_db_time,
            msas.update_db_time,
            wfpd.working_face_type as system_type,
            ms.group_code
        from model_system_alive_statistics msas
            left join model_system ms on msas.system_id = ms.id
            left join cms_mine_name cmn on ms.mine_code = cmn.code
            left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
        <where>
            <if test="query.groupCode != null and query.groupCode != ''">
                and ms.group_code = #{query.groupCode}
            </if>
            <if test="query.mineId != null and query.mineId != ''">
                and msas.mine_id = #{query.mineId}
            </if>
            <if test='(query.groupCode == null or query.groupCode == "") and (query.mineId == null or query.mineId == "")'>
                and cmn."name" not like '%MK'
            </if>
            <if test="query.sid != null and query.sid != ''">
                AND msas.system_id like #{query.sid}||'%'
            </if>
            <if test="query.systemId != null and query.systemId != ''">
                and msas.system_id = #{query.systemId}
            </if>
            <if test="query.systemName != null and query.systemName != ''">
                and ms.name like '%'||#{query.systemName}||'%'
            </if>
            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                and ms.source_system_code = #{query.sourceSystemCode}
            </if>
            <if test="query.systemType != null and query.systemType != ''">
                and wfpd.working_face_type = #{query.systemType}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND msas.disconnection_time <![CDATA[ >= ]]> #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND msas.disconnection_time <![CDATA[ <= ]]> #{query.endTime}
            </if>
            <if test="query.processingPeople != null and query.processingPeople != ''">
                and msas.processing_people = #{query.processingPeople}
            </if>
            <if test="query.isHandle != null and query.isHandle == 1">
                and msas.processing_time is not null
            </if>
            <if test="query.isHandle != null and query.isHandle == 0">
                and msas.processing_time is null
            </if>
        </where>
        order by msas.disconnection_time DESC
    </select>

    <select id="getAliveList" resultType="com.bdtd.modules.monitor.entity.ModelSystemAliveStatistics">
        select
            msas.id,
            msas.system_id,
            <choose>
                <when test='query.sid != null and query.sid == "990"'>
                    coalesce(wfpd.working_face_name, msas.system_name) as system_name,
                </when>
                <otherwise>
                    msas.system_name,
                </otherwise>
            </choose>
            msas.mine_id,
            msas.disconnection_time,
            msas.recovery_time,
            msas.disconnection_duration,
            msas.interrupt_check_time,
            msas.recovery_check_time,
            msas.interrupt_check_duration,
            msas.processing_people,
            msas.processing_time,
            msas.disconnection_cause,
            msas.recovery_error,
            msas.insert_db_time,
            msas.update_db_time,
            wfpd.working_face_type as system_type,
            ms.group_code
        from model_system_alive_statistics msas
            left join model_system ms on msas.system_id = ms.id
            left join cms_mine_name cmn on ms.mine_code = cmn.code
            left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
        <where>
            <if test="query.groupCode != null and query.groupCode != ''">
                and ms.group_code = #{query.groupCode}
            </if>
            <if test="query.mineId != null and query.mineId != ''">
                and msas.mine_id = #{query.mineId}
            </if>
            <if test='(query.groupCode == null or query.groupCode == "") and (query.mineId == null or query.mineId == "")'>
                and cmn."name" not like '%MK'
            </if>
            <if test="query.sid != null and query.sid != ''">
                AND msas.system_id like #{query.sid}||'%'
            </if>
            <if test="query.systemId != null and query.systemId != ''">
                and msas.system_id = #{query.systemId}
            </if>
            <if test="query.systemName != null and query.systemName != ''">
                and ms.name like '%'||#{query.systemName}||'%'
            </if>
            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                and ms.source_system_code = #{query.sourceSystemCode}
            </if>
            <if test="query.systemType != null and query.systemType != ''">
                and wfpd.working_face_type = #{query.systemType}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND msas.disconnection_time <![CDATA[ >= ]]> #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND msas.disconnection_time <![CDATA[ <= ]]> #{query.endTime}
            </if>
            <if test="query.processingPeople != null and query.processingPeople != ''">
                and msas.processing_people = #{query.processingPeople}
            </if>
            <if test="query.isHandle != null and query.isHandle == 1">
                and msas.processing_time is not null
            </if>
            <if test="query.isHandle != null and query.isHandle == 0">
                and msas.processing_time is null
            </if>
        </where>
        order by msas.disconnection_time DESC
    </select>

    <select id="getUpdatedAliveList" resultType="com.bdtd.modules.monitor.entity.ModelSystemAliveStatistics">
        select
            msas.id,
            msas.system_id,
            msas.system_name,
            msas.mine_id,
            msas.disconnection_time,
            msas.recovery_time,
            msas.disconnection_duration,
            msas.interrupt_check_time,
            msas.recovery_check_time,
            msas.interrupt_check_duration,
            msas.processing_people,
            msas.processing_time,
            msas.disconnection_cause,
            msas.recovery_error,
            msas.insert_db_time,
            msas.update_db_time,
            msa.indicator_id,
            msa.time_limit
        from model_system_alive_statistics msas
            join model_system_alive msa on msas.system_id = msa.system_id
            join model_system ms on msas.system_id = ms.id
        <where>
            msa.indicator_id is not null
            <if test="query.sid != null and query.sid != ''">
                AND msas.system_id like #{query.sid}||'%'
            </if>
            <if test="query.systemId != null and query.systemId != ''">
                and msas.system_id = #{query.systemId}
            </if>
            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                and ms.source_system_code = #{query.sourceSystemCode}
            </if>
            <if test='query.startTime != null and query.startTime != "" and query.endTime != null and query.endTime != ""'>
                AND (
                    msas.disconnection_time between #{query.startTime} and #{query.endTime}
                    OR
                    msas.recovery_time between #{query.startTime} and #{query.endTime}
                )
            </if>
            <if test='query.startTime != null and query.startTime != "" and (query.endTime == null or query.endTime == "")'>
                AND (
                    msas.disconnection_time <![CDATA[ >= ]]> #{query.startTime}
                    OR
                    msas.recovery_time <![CDATA[ >= ]]> #{query.startTime}
                )
            </if>
            <if test='query.endTime != null and query.endTime != "" and (query.startTime == null or query.startTime == "")'>
                AND (
                    msas.disconnection_time <![CDATA[ <= ]]> #{query.endTime}
                    OR
                    msas.recovery_time <![CDATA[ <= ]]> #{query.endTime}
                )
            </if>
        </where>
        order by msas.system_id, msas.disconnection_time
    </select>

    <select id="getOfflineRatioPageV2" resultType="com.bdtd.modules.monitor.dto.ModelSystemOfflineRatioDto">
        select
            group_code,
            mine_code,
            group_name,
            mine_name,
            group_sort,
            mine_sort,
            system_id,
            system_name,
            system_type,
            source_system_code,
            "flag",
            flag_text,
            case
                when eval_duration is not null then eval_duration
            <if test="query.queryTimeFallback == null or query.queryTimeFallback == 1">
                when eval_duration is null and true then
            </if>
            <if test="query.queryTimeFallback != null and query.queryTimeFallback != 1">
                when eval_duration is null and false then
            </if>
                    extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')))::numeric
                else null
            end as eval_duration,
            case when interrupt_duration is null then 0 else interrupt_duration end as interrupt_duration,
            case when interrupt_count is null then 0 else interrupt_count end as interrupt_count,
            offline_ratio
        from (
            select
                ms.group_code,
                ms.mine_code,
                cmg."name" as group_name,
                cmn."name" as mine_name,
                cmg."sort" as group_sort,
                cmn."sort" as mine_sort,
                ms."id" as system_id,
                ms."name" as system_name,
                ms.category as system_type,
                ms.source_system_code,
                ms."flag",
                case when ms."flag" = '1' then '正常' else '停止接入' end as flag_text,
                eval_stat.state_duration as eval_duration,
                int_stat.total_exact_int as interrupt_duration,
                int_counter.int_count as interrupt_count,
                case
                    when eval_stat.state_duration is not null then
                        case
                            when int_stat.total_exact_int is null then 0
                            when int_stat.total_exact_int &gt; eval_stat.state_duration then 1
                            else round((int_stat.total_exact_int / eval_stat.state_duration)::numeric, 5)
                        end
                <if test="query.queryTimeFallback == null or query.queryTimeFallback == 1">
                    when eval_stat.state_duration is null and true then
                </if>
                <if test="query.queryTimeFallback != null and query.queryTimeFallback != 1">
                    when eval_stat.state_duration is null and false then
                </if>
                        case
                            when int_stat.total_exact_int is null then 0
                            when int_stat.total_exact_int &gt; extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))) then 1
                            else round((int_stat.total_exact_int / extract(epoch from (TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') - TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))))::numeric, 5)
                        end
                    else null
                end as offline_ratio
            from model_system ms
                join cms_mine_name cmn on ms.mine_code = cmn.code
                join cms_mine_group cmg on ms.group_code = cmg.code
            <if test="query.systemStatus != null and query.systemStatus != ''">
                left join working_face_progress_definition wfpd on wfpd.system_code = ms.id
            </if>
                left join (
                    select
                        work_face_id,
                        work_face_type,
                        sum(extract(epoch from (end_time - begin_time))) as state_duration
					from working_face_event
					where record_type = 'state'
						and event_type = 'life_cycle'
						and (value = '生产' or value = '生产中' or value = '施工中')
						and (
							   begin_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (end_time is null or end_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
							or begin_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
						)
                    group by work_face_id, work_face_type
                ) eval_stat on ms.source_system_code = eval_stat.work_face_id and ms.category = eval_stat.work_face_type
                left join (
                    select
                        system_id,
                        sum(exact_duration) as total_exact_int
                    from (
                        select
                            msas.system_id,
                            case
                                when ed.work_face_id is not null then
                                    extract(epoch from (
                                        case
                                            when msas.recovery_time is null or msas.recovery_time &gt; ed.end_time then ed.end_time
                                            else msas.recovery_time
                                        end
                                        -
                                        case
                                            when msas.disconnection_time &lt; ed.begin_time then ed.begin_time
                                            else msas.disconnection_time
                                        end
                                    ))
                                else
                                    extract(epoch from (
                                        case
                                            when msas.recovery_time is null or msas.recovery_time &gt; TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                                            else msas.recovery_time
                                        end
                                        -
                                        case
                                            when msas.disconnection_time &lt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                                            else msas.disconnection_time
                                        end
                                    ))
                            end as exact_duration
                        from model_system_alive_statistics msas
                            join model_system ms on msas.system_id = ms.id
                            left join (
								select
									work_face_id,
									work_face_type,
									case
										when begin_time &lt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
										else begin_time
									end as begin_time,
									case
										when end_time is null or end_time &gt; TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') then TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
										else end_time
									end as end_time
								from working_face_event
								where record_type = 'state'
									and event_type = 'life_cycle'
									and (value = '生产' or value = '生产中' or value = '施工中')
									and (
										   begin_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (end_time is null or end_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
										or begin_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
									)
							)
							ed on ms.source_system_code = ed.work_face_id
                                and ms.category = ed.work_face_type
                                and (
                                       msas.disconnection_time &lt;= ed.begin_time and (msas.recovery_time is null or msas.recovery_time &gt; ed.begin_time)
                                    or msas.disconnection_time between ed.begin_time and ed.end_time
                                )
                        where
                            (
                                   msas.disconnection_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (msas.recovery_time is null or msas.recovery_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                                or msas.disconnection_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                            )
                            <if test="query.groupCode != null and query.groupCode != ''">
                            and ms.group_code = #{query.groupCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.mineCode != null and query.mineCode != ''">
                            and ms.mine_code = #{query.mineCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.sid != null and query.sid != ''">
                            and ms.sid = #{query.sid,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemId != null and query.systemId != ''">
                            and ms.id = #{query.systemId,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemName != null and query.systemName != ''">
                            and ms."name" like concat('%',#{query.systemName,jdbcType=VARCHAR}::text,'%')
                            </if>
                            <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                            and ms.source_system_code = #{query.sourceSystemCode,jdbcType=VARCHAR}
                            </if>
                            <if test="query.systemType != null and query.systemType != ''">
                            and ms.category = #{query.systemType,jdbcType=VARCHAR}
                            </if>
                    ) int_details
                    group by system_id
                ) int_stat on ms.id = int_stat.system_id
                left join (
                    select
                        system_id,
                        count(1) as int_count
                    from model_system_alive_statistics
                    where (
                           disconnection_time &lt;= TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and (recovery_time is null or recovery_time &gt; TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'))
                        or disconnection_time between TO_TIMESTAMP(#{query.startTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{query.endTime,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
                    )
                    group by system_id
                ) int_counter on ms.id = int_counter.system_id
            <where>
                <if test="query.groupCode != null and query.groupCode != ''">
                and ms.group_code = #{query.groupCode,jdbcType=VARCHAR}
                </if>
                <if test="query.mineCode != null and query.mineCode != ''">
                and ms.mine_code = #{query.mineCode,jdbcType=VARCHAR}
                </if>
                <if test="query.sid != null and query.sid != ''">
                and ms.sid = #{query.sid,jdbcType=VARCHAR}
                </if>
                <if test="query.systemId != null and query.systemId != ''">
                and ms.id = #{query.systemId,jdbcType=VARCHAR}
                </if>
                <if test="query.systemName != null and query.systemName != ''">
                and ms."name" like concat('%',#{query.systemName,jdbcType=VARCHAR}::text,'%')
                </if>
                <if test="query.sourceSystemCode != null and query.sourceSystemCode != ''">
                and ms.source_system_code = #{query.sourceSystemCode,jdbcType=VARCHAR}
                </if>
                <if test="query.systemType != null and query.systemType != ''">
                and ms.category = #{query.systemType,jdbcType=VARCHAR}
                </if>
                <if test="query.systemStatus != null and query.systemStatus != ''">
                and wfpd.work_state = #{query.systemStatus,jdbcType=VARCHAR}
                </if>
                <if test="query.ignoreUnnecessary != null and query.ignoreUnnecessary == 1">
                and eval_stat.state_duration is not null
                </if>
            </where>
        ) m
    </select>
</mapper>
