<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.AccessNodeSettingMapper">

    <select id="getAccessNodeOptionSettingFullList"
            parameterType="com.bdtd.modules.monitor.dto.AccessNodeQueryEntity"
            resultType="com.bdtd.modules.monitor.vo.AccessNodeSettingFullVo">
        select
            dano.node_type,
            dano.code,
            dano."name",
            dano.config_help,
            dano.config_type,
            dano.select_options,
            dano.input_length,
            dano.value_prefix,
            dano.value_postfix,
            dano.valid_regex,
            dano.error_tips,
            dano.default_value,
            dano.require_flag,
            dano.const_flag,
            dano.sort,
            dans.node_id,
            dans.id as node_setting_id,
            dans.node_option_id,
            dans.value,
            dans.memo,
            dans.created_at,
            dans.updated_at
        from data_access_node_option dano
            left join data_access_node_setting dans on dano.id = dans.node_option_id
            left join data_access_node dan on dans.node_id = dan.id
            left join data_access_model_system_node damsn on dan.id = damsn.node_id
        <where>
            <if test="nodeOptionId != null">
                and dano.id = #{nodeOptionId,jdbcType=INTEGER}
            </if>
            <if test="nodeOptionCode != null and nodeOptionCode != ''">
                and dano.code = #{nodeOptionCode,jdbcType=VARCHAR}
            </if>
            <if test="nodeOptionName != null and nodeOptionName != ''">
                and dano.name = #{nodeOptionName,jdbcType=VARCHAR}
            </if>
            <if test="nodeSettingId != null">
                and dans.id = #{nodeSettingId,jdbcType=INTEGER}
            </if>
            <if test="nodeId != null">
                and dans.node_id = #{nodeId,jdbcType=VARCHAR}
            </if>
            <if test="nodeTitle != null and nodeTitle != ''">
                and dan.title like concat('%', #{nodeTitle,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                and dan.node_type = #{nodeType,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and dan.business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="detectMode != null">
                and dan.detect_mode = #{detectMode,jdbcType=INTEGER}
            </if>
            <if test="requireFlag != null">
                and dan.require_flag = #{requireFlag,jdbcType=INTEGER}
            </if>
            <if test="constFlag != null">
                and dan.const_flag = #{constFlag,jdbcType=INTEGER}
            </if>
            <if test="systemCode != null and systemCode != ''">
                and damsn.system_code = #{systemCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getAccessNodeOptionSettingList"
            parameterType="com.bdtd.modules.monitor.dto.AccessNodeQueryEntity"
            resultType="com.bdtd.modules.monitor.vo.AccessNodeSettingVo">
        select
            dans.node_id,
            dans.id as node_setting_id,
            dans.node_option_id,
            dano.node_type,
            dano.code,
            dano."name",
            dano.default_value,
            dans.value,
            dans.memo,
            dano.sort
        from data_access_node_option dano
            left join data_access_node_setting dans on dano.id = dans.node_option_id
            left join data_access_node dan on dans.node_id = dan.id
            left join data_access_model_system_node damsn on dan.id = damsn.node_id
        <where>
            <if test="nodeOptionId != null">
                and dano.id = #{nodeOptionId,jdbcType=INTEGER}
            </if>
            <if test="nodeOptionCode != null and nodeOptionCode != ''">
                and dano.code = #{nodeOptionCode,jdbcType=VARCHAR}
            </if>
            <if test="nodeOptionName != null and nodeOptionName != ''">
                and dano.name = #{nodeOptionName,jdbcType=VARCHAR}
            </if>
            <if test="nodeSettingId != null">
                and dans.id = #{nodeSettingId,jdbcType=INTEGER}
            </if>
            <if test="nodeId != null">
                and dans.node_id = #{nodeId,jdbcType=VARCHAR}
            </if>
            <if test="nodeTitle != null and nodeTitle != ''">
                and dan.title like concat('%', #{nodeTitle,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                and dan.node_type = #{nodeType,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and dan.business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="detectMode != null">
                and dan.detect_mode = #{detectMode,jdbcType=INTEGER}
            </if>
            <if test="requireFlag != null">
                and dan.require_flag = #{requireFlag,jdbcType=INTEGER}
            </if>
            <if test="constFlag != null">
                and dan.const_flag = #{constFlag,jdbcType=INTEGER}
            </if>
            <if test="systemCode != null and systemCode != ''">
                and damsn.system_code = #{systemCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
