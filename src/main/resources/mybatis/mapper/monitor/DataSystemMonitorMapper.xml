<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.DataSystemMonitorMapper">

    <resultMap id="deptStateResultMap" type="com.bdtd.modules.monitor.dto.SystemMonitorDeptDto">
        <result column="group_code" jdbcType="VARCHAR" property="groupCode"/>
        <result column="group_sort" jdbcType="INTEGER" property="groupSort"/>
        <result column="mine_code" jdbcType="VARCHAR" property="mineCode"/>
        <result column="mine_sort" jdbcType="INTEGER" property="mineSort"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
        <result column="online" jdbcType="INTEGER" property="online"/>
        <result column="offline" jdbcType="INTEGER" property="offline"/>
        <result column="last_upload_date" jdbcType="TIMESTAMP" property="lastUploadDate"/>
    </resultMap>

    <resultMap id="companyStateResultMap" type="com.bdtd.modules.monitor.dto.SystemMonitorCompanyDto">
        <result column="group_code" jdbcType="VARCHAR" property="groupCode"/>
        <result column="group_sort" jdbcType="INTEGER" property="groupSort"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
        <result column="online" jdbcType="INTEGER" property="online"/>
        <result column="offline" jdbcType="INTEGER" property="offline"/>
        <result column="last_upload_date" jdbcType="TIMESTAMP" property="lastUploadDate"/>
    </resultMap>

    <select id="queryPage" resultType="com.bdtd.modules.monitor.entity.DataSystemMonitor">
        select *
        from (
            select
                dsm.*,
                wfpd.working_face_type as system_type,
                ms.source_system_code,
                ms.category,
                ms.flag,
                cmn."name" as mine_name,
                cmn."sort" as mine_sort,
                cmg."sort" as group_sort
            from data_system_monitor dsm
                join model_system ms on dsm.system_code = ms.id
                left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
                left join cms_mine_name cmn on cmn."code" = ms.mine_code
                left join cms_mine_group cmg on cmg."code" = cmn.group_code
        ) m
        ${ew.customSqlSegment}
    </select>

    <select id="queryCount" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from (
             select
                 dsm.*,
                 wfpd.working_face_type as system_type,
                 ms.source_system_code,
                 cmn."name" as category,
                 cmn."name" as mine_name,
                 ms.flag
             from data_system_monitor dsm
                  join model_system ms on dsm.system_code = ms.id
                  left join working_face_progress_definition wfpd on ms.source_system_code = wfpd.working_face_id
                  left join cms_mine_name cmn on ms.mine_code = cmn.code
         ) m
        ${ew.customSqlSegment}
    </select>

    <update id="updateUploadStatus">
        update data_system_monitor
        set upload_status = '0'
        where upload_status = '1'
          and next_upload_date <![CDATA[>]]> #{nowDate};
        update data_system_monitor
        set status = '0'
        where next_upload_date <![CDATA[<]]> #{nowDate};
    </update>

    <update id="updateSystemMonitor">
        update data_system_monitor
        set status = #{status},
            last_upload_date = #{lastUploadDate},
            upload_status = '1'
        where system_code = #{systemCode};
    </update>

    <update id="clearNextUploadDate">
        update data_system_monitor
        set next_upload_date = null
        <where>
            <if test="systemCodes != null and systemCodes.size > 0">
                system_code in <foreach collection="systemCodes" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
        </where>
    </update>

    <insert id="upsertList">
        insert into data_system_monitor
            (system_name, system_code, "status", last_upload_date, next_upload_date, upload_status, group_code, mine_code)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.systemName,jdbcType=VARCHAR},
            #{item.systemCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},
            #{item.lastUploadDate,jdbcType=TIMESTAMP},
            #{item.nextUploadDate,jdbcType=TIMESTAMP},
            #{item.uploadStatus,jdbcType=INTEGER},
            #{item.groupCode,jdbcType=VARCHAR},
            #{item.mineCode,jdbcType=VARCHAR}
            )
        </foreach>
        ON conflict(system_code) do update set
            system_name = excluded.system_name,
            "status" = excluded.status,
            last_upload_date = excluded.last_upload_date,
            next_upload_date = excluded.next_upload_date,
            upload_status = excluded.upload_status,
            group_code = excluded.group_code,
            mine_code = excluded.mine_code
    </insert>

    <delete id="deleteNotExistedSystemMonitor">
        DELETE FROM data_system_monitor
        WHERE NOT EXISTS (
          SELECT 1
          FROM model_system
          WHERE data_system_monitor.system_code = model_system.id
        )
    </delete>

    <select id="getStatisticsListOfDept" resultMap="deptStateResultMap">
        select *
        from (
            select
                group_code,
                group_name,
                group_sort,
                mine_code,
                mine_name,
                mine_sort,
                case
                    when total = 0 then 9
                    when total != 0 and total = offline then 1
                    else 0
                end as status,
                total,
                online,
                offline,
                last_upload_date
            from (
                select
                    cmn."code" as mine_code,
                    max(cmn."name") as mine_name,
                    max(cmn.sort) as mine_sort,
                    count(ms.id) as total,
                    sum(case when dsm.status = 0 then 1 else 0 end) as online,
                    sum(case when dsm.status = 1 then 1 else 0 end) as offline,
                    max(dsm.last_upload_date) as last_upload_date,
                    max(cmn.group_code) as group_code,
                    max(cmg."name") as group_name,
                    max(cmg.sort) as group_sort
                from cms_mine_name cmn
                    join cms_mine_group cmg on cmg."code" = cmn.group_code
                    left join model_system ms on ms.mine_code = cmn."code"
                        and ms.flag = '1'
                    <if test='sid != null and sid != ""'>
                        and ms.id like CONCAT(#{sid,jdbcType=VARCHAR}::text, '%')
                    </if>
                    left join data_system_monitor dsm on dsm.system_code = ms.id
                ${ew.customSqlSegment}
                group by cmn."code"
            ) m
        ) g
        <if test="status != null">
            where status = #{status}
        </if>
    </select>

    <select id="getStatisticsListOfCompany" resultType="com.bdtd.modules.monitor.dto.SystemMonitorCompanyDto">
        select *
        from (
            select
                group_code,
                count(1) as total,
                sum(case when status = 0 then 1 else 0 end) as online,
                sum(case when status = 1 then 1 else 0 end) as offline,
                sum(case when status = 9 then 1 else 0 end) as unmonitored,
                max(last_upload_date) as last_upload_date,
                max(group_sort) as group_sort
            from (
                select
                    group_code,
                    group_sort,
                    mine_code,
                    mine_sort,
                    case
                        when total = 0 then 9
                        when total != 0 and total = offline then 1
                        else 0
                    end as status,
                    total,
                    online,
                    offline,
                    last_upload_date
                from (
                    select
                        cmn."code" as mine_code,
                        max(cmn.sort) as mine_sort,
                        count(ms.id) as total,
                        sum(case when dsm.status = 0 then 1 else 0 end) as online,
                        sum(case when dsm.status = 1 then 1 else 0 end) as offline,
                        max(dsm.last_upload_date) as last_upload_date,
                        max(cmn.group_code) as group_code,
                        max(cmg.sort) as group_sort
                    from cms_mine_name cmn
                        join cms_mine_group cmg on cmg."code" = cmn.group_code
                        left join model_system ms on ms.mine_code = cmn."code"
                            and ms.flag = '1'
                        <if test='sid != null and sid != ""'>
                            AND (ms.sid = #{sid,jdbcType=VARCHAR} or ms.id like CONCAT(#{sid,jdbcType=VARCHAR}::text, '%'))
                        </if>
                        left join data_system_monitor dsm on dsm.system_code = ms.id
                    <where>
                        <if test='groupCode != null and groupCode != ""'>
                            AND cmn.group_code = #{groupCode,jdbcType=VARCHAR}
                        </if>
                        <if test='groupCode == null or groupCode == ""'>
                            AND cmn."name" not like '%MK'
                        </if>
                        <if test="mineCodes != null and mineCodes.size > 0">
                            and cmn."code" in
                            <foreach collection="mineCodes" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    </where>
                    group by cmn."code"
                ) m
            ) g
            group by group_code
        ) t
    </select>

    <select id="getStatisticsByDept" resultType="com.bdtd.modules.monitor.dto.SystemMonitorDeptStatisticDto">
        select
            count(1) as total,
            sum(case when status = 0 then 1 else 0 end) as online,
            sum(case when status = 1 then 1 else 0 end) as offline,
            sum(case when status = 9 then 1 else 0 end) as unmonitored
        from (
            select
                mine_code,
                mine_sort,
                case
                    when total = 0 then 9
                    when total != 0 and total = offline then 1
                    else 0
                end as status
            from (
                select
                    cmn."code" as mine_code,
                    max(cmn."sort") as mine_sort,
                    count(ms.id) as total,
                    sum(case when dsm.status = 0 then 1 else 0 end) as online,
                    sum(case when dsm.status = 1 then 1 else 0 end) as offline
                from cms_mine_name cmn
                    join cms_mine_group cmg on cmg."code" = cmn.group_code
                    left join model_system ms on ms.mine_code = cmn."code"
                        and ms.flag = '1'
                    <if test='sid != null and sid != ""'>
                        and ms.id like CONCAT(#{sid,jdbcType=VARCHAR}::text, '%')
                    </if>
                    left join data_system_monitor dsm on dsm.system_code = ms.id
                ${ew.customSqlSegment}
                group by cmn."code"
            ) m
        ) g
    </select>

</mapper>