<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.ModelSystemAliveMapper">
    <select id="selectLastTime" resultType="java.time.LocalDateTime">
        SELECT ${dateName} as latest_time
        FROM ${updateTable}
        <if test="mineCode != null and mineCode != ''">
            WHERE mine_code = #{mineCode}
        </if>
        ORDER BY ${dateName} DESC
        LIMIT 1
    </select>

    <select id="selectLastTimeList"
            parameterType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckParam"
            resultType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckResult">
        select
            n.latest_time,
            n.mine_code,
            ms.id as system_id
        from (
                select
                    max(${dateName}) as latest_time,
                    mine_code
                from ${updateTable}
                group by mine_code
            ) n
            left join model_system ms
                on n.mine_code = ms.mine_code and ms.update_table = '${updateTable}'
    </select>

    <select id="selectLastTimeLikeSystemId" resultType="java.time.LocalDateTime">
        SELECT ${dateName} as latest_time
        FROM ${updateTable}
        <where>
            point_id similar to '%${systemId}(.|-)%'
            <if test="mineCode != null and mineCode != ''">
                AND mine_code = #{mineCode}
            </if>
        </where>
        ORDER BY ${dateName} DESC
        LIMIT 1
    </select>

    <select id="selectLastTimeListLikeSystemId"
            parameterType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckParam"
            resultType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckResult">
        select
            max(latest_time) as latest_time,
            system_id,
            mine_code
        from (
            select
                ${dateName} as latest_time,
                substring(point_id, 0, 7) as system_id,
                mine_code
            from ${updateTable}
        ) n
        group by mine_code, system_id
    </select>

    <select id="selectLastTimeBySystemId" resultType="java.time.LocalDateTime">
        SELECT ${dateName} as latest_time
        FROM ${updateTable}
        <where>
            <if test="systemIdField != null and systemIdField != '' and systemId != null and systemId != ''">
                AND ${systemIdField} = #{systemId}
            </if>
            <if test="mineCode != null and mineCode != ''">
                AND mine_code = #{mineCode}
            </if>
        </where>
        ORDER BY ${dateName} DESC
        LIMIT 1
    </select>

    <select id="selectLastTimeListBySystemId"
            parameterType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckParam"
            resultType="com.bdtd.modules.monitor.dto.ModelSystemAliveCheckResult">
        select
            max(${dateName}) as latest_time,
            ${systemIdField} as system_id,
            mine_code
        from ${updateTable}
        group by mine_code, ${systemIdField}
    </select>

    <insert id="upsertBatch">
        insert into model_system_alive (
            parent_id,
            system_id,
            system_name,
            update_table,
            date_name,
            time_limit,
            indicator_id,
            mine_code,
            group_code,
            mine_id,
            system_id_field,
            system_id_mode,
            updated_user,
            updated_at
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.parentId, jdbcType=VARCHAR},
            #{item.systemId, jdbcType=VARCHAR},
            #{item.systemName, jdbcType=VARCHAR},
            #{item.updateTable, jdbcType=VARCHAR},
            #{item.dateName, jdbcType=VARCHAR},
            #{item.timeLimit, jdbcType=INTEGER},
            #{item.indicatorId, jdbcType=VARCHAR},
            #{item.mineCode, jdbcType=VARCHAR},
            #{item.groupCode, jdbcType=VARCHAR},
            #{item.mineId, jdbcType=BIGINT},
            #{item.systemIdField, jdbcType=VARCHAR},
            #{item.systemIdMode, jdbcType=INTEGER},
            #{item.updatedUser, jdbcType=VARCHAR},
            now()
        )
        </foreach>
        ON conflict(system_id) do update set
            parent_id = excluded.parent_id,
            system_name = excluded.system_name,
            group_code = excluded.group_code,
            mine_code = excluded.mine_code,
            mine_id = excluded.mine_id,
            system_id_field = excluded.system_id_field,
            system_id_mode = excluded.system_id_mode,
            updated_user = excluded.updated_user,
            updated_at = now()
    </insert>

    <select id="queryEnabledList" resultType="com.bdtd.modules.monitor.entity.ModelSystemAlive">
        SELECT
            msa.parent_id,
            msa.system_id,
            ms."name" as system_name,
            msa.mine_id,
            msa.mine_code,
            msa.group_code,
            msa.indicator_id,
            msa.time_limit,
            msa.update_table,
            msa.date_name,
            msa.system_id_field,
            msa.system_id_mode,
            msa.updated_user,
            msa.updated_at,
            ms.enabled_at,
            case when ms.update_collect_period is null then 0 else ms.update_collect_period end as update_collect_period
        FROM model_system_alive msa
            JOIN model_system ms on msa.system_id = ms.id
        WHERE ms.flag = '1'
    </select>

    <select id="queryEnabledAndOnlineList" resultType="com.bdtd.modules.monitor.entity.ModelSystemAlive">
        SELECT 
            msa.parent_id,
            msa.system_id,
            ms."name" as system_name,
            msa.mine_id,
            msa.mine_code,
            msa.group_code,
            msa.indicator_id,
            msa.time_limit,
            msa.update_table,
            msa.date_name,
            msa.system_id_field,
            msa.system_id_mode,
            msa.updated_user,
            msa.updated_at,
            ms.enabled_at,
            case when ms.update_collect_period is null then 0 else ms.update_collect_period end as update_collect_period
        FROM model_system_alive msa
            JOIN model_system ms on msa.system_id = ms.id
        WHERE ms.flag = '1' AND ms.route_service = 1
    </select>

</mapper>
