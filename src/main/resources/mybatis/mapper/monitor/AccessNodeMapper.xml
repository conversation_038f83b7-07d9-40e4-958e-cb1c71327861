<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.monitor.mapper.AccessNodeMapper">

    <select id="findAccessPathState" resultType="com.bdtd.modules.monitor.vo.AccessNodeState">
        select
            danh.node_id,
            dan.title as node_title,
            dan.parent_id,
            danh.offline as abnormal_state,
            danh.hb_time as detect_time,
            dan.suggestions,
            danh.memo,
            coalesce(damsn.flow_order, dan.flow_order) as flow_order
        from data_access_node_heartbeat danh
            join data_access_node dan on dan.id = danh.node_id
            left join data_access_model_system_node damsn on damsn.node_id = danh.node_id
        <where>
            dan.business_type = 0
            <if test="systemCode != null and systemCode != ''">
                or damsn.system_code = #{systemCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by flow_order desc
    </select>

    <select id="findSystemNodeList"
            parameterType="com.bdtd.modules.monitor.dto.AccessNodeQueryEntity"
            resultType="com.bdtd.modules.monitor.vo.AccessNodeVo">
        select
            dan.id,
            dan.parent_id,
            dan.title,
            dan.node_type,
            dan.business_type,
            dan.detect_mode,
            dan.time_limit,
            dan.indicator_id,
            dan.indicator_updated_at,
            dan.suggestions,
            dan.memo,
            coalesce(damsn.flow_order, dan.flow_order) as flow_order,
            case when danp.parent_id is not null then true else false end as has_children,
            dan.created_at,
            dan.updated_at,
            damsn.mine_code,
            damsn.system_code
        from data_access_node dan
            left join data_access_model_system_node damsn on dan.id = damsn.node_id
            left join model_system ms on damsn.system_code = ms.id
            left join (
                select distinct parent_id
                from data_access_node
            ) danp on danp.parent_id = dan.id
        <where>
            <if test="parentId != null and parentId != ''">
                and dan.parent_id = #{parentId,jdbcType=VARCHAR}
            </if>
            <if test="hasChildren != null and hasChildren == true">
                and exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
            </if>
            <if test="hasChildren != null and hasChildren == false">
                and not exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
            </if>
            <if test="nodeTitle != null and nodeTitle != ''">
                and dan.title like concat('%', #{nodeTitle,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                and dan.node_type = #{nodeType,jdbcType=VARCHAR}
            </if>
            <if test="detectMode != null">
                and dan.detect_mode = #{detectMode,jdbcType=INTEGER}
            </if>
            <if test="businessType != null">
                and dan.business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="enabled != null">
                and dan.enabled = #{enabled,jdbcType=INTEGER}
            </if>
            <if test="systemCode != null and systemCode != ''">
                and damsn.system_code = #{systemCode,jdbcType=VARCHAR}
            </if>
            <if test="systemName != null and systemName != ''">
                and ms.name like concat('%', #{systemName,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="mineCode != null and mineCode != ''">
                and ms.mine_code = #{mineCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by flow_order desc
    </select>

    <select id="findList"
            parameterType="com.bdtd.modules.monitor.dto.AccessNodeQueryEntity"
            resultType="com.bdtd.modules.monitor.vo.AccessNodeVo">
        select
            dan.id,
            dan.parent_id,
            dan.title,
            dan.node_type,
            dan.business_type,
            dan.detect_mode,
            dan.suggestions,
            dan.memo,
            coalesce(damsn.flow_order, dan.flow_order) as flow_order,
            case when danp.parent_id is not null then true else false end as has_children,
            dan.created_at,
            dan.updated_at,
            damsn.mine_code,
            damsn.system_code
        from data_access_node dan
            left join data_access_model_system_node damsn on dan.id = damsn.node_id
            left join model_system ms on damsn.system_code = ms.id
            left join (
                select distinct parent_id
                from data_access_node
            ) danp on danp.parent_id = dan.id
        <where>
            <if test="parentId != null and parentId != ''">
                and dan.parent_id = #{parentId,jdbcType=VARCHAR}
            </if>
            <if test="hasChildren != null and hasChildren == true">
                and exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
            </if>
            <if test="hasChildren != null and hasChildren == false">
                and not exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
            </if>
            <if test="nodeTitle != null and nodeTitle != ''">
                and dan.title like concat('%', #{nodeTitle,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                and dan.node_type = #{nodeType,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and dan.business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="detectMode != null">
                and dan.detect_mode = #{detectMode,jdbcType=INTEGER}
            </if>
            <if test="systemCode != null and systemCode != ''">
                and damsn.system_code = #{systemCode,jdbcType=VARCHAR}
            </if>
            <if test="systemName != null and systemName != ''">
                and ms.name like concat('%', #{systemName,jdbcType=VARCHAR}::text, '%')
            </if>
            <if test="mineCode != null and mineCode != ''">
                and ms.mine_code = #{mineCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="findPage" resultType="com.bdtd.modules.monitor.vo.AccessNodeVo">
        select *
        from (
            select
                dan.id,
                dan.parent_id,
                dan.title,
                dan.node_type,
                dan.business_type,
                dan.detect_mode,
                dan.suggestions,
                dan.memo,
                coalesce(damsn.flow_order, dan.flow_order) as flow_order,
                case when danp.parent_id is not null then true else false end as has_children,
                dan.created_at,
                dan.updated_at,
                damsn.mine_code,
                damsn.system_code
            from data_access_node dan
                left join data_access_model_system_node damsn on dan.id = damsn.node_id
                left join model_system ms on damsn.system_code = ms.id
                left join (
                    select distinct parent_id
                    from data_access_node
                ) danp on danp.parent_id = dan.id
            <where>
                <if test="queryEntity.parentId != null and queryEntity.parentId != ''">
                    and dan.parent_id = #{queryEntity.parentId,jdbcType=VARCHAR}
                </if>
                <if test="queryEntity.hasChildren != null and queryEntity.hasChildren == true">
                    and exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
                </if>
                <if test="queryEntity.hasChildren != null and queryEntity.hasChildren == false">
                    and not exists ( select 1 from data_access_node dan2 where dan2.parent_id = dan.id )
                </if>
                <if test="queryEntity.nodeTitle != null and queryEntity.nodeTitle != ''">
                    and dan.title like concat('%', #{queryEntity.nodeTitle,jdbcType=VARCHAR}::text, '%')
                </if>
                <if test="queryEntity.nodeType != null and queryEntity.nodeType != ''">
                    and dan.node_type = #{queryEntity.nodeType,jdbcType=VARCHAR}
                </if>
                <if test="queryEntity.businessType != null">
                    and dan.business_type = #{queryEntity.businessType,jdbcType=INTEGER}
                </if>
                <if test="queryEntity.detectMode != null">
                    and dan.detect_mode = #{queryEntity.detectMode,jdbcType=INTEGER}
                </if>
                <if test="queryEntity.systemCode != null and queryEntity.systemCode != ''">
                    and damsn.system_code = #{queryEntity.systemCode,jdbcType=VARCHAR}
                </if>
                <if test="queryEntity.systemName != null and queryEntity.systemName != ''">
                    and ms.name like concat('%', #{queryEntity.systemName,jdbcType=VARCHAR}::text, '%')
                </if>
                <if test="queryEntity.mineCode != null and queryEntity.mineCode != ''">
                    and ms.mine_code = #{queryEntity.mineCode,jdbcType=VARCHAR}
                </if>
            </where>
        ) m
    </select>

    <select id="findLatestTime" resultType="java.sql.Timestamp">
        SELECT ${fieldName} as latest_time
        FROM ${tableName}
        <if test="keyField != null and keyField != '' and keyValue != null and keyValue != ''">
            WHERE ${keyField} = #{keyValue,jdbcType=VARCHAR}
        </if>
        ORDER BY ${fieldName} DESC NULLS LAST
        LIMIT 1
    </select>

    <select id="findBatchLatestTime" resultType="com.bdtd.modules.monitor.dto.AccessNodeHeartbeatItem">
        SELECT
            ${keyField} as key_field,
            max(${fieldName}) as latest_time
        FROM ${tableName}
        <if test="keyField != null and keyField != '' and keyValueList != null and keyValueList.size > 0">
            WHERE ${keyField} IN <foreach collection="keyValueList" item="item" index="i" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
        </if>
        GROUP BY ${keyField}
    </select>

    <insert id="generatePositionCollectServiceNodes">
        INSERT INTO data_access_node
            (id, parent_id, title, node_type, business_type, detect_mode, time_limit, suggestions, flow_order, created_at, updated_at)
        select
            id,
            parent_id,
            title,
            node_type,
            business_type,
            detect_mode,
            time_limit,
            suggestions,
            flow_order,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        from (
            select
                concat(source_system_code, '01') as id,
                0::int8 as parent_id,
                concat(category, '定位采集接口') as title,
                'heartbeat'::text as node_type,
                1::int2 as business_type,
                1::int2 as detect_mode,
                600::int4 as time_limit,
                '请联系实施人员排查矿端上传链路'::text as suggestions,
                399::int4 as flow_order,
                ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
            from model_system ms
            where sid = 991
        ) m
        where rank = 1
        ON CONFLICT(id) do nothing
    </insert>

    <insert id="generatePositionCollectServerNodes">
        INSERT INTO data_access_node
            (id, parent_id, title, node_type, business_type, detect_mode, time_limit, suggestions, flow_order, created_at, updated_at)
        select
            id,
            parent_id,
            title,
            node_type,
            business_type,
            detect_mode,
            time_limit,
            suggestions,
            flow_order,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        from (
            select
                concat(source_system_code, '03') as id,
                0::int8 as parent_id,
                concat(category, '定位上位机') as title,
                'heartbeat'::text as node_type,
                3::int2 as business_type,
                1::int2 as detect_mode,
                600::int4 as time_limit,
                '请联系实施人员排查矿端上传链路'::text as suggestions,
                599::int4 as flow_order,
                ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
            from model_system ms
            where sid = 991
        ) m
        where rank = 1
        ON CONFLICT(id) do nothing
    </insert>

    <insert id="generatePositionCollectNodeSettings">
        INSERT INTO data_access_node_setting (node_id, node_option_id, value, memo)
        select
            dan.id as node_id,
            dano.id as node_option_id,
            case
                when dan.business_type = 1 and dano.code = 'table_name' then 'working_face_progress_mine_heartbeat'
                when dan.business_type = 1 and dano.code = 'field_name' then 'service_last_online_time'
                when dan.business_type = 1 and dano.code = 'key_field' then 'mine_code'
                when dan.business_type = 1 and dano.code = 'period' then 600::text
                when dan.business_type = 3 and dano.code = 'table_name' then 'working_face_progress_mine_heartbeat'
                when dan.business_type = 3 and dano.code = 'field_name' then 'connect_last_online_time'
                when dan.business_type = 3 and dano.code = 'key_field' then 'mine_code'
                when dan.business_type = 3 and dano.code = 'period' then 600::text
                else null::text
                end::text as value,
                concat(dan.title, ' ', dano.name)::text as memo
        from data_access_node dan
            join data_access_node_option dano on dan.node_type = dano.node_type
            join (
                select system_code
                from (
                     select
                         concat(source_system_code, '01') as system_code,
                         ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
                     from model_system ms
                     where sid = 991
                ) m
                where rank = 1
                UNION
                select system_code
                from (
                         select
                             concat(source_system_code, '03') as system_code,
                             ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
                         from model_system ms
                         where sid = 991
                     ) m
                where rank = 1
            ) ms on dan.id = ms.system_code
        ON CONFLICT(node_id, node_option_id) do nothing
    </insert>

    <insert id="generatePositionCollectNodeRelations">
        INSERT INTO data_access_model_system_node
            (mine_code, system_code, node_id, detect_mode, flow_order, memo, created_at, updated_at)
        select
            mine_code,
            system_code,
            dan.id as node_id,
            1::int2,
            dan.flow_order,
            '自动创建'::text,
            current_timestamp,
            current_timestamp
        from (
            select
                id as system_code,
                concat(source_system_code, '01') as node_id,
                mine_code,
                ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
            from model_system ms
            where sid = 991
            UNION
            select
                id as system_code,
                concat(source_system_code, '03') as node_id,
                mine_code,
                ROW_NUMBER() OVER (partition BY mine_code ORDER BY created_at) as rank
            from model_system ms
            where sid = 991
        ) m join data_access_node dan on m.node_id = dan.id and m.rank = 1
        ON CONFLICT(mine_code, system_code, node_id) do nothing
    </insert>

</mapper>
