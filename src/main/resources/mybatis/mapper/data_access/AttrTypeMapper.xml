<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.AttrTypeMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.data_access.entity.AttrType">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <select id="select" parameterType="String" resultType="java.util.Map">
    ${value}
  </select>

  <update id="update" parameterType="String" >
    ${value}
  </update>




</mapper>