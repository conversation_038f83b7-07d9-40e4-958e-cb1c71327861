<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.SystemModelMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.data_access.entity.SystemModel">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="conf_table" jdbcType="VARCHAR" property="confTable"/>
        <result column="update_table" jdbcType="VARCHAR" property="updateTable"/>
        <result column="history_table" jdbcType="VARCHAR" property="historyTable"/>
        <result column="definition_routing_key" jdbcType="VARCHAR" property="definitionRoutingKey"/>
        <result column="update_routing_key" jdbcType="VARCHAR" property="updateRoutingKey"/>
        <result column="exchange" jdbcType="VARCHAR" property="exchange"/>
        <result column="conf_table_struct" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="confTableStruct"/>
        <result column="flag" jdbcType="VARCHAR" property="flag"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <resultMap id="BaseResultMapNoTable" type="com.bdtd.modules.data_access.dto.SystemModelNoTable">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="flag" jdbcType="VARCHAR" property="flag"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        id, name, conf_table, update_table, history_table, flag, created_at, updated_at
    </sql>

    <select id="selectByExample" parameterType="com.bdtd.modules.data_access.dto.SystemModelExample" resultMap="BaseResultMapNoTable">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from model_system
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <insert id="insert" parameterType="com.bdtd.modules.data_access.dto.SystemModelNoTable">
        insert into model_system (id, name)
        values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.data_access.dto.SystemModelNoTable">
        update model_system
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from model_system
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="selectSystemModel" parameterType="String" resultMap="BaseResultMap">
        select *
        from model_system
        <if test="id != null">
            where id = #{id,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectById" parameterType="String" resultType="com.bdtd.modules.data_access.entity.SystemModel">
        select *
        from model_system
        <if test="id != null">
            where id = #{id,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectBySystemModel" resultMap="BaseResultMap">
        select *
        from model_system
        where 1 = 1
        <if test="id != null">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="routeService != null and routeService != ''">
            and route_service = #{routeService}
        </if>
        <if test="warnService != null and warnService != ''">
            and warn_service = #{warnService}
        </if>
        <if test="converterService != null and converterService != ''">
            and converter_service = #{converterService}
        </if>
        <if test="accessConfigurable != null and accessConfigurable != ''">
            and access_configurable = #{accessConfigurable}
        </if>
        <if test="exchange != null and exchange != ''">
            and exchange = #{exchange}
        </if>
        order by name ASC
    </select>
</mapper>