<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.ChildSystemMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.data_access.entity.ChildSystem">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="topo_config" typeHandler="com.bdtd.util.sql.ArrayJsonHandler" property="topoConfig" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, topo_config, image_path, sort_num, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.data_access.dto.ChildSystemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_child_system
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="insert" parameterType="com.bdtd.modules.data_access.entity.ChildSystem">
    insert into data_child_system (id, name, topo_config,
      image_path, sort_num)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{topoConfig,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
    #{imagePath,jdbcType=VARCHAR},#{sortNum,jdbcType=INTEGER})
  </insert>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from data_child_system
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.data_access.entity.ChildSystem">
    update data_child_system
    <set>
      <if test=" name != null ">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="topoConfig != null ">
        topo_config = #{topoConfig,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null ">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="imagePath != null ">
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

</mapper>