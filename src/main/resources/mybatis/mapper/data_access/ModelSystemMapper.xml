<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.ModelSystemMapper">
    <insert id="upsertBatch">
        INSERT INTO model_system (
            id, "name", sid, category, group_code, mine_code, mine_id,
            definition_collect_period, update_collect_period,
            conf_table, conf_table_struct, update_table, history_table,
            route_service, warn_service, converter_service, early_warn_service, access_configurable,
            patch_enabled, soft_deletion_enabled,
            handler_type, handler_version,
            exchange,
            definition_routing_key, update_routing_key, history_routing_key,
            history_queue, definition_queue, update_queue,
            source_system_code, source_system_name, control_source_id,
            flag, enabled_at,
            created_at, updated_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id, jdbcType=VARCHAR},
            #{item.name, jdbcType=VARCHAR},
            #{item.sid, jdbcType=INTEGER},
            #{item.category, jdbcType=VARCHAR},
            #{item.groupCode, jdbcType=VARCHAR},
            #{item.mineCode, jdbcType=VARCHAR},
            #{item.mineId, jdbcType=INTEGER},
            #{item.definitionCollectPeriod, jdbcType=INTEGER},
            #{item.updateCollectPeriod, jdbcType=INTEGER},
            #{item.confTable, jdbcType=VARCHAR},
            #{item.confTableStruct, jdbcType=VARCHAR},
            #{item.updateTable, jdbcType=VARCHAR},
            #{item.historyTable, jdbcType=VARCHAR},
            #{item.routeService, jdbcType=INTEGER},
            #{item.warnService, jdbcType=INTEGER},
            #{item.converterService, jdbcType=INTEGER},
            #{item.earlyWarnService, jdbcType=INTEGER},
            #{item.accessConfigurable, jdbcType=INTEGER},
            #{item.patchEnabled, jdbcType=INTEGER},
            #{item.softDeletionEnabled, jdbcType=INTEGER},
            #{item.handlerType, jdbcType=VARCHAR},
            #{item.handlerVersion, jdbcType=VARCHAR},
            #{item.exchange, jdbcType=VARCHAR},
            #{item.definitionRoutingKey, jdbcType=VARCHAR},
            #{item.updateRoutingKey, jdbcType=VARCHAR},
            #{item.historyRoutingKey, jdbcType=VARCHAR},
            #{item.historyQueue, jdbcType=VARCHAR},
            #{item.definitionQueue, jdbcType=VARCHAR},
            #{item.updateQueue, jdbcType=VARCHAR},
            #{item.sourceSystemCode, jdbcType=VARCHAR},
            #{item.sourceSystemName, jdbcType=VARCHAR},
            #{item.controlSourceId, jdbcType=VARCHAR},
            #{item.flag, jdbcType=VARCHAR},
            <if test='item.flag != null and item.flag == "1" and item.enabledAt != null'>
                #{item.enabledAt, jdbcType=TIMESTAMP},
            </if>
            <if test='item.flag != null and item.flag == "1" and item.enabledAt == null'>
                CURRENT_TIMESTAMP,
            </if>
            <if test='item.flag == null or item.flag != "1"'>
                null,
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
            "name" = EXCLUDED."name",
            sid = EXCLUDED.sid,
            category = EXCLUDED.category,
            definition_collect_period = EXCLUDED.definition_collect_period,
            update_collect_period = EXCLUDED.update_collect_period,
            conf_table = EXCLUDED.conf_table,
            conf_table_struct = EXCLUDED.conf_table_struct,
            update_table = EXCLUDED.update_table,
            history_table = EXCLUDED.history_table,
            route_service = EXCLUDED.route_service,
            warn_service = EXCLUDED.warn_service,
            converter_service = EXCLUDED.converter_service,
            early_warn_service = EXCLUDED.early_warn_service,
            access_configurable = EXCLUDED.access_configurable,
            patch_enabled = EXCLUDED.patch_enabled,
            soft_deletion_enabled = EXCLUDED.soft_deletion_enabled,
            handler_type = EXCLUDED.handler_type,
            handler_version = EXCLUDED.handler_version,
            "exchange" = EXCLUDED."exchange",
            definition_routing_key = EXCLUDED.definition_routing_key,
            update_routing_key = EXCLUDED.update_routing_key,
            history_routing_key = EXCLUDED.history_routing_key,
            history_queue = EXCLUDED.history_queue,
            definition_queue = EXCLUDED.definition_queue,
            update_queue = EXCLUDED.update_queue,
            source_system_code = EXCLUDED.source_system_code,
            source_system_name = EXCLUDED.source_system_name,
            control_source_id = EXCLUDED.control_source_id,
            flag = EXCLUDED.flag,
            enabled_at = case
                when model_system.flag != '1' and EXCLUDED.flag = '1' then coalesce(EXCLUDED.enabled_at, CURRENT_TIMESTAMP)
                else model_system.enabled_at
            end,
            updated_at = CURRENT_TIMESTAMP
    </insert>

    <insert id="upsertBatchOnlyEnableRoute">
        INSERT INTO model_system (
            id, "name", sid, category, group_code, mine_code, mine_id,
            definition_collect_period, update_collect_period,
            conf_table, conf_table_struct, update_table, history_table,
            route_service, warn_service, converter_service, early_warn_service, access_configurable,
            patch_enabled, soft_deletion_enabled,
            handler_type, handler_version,
            exchange,
            definition_routing_key, update_routing_key, history_routing_key,
            history_queue, definition_queue, update_queue,
            source_system_code, source_system_name, business_system_code, control_source_id,
            flag, enabled_at,
            created_at, updated_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id, jdbcType=VARCHAR},
            #{item.name, jdbcType=VARCHAR},
            #{item.sid, jdbcType=INTEGER},
            #{item.category, jdbcType=VARCHAR},
            #{item.groupCode, jdbcType=VARCHAR},
            #{item.mineCode, jdbcType=VARCHAR},
            #{item.mineId, jdbcType=INTEGER},
            #{item.definitionCollectPeriod, jdbcType=INTEGER},
            #{item.updateCollectPeriod, jdbcType=INTEGER},
            #{item.confTable, jdbcType=VARCHAR},
            #{item.confTableStruct, jdbcType=VARCHAR},
            #{item.updateTable, jdbcType=VARCHAR},
            #{item.historyTable, jdbcType=VARCHAR},
            #{item.routeService, jdbcType=INTEGER},
            #{item.warnService, jdbcType=INTEGER},
            #{item.converterService, jdbcType=INTEGER},
            #{item.earlyWarnService, jdbcType=INTEGER},
            #{item.accessConfigurable, jdbcType=INTEGER},
            #{item.patchEnabled, jdbcType=INTEGER},
            #{item.softDeletionEnabled, jdbcType=INTEGER},
            #{item.handlerType, jdbcType=VARCHAR},
            #{item.handlerVersion, jdbcType=VARCHAR},
            #{item.exchange, jdbcType=VARCHAR},
            #{item.definitionRoutingKey, jdbcType=VARCHAR},
            #{item.updateRoutingKey, jdbcType=VARCHAR},
            #{item.historyRoutingKey, jdbcType=VARCHAR},
            #{item.historyQueue, jdbcType=VARCHAR},
            #{item.definitionQueue, jdbcType=VARCHAR},
            #{item.updateQueue, jdbcType=VARCHAR},
            #{item.sourceSystemCode, jdbcType=VARCHAR},
            #{item.sourceSystemName, jdbcType=VARCHAR},
            #{item.businessSystemCode, jdbcType=VARCHAR},
            #{item.controlSourceId, jdbcType=VARCHAR},
            #{item.flag, jdbcType=VARCHAR},
            <if test='item.flag != null and item.flag == "1" and item.enabledAt != null'>
                #{item.enabledAt, jdbcType=TIMESTAMP},
            </if>
            <if test='item.flag != null and item.flag == "1" and item.enabledAt == null'>
                CURRENT_TIMESTAMP,
            </if>
            <if test='item.flag == null or item.flag != "1"'>
                null,
            </if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
            "name" = EXCLUDED."name",
            sid = EXCLUDED.sid,
            category = EXCLUDED.category,
            definition_collect_period = EXCLUDED.definition_collect_period,
            update_collect_period = EXCLUDED.update_collect_period,
            conf_table = EXCLUDED.conf_table,
            conf_table_struct = EXCLUDED.conf_table_struct,
            update_table = EXCLUDED.update_table,
            history_table = EXCLUDED.history_table,
            route_service = case when EXCLUDED.route_service = 1 then 1 else model_system.route_service end,
            warn_service = EXCLUDED.warn_service,
            converter_service = EXCLUDED.converter_service,
            early_warn_service = EXCLUDED.early_warn_service,
            access_configurable = EXCLUDED.access_configurable,
            patch_enabled = EXCLUDED.patch_enabled,
            soft_deletion_enabled = EXCLUDED.soft_deletion_enabled,
            handler_type = EXCLUDED.handler_type,
            handler_version = EXCLUDED.handler_version,
            "exchange" = EXCLUDED."exchange",
            definition_routing_key = EXCLUDED.definition_routing_key,
            update_routing_key = EXCLUDED.update_routing_key,
            history_routing_key = EXCLUDED.history_routing_key,
            history_queue = EXCLUDED.history_queue,
            definition_queue = EXCLUDED.definition_queue,
            update_queue = EXCLUDED.update_queue,
            source_system_code = EXCLUDED.source_system_code,
            source_system_name = EXCLUDED.source_system_name,
            business_system_code = EXCLUDED.business_system_code,
            control_source_id = EXCLUDED.control_source_id,
            flag = EXCLUDED.flag,
            enabled_at = case
                when model_system.flag != '1' and EXCLUDED.flag = '1' then coalesce(EXCLUDED.enabled_at, CURRENT_TIMESTAMP)
                else model_system.enabled_at
            end,
            updated_at = CURRENT_TIMESTAMP
    </insert>

    <update id="disableBySourceSystemCodes">
        update model_system
        set flag = '0'
        <where>
            <if test="mineCode != null and mineCode != ''">
                and mine_code = #{mineCode, jdbcType=VARCHAR}
            </if>
            <if test="set != null and set.size > 0">
                and source_system_code in
                <foreach item="item" collection="set" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            and flag = '1'
        </where>
    </update>

    <update id="disableByEnabledSourceSystemCodes">
        update model_system
        set flag = '0'
        <where>
            <if test="mineCode != null and mineCode != ''">
                and mine_code = #{mineCode, jdbcType=VARCHAR}
            </if>
            <if test="set != null and set.size > 0">
                and source_system_code not in
                <foreach item="item" collection="set" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            and flag = '1'
        </where>
    </update>

    <update id="disableByEnabledMineCodes" parameterType="java.util.Set">
        update model_system
        set flag = '0',
            updated_at = now()
        <where>
            <if test="set != null and set.size > 0">
                and mine_code not in
                <foreach collection="set" item="item" open="(" separator="," close=")">#{item, jdbcType=VARCHAR}</foreach>
            </if>
            and flag = '1'
        </where>
    </update>

    <select
        id="loadBusinessSystemRelation"
        resultType="com.bdtd.modules.data_access.dto.ModelSystemBusinessRelation"
    >
        SELECT DISTINCT
            id, name, mine_code, sid, source_system_code, source_system_name, business_system_code
        FROM model_system
        WHERE flag = '1'
          and business_system_code is not null
          and business_system_code != ''
    </select>

</mapper>
