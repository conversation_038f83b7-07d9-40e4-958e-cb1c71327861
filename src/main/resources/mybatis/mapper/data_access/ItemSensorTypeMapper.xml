<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.ItemSensorTypeMapper">

    <select id="selectSensorTypeList" resultType="com.bdtd.modules.data_access.entity.ItemSensorType" parameterType="com.bdtd.modules.data_access.dto.ItemSensorTypeQuery">
        SELECT
        DISTINCT
        ist.sensor_type_code ,
        ist.sensor_type_name ,
        ist.point_value_type_code ,
        ist.sensor_type_note
        FROM safety_definition sd
        LEFT JOIN item_sensor_type ist
        ON sd.sensor_type_code  = ist.sensor_type_code
        <where>
            ist.sensor_type_code IS NOT NULL
            <if test="query.sensorTypeCode != null and query.sensorTypeCode != ''">
                AND ist.sensor_type_code LIKE  '%'||#{query.sensorTypeCode}||'%'
            </if>
            <if test="query.sensorTypeName != null and query.sensorTypeName != ''">
                AND ist.sensor_type_name LIKE  '%'||#{query.sensorTypeName}||'%'
            </if>
            <if test="query.pointValueTypeCode != null and query.pointValueTypeCode != ''">
                AND ist.point_value_type_code LIKE  '%'||#{query.pointValueTypeCode}||'%'
            </if>
        </where>
    </select>

</mapper>
