<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.data_access.mapper.PatchLogMapper">

    <insert id="updateLog">
        update data_patch_log
        set collect_count = #{collectCount,jdbcType=INTEGER},
            data_time = #{dataTime,jdbcType=TIMESTAMP},
            updated_at = #{updatedAt,jdbcType=TIMESTAMP}
        where patch_log_id = #{patchLogId,jdbcType=VARCHAR}
    </insert>

    <insert id="insertLog">
        INSERT INTO data_patch_log (
            group_code,
            group_name,
            mine_code,
            mine_name,
            system_code,
            system_name,
            patch_log_id,
            start_time,
            end_time,
            collect_count,
            data_time,
            created_at,
            updated_at
        )
        VALUES (
            #{item.groupCode, jdbcType=VARCHAR},
            #{item.groupName, jdbcType=VARCHAR},
            #{item.mineCode, jdbcType=VARCHAR},
            #{item.mineName, jdbcType=VARCHAR},
            #{item.systemCode, jdbcType=VARCHAR},
            #{item.systemName, jdbcType=VARCHAR},
            #{item.patchLogId, jdbcType=VARCHAR},
            #{item.startTime, jdbcType=TIMESTAMP},
            #{item.endTime, jdbcType=TIMESTAMP},
            #{item.collectCount, jdbcType=INTEGER},
            #{item.dataTime, jdbcType=TIMESTAMP},
            now(),
            now()
        )
        ON CONFLICT(point_id) DO UPDATE
        SET
            point_id = excluded.point_id
    </insert>

</mapper>
