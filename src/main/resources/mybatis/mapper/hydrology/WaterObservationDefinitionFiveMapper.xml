<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.hydrology.mapper.WaterObservationDefinitionFiveMapper">

<select id="selectBySystemId" resultType="java.util.Map">
    select d.mine_code,
        d.point_id,
        d.point_type,
        d.type,
        d.updated_at,
        d.name,
        d.unit,
        d.location_type,
        r.state,
        r.pressure,
        r.elevation,
        r.depth,
        r.flow,
        r.temperature
    from water_observation_definition_five d
    left join water_observation_realtime_five r on d.point_id = r.point_id
    where 1=1
    <if test="entity.mineCode !=null and entity.mineCode !=''">
        and d.mine_code like '%'||  #{entity.mineCode} ||'%'
    </if>
    <if test="entity.pointId !=null and entity.pointId !=''">
        and d.point_id like '%'||  #{entity.pointId} ||'%'
    </if>
    <if test="entity.type !=null and entity.type !=''">
        and d.type like '%'||  #{entity.type} ||'%'
    </if>
    <if test="entity.name !=null and entity.name !=''">
        and d.name like '%'||  #{entity.name} ||'%'
    </if>
</select>
</mapper>
