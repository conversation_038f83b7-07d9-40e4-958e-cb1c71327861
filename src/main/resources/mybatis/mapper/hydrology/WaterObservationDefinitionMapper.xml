<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.hydrology.mapper.WaterObservationDefinitionMapper">
    <select id="selectBySystemId" resultType="java.util.Map">
        select
            d.mine_code,
            d.point_id,
            d.point_type,
            d.type,
            d.name,
            d.unit,
            d.location_type,
            r.state,
            (
                case
                    when d.point_type = 1 and  r.value ='0'  then '关'
                    when d.point_type = 1 and  r.value ='-1'  then '已移除'
                    when d.point_type = 1 and  r.value ='1' then '开'
                    else   r.value
                end
            ) AS value,
            r.timestamp as updated_at
        from water_observation_definition d
            left join water_observation_realtime r on d.point_id = r.point_id
        where 1=1
        <if test="entity.mineCode != null and entity.mineCode != ''">
            and d.mine_code like '%'||  #{entity.mineCode} ||'%'
        </if>
        <if test="entity.pointId != null and entity.pointId != ''">
            and d.point_id like '%'||  #{entity.pointId} ||'%'
        </if>
        <if test="entity.type != null and entity.type != ''">
            and d.type like '%'||  #{entity.type} ||'%'
        </if>

        <if test="entity.name != null and entity.name != ''">
            and d.name like '%'||  #{entity.name} ||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND d.deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND d.deleted_at is not null
        </if>
        ORDER BY r."value" IS NULL, r."timestamp" desc
    </select>
</mapper>
