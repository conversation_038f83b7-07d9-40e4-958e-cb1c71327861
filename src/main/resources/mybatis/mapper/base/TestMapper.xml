<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.base.mapper.TestMapper">

  <insert id="insertBatch">
    insert into alarm_history ( camera_id, alg_name,timestamp)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.camera_id,jdbcType=INTEGER}, #{item.alg_name,jdbcType=VARCHAR}, #{item.timestamp,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="select" parameterType="String" resultType="java.util.Map">
    ${value}
  </select>
  <select id="selectValue" parameterType="String" resultType="String">
    ${value}
  </select>
  <select id="selectListString" parameterType="String" resultType="Boolean">
    ${value}
  </select>
</mapper>