<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.safety.mapper.SafetyAlarmRealtimeMapper">

    <select id="searchAlarm" resultType="com.bdtd.modules.safety.entity.SafetyAlarmRealtime">
        select
            mine_code,
            mine_name,
            group_code,
            group_name,
            point_id,
            sensor_type_code,
            sensor_type_name,
            location,
            point_value_type_code,
            unit_code,
            unit_name,
            alarm_type_code,
            alarm_type_name,
            alarm_during,
            max_time,
            max_value,
            min_time,
            min_value,
            avg_value,
            reason,
            measures,
            record_time,
            record_person,
            handle_status,
            data_time,
            begin_time
        from safety_alarm_realtime
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                sensor_type_code in
                <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in
                <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="location != null and location != ''">
                and (location like '%'||#{location}||'%'
                or point_id like '%'||#{location}||'%')
            </if>
            <if test="pointValueTypeCode != null and pointValueTypeCode != ''">
                and point_value_type_code =#{pointValueTypeCode}
            </if>
            <if test="alarmTypeCode != null and alarmTypeCode != ''">
                and alarm_type_code in
                <foreach collection="alarmTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="alarmTypeName != null and alarmTypeName != ''">
                and alarm_type_name in
                <foreach collection="alarmTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="handleStatus != null">
                and handle_status = #{handleStatus}
            </if>
            <if test="startTime!=null and startTime!=''">
                and to_char(begin_time, 'yyyy-mm-dd hh24:mm:ss') &gt;= #{startTime}
            </if>
            <if test="endTime!=null and endTime!=''">
                and to_char(begin_time, 'yyyy-mm-dd hh24:mm:ss') &lt;= #{endTime}
            </if>
        </where>
        order by data_time DESC
    </select>
</mapper>
