<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.safety.mapper.SafetyRealtimeMapper">

    <select id="findSafetyRealtimePage" resultType="com.bdtd.modules.safety.entity.SafetyRealtime">
        select
            r.mine_code,
            r.mine_name,
            r.group_code,
            r.group_name,
            r.point_id,
            r.sensor_type_code,
            r.sensor_type_name,
            r.location,
            r.point_value_type_code,
            COALESCE(r.unit_code, d.unit_code) as unit_code,
            COALESCE(r.unit_name, d.unit_name) as unit_name,
            r.alarm_status_code,
            r.alarm_status_name,
            r.data_time,
            (
                case
                    when r.point_value_type_code = 1 and value ='0'  then '关'
                    when r.point_value_type_code = 1 and value ='-1'  then '已移除'
                    when r.point_value_type_code = 1 and value ='1' then '开'
                else r.value end
            ) AS value
        from safety_realtime r
            left join safety_definition d on r.point_id = d.point_id
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                r.sensor_type_code in <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and r.sensor_type_name in <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="location != null and location != ''">
                and r.location like  '%'||#{location}||'%'
            </if>
            <if test="alarmStatusCode != null and alarmStatusCode != ''">
                and r.alarm_status_code in <foreach collection="alarmStatusCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="alarmStatusName != null and alarmStatusName != ''">
                and r.alarm_status_name in <foreach collection="alarmStatusName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
        </where>
        order by r.data_time DESC
    </select>

    <select id="findSafetyRealtimeCount" resultType="com.bdtd.modules.safety.vo.SafetyRealtimeVo">
        select
            mine_code,
            sum( CASE WHEN alarm_status_code = '0' THEN 1 ELSE 0 END ) AS a0,
            sum( CASE WHEN alarm_status_code = '1' THEN 1 ELSE 0 END ) AS a1,
            sum( CASE WHEN alarm_status_code = '128' THEN 1 ELSE 0 END ) AS a128,
            sum( CASE WHEN alarm_status_code = '16' THEN 1 ELSE 0 END ) AS a16,
            sum( CASE WHEN alarm_status_code = '2' THEN 1 ELSE 0 END ) AS a2,
            sum( CASE WHEN alarm_status_code = '32' THEN 1 ELSE 0 END ) AS a32,
            sum( CASE WHEN alarm_status_code = '4' THEN 1 ELSE 0 END ) AS a4,
            sum( CASE WHEN alarm_status_code = '5' THEN 1 ELSE 0 END ) AS a5,
            sum( CASE WHEN alarm_status_code = '64' THEN 1 ELSE 0 END ) AS a64,
            sum( CASE WHEN alarm_status_code = '8' THEN 1 ELSE 0 END ) AS a8
        from
            safety_realtime
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                 sensor_type_code in <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in	<foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
        </where>
        group by mine_code
    </select>

    <select id="findSafetyRealtimeListAll" resultType="com.bdtd.modules.safety.entity.SafetyRealtime">
        select
            r.mine_code,r.mine_name,r.group_code,r.group_name,r.point_id,
            r.sensor_type_code,r.sensor_type_name,
            r.location,r.point_value_type_code,
            COALESCE(r.unit_code, d.unit_code) as unit_code,
            COALESCE(r.unit_name, d.unit_name) as unit_name,
            r.alarm_status_code,r.alarm_status_name,r.data_time,
            (
                case
                    when r.point_value_type_code = 1 and value ='0'  then '关'
                    when r.point_value_type_code = 1 and value ='-1'  then '已移除'
                    when r.point_value_type_code = 1 and value ='1' then '开'
                else r.value end
            ) AS value
        from safety_realtime r
            left join safety_definition d on r.point_id = d.point_id
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                r.sensor_type_code in <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and r.sensor_type_name in	<foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="location != null and location != ''">
                and r.location like  '%'||#{location}||'%'
            </if>
            <if test="alarmStatusCode != null and alarmStatusCode != ''">
                and r.alarm_status_code in <foreach collection="alarmStatusCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="alarmStatusName != null and alarmStatusName != ''">
                and r.alarm_status_name in <foreach collection="alarmStatusName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="pointId != null and pointId != ''">
                and r.point_id = #{pointId}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and d.deleted_at is not null
            </if>
        </where>
        order by r.data_time desc
    </select>

    <select id="safetyRealtimeListThree" resultType="com.bdtd.modules.safety.dto.SafetyRealtimeDto">
        select
            point_id,
            alarm_status_code,
            data_time,
            (
                case
                    when point_value_type_code = 1 and value ='0'  then '关'
                    when point_value_type_code = 1 and value ='-1'  then '已移除'
                    when point_value_type_code = 1 and value ='1' then '开'
                else value end
            ) AS value
        from safety_realtime
    </select>
</mapper>
