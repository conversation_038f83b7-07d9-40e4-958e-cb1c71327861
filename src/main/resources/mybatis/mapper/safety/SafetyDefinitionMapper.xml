<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.safety.mapper.SafetyDefinitionMapper">

    <select id="findSafetyDefinitionPage" resultType="com.bdtd.modules.safety.entity.SafetyDefinition">
        SELECT *
        FROM safety_definition
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                sensor_type_code in
                <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in
                <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="location != null and location != ''">
                and location like '%'||#{location}||'%'
            </if>
            <if test="substationName != null and substationName != ''">
                and substation_name like '%'||#{substationName}|| '%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and deleted_at is not null
            </if>
        </where>
    </select>

    <select id="findSafetyDefinitionList" resultType="com.bdtd.modules.safety.entity.SafetyDefinition">
        SELECT *
        FROM safety_definition
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                sensor_type_code in
                <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in
                <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="location != null and location != ''">
                and location like '%'||#{location}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and deleted_at is not null
            </if>
        </where>
    </select>

    <select id="listWithValue" resultType="com.bdtd.modules.safety.entity.SafetyDefinition">
        SELECT
            s.mine_code,
            s.mine_name,
            s.group_code,
            s.group_name,
            s.system_code,
            s.system_name,
            s.substation_code,
            s.substation_name,
            s.point_id,
            s.sensor_type_code,
            s.sensor_type_name,
            s.point_value_type_code,
            s.unit_code,
            s.unit_name,
            s.upper_range_value,
            s.lower_range_value,
            s.upper_alarm_value,
            s.upper_alarm_reset_value,
            s.lower_alarm_value,
            s.lower_alarm_reset_value,
            s.upper_cut_value,
            s.upper_reset_value,
            s.lower_cut_value,
            s.lower_reset_value,
            s.translate,
            s.data_type,
            s.location,
            s.relation,
            s.x,
            s.y,
            s.z,
            s.collect_status,
            s.system_id,
            s.insert_db_time,
            s.update_db_time,
            s.deleted_at,
            r.data_time,
            (
                case
                    when s.point_value_type_code = 1 and r.value ='0' then '关'
                    when s.point_value_type_code = 1 and r.value ='-1' then '已移除'
                    when s.point_value_type_code = 1 and r.value ='1' then '开'
                else r.value end
            ) AS value
        FROM safety_definition s
            LEFT JOIN safety_realtime r on s.point_id = r.point_id
        <where>
            <if test="pointId != null and pointId != ''">
                and s.point_id like '%'||#{pointId}||'%'
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and s.sensor_type_name like '%'||#{sensorTypeName}||'%'
            </if>
            <if test="sensorName != null and sensorName != ''">
                and s.location like '%'||#{sensorName}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and s.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and s.deleted_at is not null
            </if>
        </where>
    </select>
</mapper>
