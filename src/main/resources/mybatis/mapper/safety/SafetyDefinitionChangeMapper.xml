<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.safety.mapper.SafetyDefinitionChangeMapper">

    <select id="findSafetyDefinitionChangePage" resultType="com.bdtd.modules.safety.entity.SafetyDefinitionChange">
        select *
        from safety_definition_change
        <where>
            <if test="beginTime != null and endTime == null">
                and begin_time &gt;= #{beginTime}
            </if>
            <if test="beginTime == null and endTime != null">
                and begin_time &lt;= #{endTime}
            </if>
            <if test="beginTime != null and endTime != null">
                and begin_time between #{beginTime} and #{endTime}
            </if>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                and sensor_type_code in
                <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in
                <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="pointValueTypeCode != null and pointValueTypeCode != ''">
                and point_value_type_code = #{pointValueTypeCode}
            </if>
            <if test="pointId != null and pointId != ''">
                and point_id = #{pointId}
            </if>
            <if test="location != null and location != ''">
                and location like '%'||#{location}||'%'
            </if>
        </where>
        order by end_time DESC
    </select>

    <select id="findSafetyDefinitionChangeList" resultType="com.bdtd.modules.safety.entity.SafetyDefinitionChange">
        select *
        from safety_definition_change
        <where>
            <if test="sensorTypeCode != null and sensorTypeCode != ''">
                sensor_type_code in
                <foreach collection="sensorTypeCode" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="sensorTypeName != null and sensorTypeName != ''">
                and sensor_type_name in
                <foreach collection="sensorTypeName" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <!--
            <if test="pointValueTypeCode != null and pointValueTypeCode != ''">
                and point_value_type_code = #{pointValueTypeCode}
            </if> -->
            <if test="pointId != null and pointId != ''">
                and point_id like '%'||#{pointId}||'%'
            </if>
            <if test="location != null and location != ''">
                and location = #{location}
            </if>
        </where>
    </select>

</mapper>
