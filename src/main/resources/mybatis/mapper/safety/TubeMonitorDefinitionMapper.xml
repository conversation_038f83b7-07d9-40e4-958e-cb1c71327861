<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.safety.mapper.TubeMonitorDefinitionMapper">

    <select id="selectBySystemId" resultType="java.util.Map">
        SELECT
            d.mine_code,
            d.point_id,
            d.point_name,
            d.system_type,
            d.system_name,
            d.manufacturer_name,
            COALESCE(d.sensor_type, r.sensor_type) as sensor_type,
            d.data_type,
            COALESCE(d.unit, r.unit) as unit,
            d.high_range,
            d.low_range,
            d.upper_alarm,
            d.area_code,
            d.area_name,
            d.x,
            d.y,
            d.z,
            r."state",
            r."timestamp",
            r."value"
        FROM tube_monitor_definition d
            LEFT JOIN tube_monitor_realtime r ON d.point_id = r.point_id
        <where>
            <if test="entity.pointId !=null and entity.pointId !=''">
                and d.point_id like '%'|| #{entity.pointId} ||'%'
            </if>
            <if test="entity.mineCode !=null and entity.mineCode !=''">
                and d.mine_code like '%'|| #{entity.mineCode} ||'%'
            </if>
            <if test="entity.sensorType !=null and entity.sensorType !=''">
                and d.sensor_type = #{entity.sensorType}
            </if>
            <if test="entity.areaName !=null and entity.areaName !=''">
                and d.area_name like '%'|| #{entity.areaName} ||'%'
            </if>
            <if test="entity.status !=null and entity.status !=''">
                and d.state = #{entity.status}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                AND d.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                AND d.deleted_at is not null
            </if>
        </where>
        ORDER BY r."value" IS NULL, r."timestamp" desc
    </select>

</mapper>
