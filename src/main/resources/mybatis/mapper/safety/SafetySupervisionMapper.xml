<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bdtd.modules.safety.mapper.SafetySupervisionMapper">

    <select id="selectByPointIds" resultType="java.util.HashMap">
        select
            point_id,
            location,
            upper_range_value
        from safety_definition
        where point_id in
            <foreach collection="list" item="point_id" index="index" open="(" close=")" separator=",">
                #{point_id}
            </foreach>
    </select>

    <select id="selectCount" resultType="HashMap">
        SELECT
            a.point_id,
            a.count,
            ss.name,
            ss.type
        FROM (
            SELECT point_id, count(1)
            FROM safety_supervision_alarm_statistics
            WHERE point_id in
                <foreach collection="list" item="point_id" open="(" separator="," close=")">
                    (#{point_id})
                </foreach>
                and begin_time  <![CDATA[ >= ]]>  #{begin_time}
                and begin_time <![CDATA[ <= ]]>  #{end_time}
            GROUP BY point_id
        ) a left join safety_supervision ss on a.point_id = ss.point_id
    </select>

    <select id="selectGroupByAlarmLevel" resultType="HashMap">
        select
            count(point_id) as num,
            alarm_level
        from safety_supervision_alarm_statistics
        where 1=1
            <if test="typeName!=null">
                and type=#{typeName}
            </if>
            and begin_time <![CDATA[ >= ]]> #{startTime}
            and begin_time <![CDATA[ <= ]]>#{endTime}
        group by alarm_level;
    </select>

    <select id="selectList" resultType="HashMap">
        select
            count(point_id) as num,
            #{field}
        from safety_supervision_alarm_statistics
        where begin_time <![CDATA[ >= ]]> #{startTime}
            and begin_time <![CDATA[ <= ]]>#{endTime}
        group by #{field}
    </select>
</mapper>
