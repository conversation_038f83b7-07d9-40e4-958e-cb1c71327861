<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.business.mapper.BeltWeigherDefinitionMapper">


    <select id="findBeltWeigherByCount" resultType="com.bdtd.modules.others.dto.BeltWeigherDefinitionVo">
        SELECT
            c.location,
            sum( cast( c.day_total AS NUMERIC ) ) dt,
            sum( cast( c.month_total AS NUMERIC ) ) mt,
            sum( cast( c.year_total AS NUMERIC ) ) yt,
            sum( cast( c.class_total AS NUMERIC ) ) ct,
            sum( cast( c.flow_total AS NUMERIC ) ) ft
                FROM
                    (
                SELECT
                    a.location,
                CASE

                    WHEN a.type = '流量' THEN
                    b.VALUE
                    ELSE '0'
                    END AS flow_total,
                CASE
                    WHEN substr( a.NAME, length( a.NAME ) - 2, 3 ) = '日累计' THEN
                    b.VALUE
                    ELSE '0'
                    END AS day_total,
                CASE
                    WHEN substr( a.NAME, length( a.NAME ) - 2, 3 ) = '月累计' THEN
                    b.VALUE
                    ELSE '0'
                    END AS month_total,
                CASE
                    WHEN substr( a.NAME, length( a.NAME ) - 2, 3 ) = '班累计' THEN
                    b.VALUE
                    ELSE '0'
                    END AS class_total,
                CASE
                    WHEN substr( a.NAME, length( a.NAME ) - 2, 3 ) = '年累计' THEN
                    b.VALUE
                    ELSE '0'
                    END AS year_total
                FROM
                    belt_weigher_definition a
                    LEFT JOIN belt_weigher_realtime b ON a.point_id = b.point_id
                WHERE
                    a.NAME LIKE '%日累计'
                    OR a.NAME LIKE '%月累计'
                    OR a.NAME LIKE '%年累计'
                    OR a.NAME LIKE '%班累计'
                    OR a.type = '流量'
                    <if test="location != null and location != ''">
                        and a.location in <foreach collection="location" item="item" index="i" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    ) c
                GROUP BY
                c.location
    </select>
</mapper>
