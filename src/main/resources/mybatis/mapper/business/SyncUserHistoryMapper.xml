<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.business.dao.SyncUserHistoryMapper">

    <select id="selectSyncHistory" resultType="java.util.Map">
        SELECT *
        FROM (
            SELECT pe.*, COALESCE(su.status, 0) as status
            FROM "position_employee" as pe
                LEFT JOIN sync_user_history as su on pe.card_id = su.card_id
        ) se
        <if test="status == 1">
            WHERE se.status = 1
        </if>
        <if test="status == 0">
            WHERE se.status = 0
        </if>
        ORDER BY se.insert_db_time, card_id DESC
        LIMIT #{pageSize}
        OFFSET #{number}
    </select>

    <select id="selectCountSyncHistory" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(*)
        FROM (
            SELECT pe.*, COALESCE(su.status,0) as status
            FROM "position_employee" as pe
                LEFT JOIN sync_user_history as su on pe.card_id = su.card_id
        ) se
        <if test="_parameter == 1">
            WHERE se.status = 1
        </if>
        <if test="_parameter == 0">
            WHERE se.status = 0
        </if>
    </select>

</mapper>
