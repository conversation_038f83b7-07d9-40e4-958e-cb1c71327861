<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportRealtimeMapper">

    <select id="selectRealtimeData" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportRealtimeVo">
        SELECT
        car_number_name,
        location_number,
        x,
        y,
        driver_id,
        driver_name,
        timestamp
        FROM
        auxiliary_transport_realtime
        where 1=1
        <if test="entity.carNumberName !=null and entity.carNumberName !=''">
            and car_number_name like '%'||  #{entity.carNumberName} ||'%'
        </if>
        <if test="entity.locationNumber !=null and entity.locationNumber !=''">
            and location_number like '%'||  #{entity.locationNumber} ||'%'
        </if>
        <if test="entity.driverId !=null and entity.driverId !=''">
            and driver_id like '%'||  #{entity.driverId} ||'%'
        </if>
        <if test="entity.driverName !=null and entity.driverName !=''">
            and driver_name like '%'||  #{entity.driverName} ||'%'
        </if>
        order by updated_at DESC
    </select>
</mapper>
