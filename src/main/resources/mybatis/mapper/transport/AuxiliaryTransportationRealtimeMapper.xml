<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportationRealtimeMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationRealtimeVo">
        SELECT
        atr.sender_id ,
        atcd.car_number,
        atcd.car_type,
        atr.car_department,
        atr.area_code ,
        atr.area_name ,
        atr.in_area_time ,
        atr.station_code ,
        atr.station_name ,
        atr.in_station_time ,
        atr.in_mine_time ,
        atr.out_mine_time ,
        atr.distance ,
        atr.direction ,
        atr.data_time ,
        atr.driver_id ,
        atr.x ,
        atr.y ,
        atr.z ,
        atr.tunnel_code ,
        atr.tunnel_name ,
        atr.tunnel_loc ,
        atr.data_time ,
        atr.real_speed
        FROM auxiliary_transportation_realtime atr
        LEFT JOIN auxiliary_transportation_car_definition atcd
        ON atr.sender_id = atcd.sender_id
        WHERE 1=1
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND atr.sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND atcd.car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.areaName !=null and entity.areaName !=''">
            AND atr.area_name LIKE '%'||  #{entity.areaName} ||'%'
        </if>
        <if test="entity.stationName !=null and entity.stationName !=''">
            AND atr.station_name LIKE '%'||  #{entity.stationName} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND atcd.car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND atcd.car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>

        ORDER BY atr.data_time DESC
    </select>



    <select id="getCarRealtimeList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationRealtimeVo">
        SELECT
        atr.sender_id ,
        atcd.car_number,
        atcd.car_type,
        atr.car_department,
        atr.area_code ,
        atr.area_name ,
        atr.in_area_time ,
        atr.station_code ,
        atr.station_name ,
        atr.in_station_time ,
        atr.in_mine_time ,
        atr.out_mine_time ,
        atr.distance ,
        atr.direction ,
        atr.data_time ,
        atr.driver_id ,
        atr.x ,
        atr.y ,
        atr.z ,
        atr.tunnel_code ,
        atr.tunnel_name ,
        atr.tunnel_loc ,
        atr.data_time ,
        atr.real_speed
        FROM auxiliary_transportation_realtime atr
        LEFT JOIN auxiliary_transportation_car_definition atcd
        ON atr.sender_id = atcd.sender_id
        WHERE 1=1
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND atr.sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND atcd.car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.areaName !=null and entity.areaName !=''">
            AND atr.area_name LIKE '%'||  #{entity.areaName} ||'%'
        </if>
        <if test="entity.stationName !=null and entity.stationName !=''">
            AND atr.station_name LIKE '%'||  #{entity.stationName} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND atcd.car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND atcd.car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>
        ORDER BY atr.data_time DESC
    </select>
</mapper>
