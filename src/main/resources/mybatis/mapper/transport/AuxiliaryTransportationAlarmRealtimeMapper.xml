<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportationAlarmRealtimeMapper">
    <select id="selectBySystemId" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationAlarmRealtimeVo">
        SELECT
        atar.sender_id,
        atar.car_number,
        atar.car_type,
        atar.car_department,
        atar.begin_time,
        atar.end_time,
        atar.alarm_value,
        atar.is_handle,
        atar.handled_by,
        atar.data_type,
        atar.alarm_type_code,
        atar.alarm_type_name,
        atar.section_name,
        atar.limit_speed,
        atar.speed,
        atar.speed_percentage,
        atar.processing_results
        FROM
        auxiliary_transportation_alarm_realtime atar
        WHERE 1=1
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND atar.car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND atar.sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND atar.car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND atar.car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>
        <if test="startTime !=null and startTime !=''">
            AND atar.begin_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND atar.begin_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="entity.alarmTypeCode !=null and entity.alarmTypeCode !=''">
            AND atar.alarm_type_code = #{entity.alarmTypeCode}
        </if>
        ORDER BY atar.update_db_time DESC
    </select>


    <select id="getAuxiliaryTransportationAlarmRealtimeList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationAlarmRealtimeVo">
        SELECT
        atar.sender_id,
        atar.car_number,
        atar.car_type,
        atar.car_department,
        atar.begin_time,
        atar.end_time,
        atar.alarm_value,
        atar.is_handle,
        atar.handled_by,
        atar.data_type,
        atar.alarm_type_code,
        atar.alarm_type_name,
        atar.section_name,
        atar.limit_speed,
        atar.speed,
        atar.speed_percentage,
        atar.processing_results
        FROM
        auxiliary_transportation_alarm_realtime atar
        WHERE 1=1
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND atar.car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND atar.sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND atar.car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND atar.car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>
        <if test="entity.alarmTypeCode !=null and entity.alarmTypeCode !=''">
            AND atar.alarm_type_code = #{entity.alarmTypeCode}
        </if>
        ORDER BY atar.update_db_time DESC
    </select>
</mapper>
