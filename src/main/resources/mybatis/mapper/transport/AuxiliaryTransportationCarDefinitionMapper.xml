<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportationCarDefinitionMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationCarDefinitionVo">
        SELECT
        sender_id,
        car_number,
        car_type,
        car_department,
        car_internal
        FROM
        auxiliary_transportation_car_definition
        WHERE 1=1
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>
        <if test="entity.carInternal !=null and entity.carInternal !=''">
            AND car_internal = #{entity.carInternal}
        </if>
        ORDER BY update_db_time DESC
    </select>


    <select id="getCarTypeCountList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationCarCountVo">
        SELECT
        car_type,
        count(car_type)  AS car_type_count
        FROM
        auxiliary_transportation_car_definition
        GROUP BY car_type
    </select>

    <select id="getCarDepartmentList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationCarDefinitionVo">
        SELECT
        DISTINCT car_department
        FROM
        auxiliary_transportation_car_definition
    </select>


    <select id="getCarTypeList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationCarDefinitionVo">
        SELECT
        DISTINCT car_type
        FROM
        auxiliary_transportation_car_definition
    </select>

    <select id="getCarList" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationCarDefinitionVo">
        SELECT
        sender_id,
        car_number,
        car_type,
        car_department,
        car_internal
        FROM
        auxiliary_transportation_car_definition
        WHERE 1=1
        <if test="entity.carNumber !=null and entity.carNumber !=''">
            AND car_number LIKE '%'||  #{entity.carNumber} ||'%'
        </if>
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        <if test="entity.carType !=null and entity.carType !=''">
            AND car_type LIKE '%'||  #{entity.carType} ||'%'
        </if>
        <if test="entity.carDepartment !=null and entity.carDepartment !=''">
            AND car_department LIKE '%'||  #{entity.carDepartment} ||'%'
        </if>
        <if test="entity.carInternal !=null and entity.carInternal !=''">
            AND car_internal = #{entity.carInternal}
        </if>
        ORDER BY update_db_time DESC
    </select>
</mapper>
