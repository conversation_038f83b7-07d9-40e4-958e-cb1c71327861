<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportationAccessRecordMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportationAccessRecordVo">
        SELECT
        sender_id ,
        in_mine_time ,
        out_mine_time ,
        class_set_code ,
        class_set_name ,
        in_station_code ,
        out_station_code ,
        FROM auxiliary_transportation_access_record
        WHERE 1=1
        <if test="entity.senderId !=null and entity.senderId !=''">
            AND sender_id LIKE '%'||  #{entity.senderId} ||'%'
        </if>
        ORDER BY update_db_time DESC
    </select>
</mapper>
