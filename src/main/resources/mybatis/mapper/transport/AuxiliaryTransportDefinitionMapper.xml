<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.transport.mapper.AuxiliaryTransportDefinitionMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.transport.dto.AuxiliaryTransportDefinitionVo">
        SELECT
        car_number_name,
        sip_number,
        location_number
        FROM
        auxiliary_transport_definition
        where 1=1
        <if test="entity.carNumberName !=null and entity.carNumberName !=''">
            and car_number_name like '%'||  #{entity.carNumberName} ||'%'
        </if>
        <if test="entity.sipNumber !=null and entity.sipNumber !=''">
            and sip_number like '%'||  #{entity.sipNumber} ||'%'
        </if>
        <if test="entity.locationNumber !=null and entity.locationNumber !=''">
            and location_number like '%'||  #{entity.locationNumber} ||'%'
        </if>
        order by updated_at DESC
    </select>
</mapper>
