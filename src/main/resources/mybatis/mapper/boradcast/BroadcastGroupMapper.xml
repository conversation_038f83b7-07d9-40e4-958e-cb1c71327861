<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.broadcast.mapper.BroadcastGroupMapper">
    <select id="selectGroup" resultType="java.util.Map">
        select *
        from broadcast_group

        <where>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
        </where>
    </select>

    <select id="selectSameNameCount" resultType="java.lang.Integer">
        select count("id") from broadcast_group
        <where>
            <if test="entity.name != null and entity.name != ''">
                and name = #{entity.name}
            </if>
            <if test="entity.id != null and entity.id != ''">
                and id != #{entity.id }
            </if>
        </where>
    </select>
</mapper>
