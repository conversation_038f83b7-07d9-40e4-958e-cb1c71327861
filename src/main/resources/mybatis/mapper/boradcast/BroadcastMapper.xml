<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.broadcast.mapper.BroadcastMapper">


    <insert id="sendBroadcast"  parameterType="java.util.Map">
        insert into broadcast_directive (
        point_id,
        text,
        duration,
        send_times,
        name
        )values
        <foreach collection="entity" item="item" index="index" separator=",">
            (
            #{item.pointId},
            #{text},
            #{duration},
            #{sendTimes},
            #{item.name}
            )
        </foreach>
    </insert>

    <select id="selectBygroupIds" resultType="java.lang.String">
        select point_id from broadcast_map
        where boardcast_group_id in
        <foreach collection="groupIds" item="groupId" index="i" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </select>

    <select id="selectNameByPointId" resultType="java.lang.String">
        select name from broadcast where point_id = #{pointId} limit 1
    </select>
</mapper>
