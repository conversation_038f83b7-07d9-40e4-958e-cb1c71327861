<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.broadcast.mapper.BroadcastMapMapper">

    <delete id="deleteByGroupIds">
        delete from broadcast_map
        where boardcast_group_id in
        <foreach collection="groupIds" item="groupId" index="i" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <select id="selectRelList" resultType="com.bdtd.modules.broadcast.entity.BroadcastMap">
        select
        mp.boardcast_group_id,
        g.name as group_name,
        mp.point_id,
        b.name,
        b.location,
        b.ip,
        b.state
        from broadcast_map mp
        LEFT JOIN broadcast_group g on g.id = mp.boardcast_group_id
        LEFT JOIN broadcast b on b.point_id = mp.point_id

        <where>
            <if test="groupId != null and groupId != ''">
                and mp.boardcast_group_id = #{groupId}
            </if>

            <if test="name != null and name != ''">
                and b.name  like   '%'|| #{name} ||'%'
            </if>
        </where>
        order by mp.boardcast_group_id
    </select>
</mapper>
