<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.broadcast.mapper.DispatchDirectiveMapper">
    <select id="selectBy" resultType="java.util.Map">
        select *
        from dispatch_directive
        <where>
            <if test="entity.tel != null and entity.tel != ''">
                and tel like '%' || #{entity.tel} || '%'
            </if>
        </where>
        order by updated_at DESC
    </select>
</mapper>
