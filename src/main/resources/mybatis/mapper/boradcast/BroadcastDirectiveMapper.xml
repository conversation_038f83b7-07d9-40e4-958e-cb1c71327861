<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.broadcast.mapper.BroadcastDirectiveMapper">


    <select id="broadcastDirectivelist" resultType="map" >
       SELECT
            bd.point_id,
            b.name,
            bd.text,
            bd.created_at,
            bd.send_times,
            bd.duration
            FROM
             broadcast_directive bd
             join   broadcast b
            on
            bd.point_id = b.point_id
            <if test="name != null and name !=''">
                and  b.name like '%${name}%'
            </if>
            <if test="text != null and text !=''">
                and  bd.text like  '%${text}%'
            </if>


   </select>
</mapper>