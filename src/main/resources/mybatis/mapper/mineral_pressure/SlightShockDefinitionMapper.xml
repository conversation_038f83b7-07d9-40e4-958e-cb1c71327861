<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.SlightShockDefinitionMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.mineral_pressure.dto.SlightShockDefinitionVo">
        SELECT
            point_id,
            area_name,
            passageway_amount,
            sampling_length,
            sampling_length_unit,
            sampling_frequency,
            sampling_frequency_unit,
            sensor_direction,
            sensor_direction_name,
            install_mode,
            sensitivity,
            sensitivity_unit,
            sensor_type,
            location,
            x,
            y,
            z,
            install_time
        FROM
            slight_shock_definition
        where 1=1
        <if test="entity.pointId !=null and entity.pointId !=''">
            and point_id like '%'||  #{entity.pointId} ||'%'
        </if>
        <if test="entity.areaName !=null and entity.areaName !=''">
            and area_name like '%'||  #{entity.areaName} ||'%'
        </if>
        <if test="entity.passagewayAmount !=null and entity.passagewayAmount !=''">
            and passageway_amount like '%'||  #{entity.passagewayAmount} ||'%'
        </if>
        <if test="entity.installMode !=null and entity.installMode !=''">
            and install_mode like '%'||  #{entity.installMode} ||'%'
        </if>
        <if test="entity.sensorType !=null and entity.sensorType !=''">
            and sensor_type like '%'||  #{entity.sensorType} ||'%'
        </if>
        <if test="entity.location !=null and entity.location !=''">
            and location like '%'||  #{entity.location} ||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
        order by updated_at DESC
    </select>

</mapper>
