<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.MineStressRealtimeMapper">

    <select id="selectData" resultType="com.bdtd.modules.mineral_pressure.entity.MineStressRealtime">
        select d.mine_code,
               d.point_id,
               d.state,
               d.timestamp,
               d.monitor_value
        from  mine_stress_realtime d
       <where>
            d.timestamp between #{start} and #{end}
           <if test="pointId!=null and  pointId!=''  ">
              and d.point_id=#{pointId}
           </if>
           group by point_id  ,time(#{polymerize})
        </where>
    </select>

</mapper>
