<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.SlightShockRealtimeMapper">

    <select id="selectRealtimeData" resultType="com.bdtd.modules.mineral_pressure.dto.SlightShockRealtimeVo">
        SELECT
            event_id,
            passageway_amount,
            x,
            y,
            z,
            energy,
            "level",
            "location",
            max_amplitude,
            amplitude_unit,
            avg_amplitude,
            dominant_frequency,
            dominant_frequency_unit,
            data_time,
            analysis_results
        FROM
            slight_shock_realtime
        WHERE 1=1
        <if test="entity.eventId !=null and entity.eventId !=''">
            and event_id like '%'||  #{entity.eventId} ||'%'
        </if>
        <if test="entity.passagewayAmount !=null and entity.passagewayAmount !=''">
            and passageway_amount like '%'||  #{entity.passagewayAmount} ||'%'
        </if>
        <if test="entity.location !=null and entity.location !=''">
            and location like '%'||  #{entity.location} ||'%'
        </if>
        order by updated_at DESC
    </select>

</mapper>
