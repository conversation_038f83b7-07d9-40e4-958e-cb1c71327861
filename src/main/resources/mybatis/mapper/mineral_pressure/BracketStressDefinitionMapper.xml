<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.BracketStressDefinitionMapper">
    <select id="selectBySystemId" resultType="java.util.Map">
        SELECT
            d.point_id,
            d.system_id,
            d.system_name,
            d.point_type,
            d.data_type,
            d.monitoring_area_name,
            d.sensor_status,
            d.sensor_type,
            d."location",
            d.x,
            d.y,
            d.z,
            d.decompression_value,
            d.brace_value,
            d.unit,
            d.data_time,
            r.monitor_value,
            r.updated_at
        FROM bracket_stress_definition d
            LEFT JOIN mine_stress_realtime r ON d.point_id = r.point_id
        <where>
            <if test="entity.pointId != null and entity.pointId != ''">
                and d.point_id like '%' || #{entity.pointId} || '%'
            </if>
            <if test="entity.location != null and entity.location != ''">
                and d.tunnel_name like '%' || #{entity.location} || '%'
            </if>
            <if test="entity.stationType != null and entity.stationType != ''">
                and d.station_type like '%' || #{entity.stationType} || '%'
            </if>
            <if test="entity.monitoringAreaName != null and entity.monitoringAreaName != ''">
                and d.monitoring_area_name like '%'||#{entity.monitoringAreaName}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                AND deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                AND deleted_at is not null
            </if>
        </where>
        ORDER BY r."monitor_value" IS NULL, r."updated_at" desc
    </select>

</mapper>
