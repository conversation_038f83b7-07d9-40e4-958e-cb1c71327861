<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.GroundSoundDefinitionMapper">

    <select id="selectBySystemId" resultType="com.bdtd.modules.mineral_pressure.dto.GroundSoundDefinitionVo">
        SELECT
        d.mine_code,
        d.mine_name,
        d.relation_tunnel,
        d.passageway_amount,
        d.sampling_length,
        d.sampling_length_unit,
        d.sampling_frequency,
        d.sampling_frequency_unit,
        d.sensor_direction,
        d.sensor_direction_name,
        d.install_mode,
        d.sensitivity,
        d.sensitivity_unit,
        d.sensor_type,
        d.frequency,
        d.threshold,
        d.describe,
        d.passageway_number,
        r.status,
        r.value
        FROM
        ground_sound_definition d
        LEFT JOIN ground_sound_realtime r ON d.mine_code = r.mine_code
        AND d.relation_tunnel = r.relation_tunnel
        AND d.passageway_number = r.passageway_number
        where 1=1
        <if test="entity.mineCode !=null and entity.mineCode !=''">
            and d.mine_code like '%'||  #{entity.mineCode} ||'%'
        </if>
        <if test="entity.mineName !=null and entity.mineName !=''">
            and d.mine_name like '%'||  #{entity.mineName} ||'%'
        </if>
        <if test="entity.relationTunnel !=null and entity.relationTunnel !=''">
            and d.relation_tunnel like '%'||  #{entity.relationTunnel} ||'%'
        </if>
        <if test="entity.passagewayNumber !=null and entity.passagewayNumber !=''">
            and d.passageway_number like '%'||  #{entity.passagewayNumber} ||'%'
        </if>
        order by d.updated_at DESC
    </select>

</mapper>
