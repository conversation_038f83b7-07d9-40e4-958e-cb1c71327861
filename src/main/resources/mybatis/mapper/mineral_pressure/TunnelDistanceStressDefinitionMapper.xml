<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.TunnelDistanceStressDefinitionMapper">

<select id="selectBySystemId" resultType="java.util.Map">
    select
        d.mine_code,
        d.point_id,
        d.sensor_type,
        d.x,
        d.y,
        d.z,
        d.group_code,
        d.tunnel_name,
        d.station_type,
        d.station_number,
        d.point_monitor_type,
        d.channel_no,
        d.unit,
        r.monitor_value,
        r.updated_at
    from tunnel_distance_stress_definition d
        left join mine_stress_realtime r on d.point_id = r.point_id
    <where>
        <if test="entity.pointId != null and entity.pointId != ''">
            and  d.point_id like '%'||#{entity.pointId}||'%'
        </if>
        <if test="entity.tunnelName != null and entity.tunnelName != ''">
            and d.tunnel_name like '%'||#{entity.tunnelName}||'%'
        </if>
        <if test="entity.pointMonitorType != null and entity.pointMonitorType != ''">
            and d.point_monitor_type = #{entity.pointMonitorType}
        </if>
        <if test="entity.monitoringAreaName != null and entity.monitoringAreaName != ''">
            and d.monitoring_area_name like '%'||#{entity.monitoringAreaName}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </where>
    order by r.updated_at desc
</select>
</mapper>
