<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.mineral_pressure.mapper.RoofStressDefinitionMapper">

<select id="selectBySystemId" resultType="java.util.Map">
    select d.*,
        r.monitor_value,
        r.deep_monitor_value,
        r.updated_at
    from roof_stress_definition d
        left join mine_stress_realtime r on d.point_id = r.point_id
    <where>
        <if test="entity.pointId != null and entity.pointId != ''">
            and  d.point_id like '%'||#{entity.pointId}||'%'
        </if>
        <if test="entity.location != null and entity.location != ''">
            and d.location like '%'||#{entity.location}||'%'
        </if>
        <if test="entity.tunnelName != null and entity.tunnelName != ''">
            and d.tunnel_name like '%'||#{entity.tunnelName}||'%'
        </if>
        <if test="entity.stationType != null and entity.stationType != ''">
            and d.station_type like '%'||#{entity.stationType}||'%'
        </if>
        <if test="entity.pointMonitorType != null and entity.pointMonitorType != ''">
            and d.point_monitor_type =  #{entity.pointMonitorType}
        </if>
        <if test="entity.monitoringAreaName != null and entity.monitoringAreaName != ''">
            and d.monitoring_area_name like '%'||#{entity.monitoringAreaName}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </where>
    ORDER BY r."monitor_value" IS NULL, r."updated_at" desc
</select>

    <select id="selectAll" resultType="java.util.Map" >
        select * from mine_stress_all;
    </select>
</mapper>
