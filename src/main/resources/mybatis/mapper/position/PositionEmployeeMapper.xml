<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionEmployeeMapper">

    <select id="findPositionEmployeeByDepartment" resultType="com.bdtd.modules.position.entity.PositionEmployee">
        SELECT department
        FROM position_employee
        WHERE department is not null
            AND department != ''
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
        GROUP BY department
    </select>

    <select id="listWithValue" resultType="com.bdtd.modules.position.entity.PositionStationDefinition">
        SELECT *
        FROM position_station_definition
        <where>
            <if test="stationCode != null and stationCode != ''">
                AND station_code like '%'||#{stationCode}||'%'
            </if>
            <if test="stationName != null and stationName != ''">
                AND station_name like '%'||#{stationName}||'%'
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                AND deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                AND deleted_at is not null
            </if>
        </where>
    </select>

    <select id="selectLeaderIds" resultType="java.lang.String">
        SELECT card_id
        FROM position_employee
        WHERE leader = 1
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>
</mapper>
