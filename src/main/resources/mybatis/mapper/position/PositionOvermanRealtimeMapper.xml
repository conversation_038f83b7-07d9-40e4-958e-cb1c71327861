<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionOvermanRealtimeMapper">

    <select id="getOverManAlarm" resultType="com.bdtd.util.web.Msg">
        SELECT *
        FROM position_overman_realtime
        <where>
            collect_status = 100
            <if test="areaCode != null and areaCode != ''">
                AND area_code = #{areaCode}
            </if>
        </where>
    </select>


    <select id="getPositionOverManRealtimeList"  resultType="com.bdtd.modules.position.entity.PositionOvermanRealtime">
        SELECT *
        FROM position_overman_realtime
        WHERE collect_status = 100
        <if test="query.type != null and query.type != ''">
            AND 'type' =#{query.type}
        </if>
    </select>

    <select id="getLastAreaOverManRealtimeList" resultType="com.bdtd.modules.position.entity.PositionOvermanRealtime">
        SELECT DISTINCT ON(area_code) *
        FROM position_overman_realtime
        WHERE collect_status = 100
            AND type = '区域超员'
            AND (end_time isnull OR end_time = '1900-01-01 00:00:00')
        ORDER BY area_code, begin_time DESC
    </select>
</mapper>
