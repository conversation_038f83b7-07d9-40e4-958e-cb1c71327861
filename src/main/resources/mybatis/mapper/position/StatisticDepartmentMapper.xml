<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.StatisticDepartmentMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.position.entity.StatisticDepartment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="timestamp" jdbcType="TIMESTAMP" property="timestamp" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, count, timestamp
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.position.dto.StatisticDepartmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from statistic_department
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_department
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statistic_department
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bdtd.modules.position.entity.StatisticDepartment">
    insert into statistic_department ( name, count,
      timestamp)
    values (#{name,jdbcType=VARCHAR}, #{count,jdbcType=INTEGER},
      #{timestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bdtd.modules.position.entity.StatisticDepartment">
    insert into statistic_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="timestamp != null">
        timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="timestamp != null">
        #{timestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bdtd.modules.position.entity.StatisticDepartment">
    update statistic_department
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="timestamp != null">
        timestamp = #{timestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bdtd.modules.position.entity.StatisticDepartment">
    update statistic_department
    set name = #{name,jdbcType=VARCHAR},
      count = #{count,jdbcType=INTEGER},
      timestamp = #{timestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTime" resultType="HashMap">
    select * from statistic_department where timestamp <![CDATA[ >= ]]> #{startTime} and timestamp <![CDATA[ <= ]]> #{endTime}
  </select>


</mapper>