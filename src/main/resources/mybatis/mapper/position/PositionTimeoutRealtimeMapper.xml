<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionTimeoutRealtimeMapper">

    <select id="findPositionTimeoutRealtimeByQuery"  resultType="com.bdtd.modules.position.entity.PositionTimeoutRealtime">
            SELECT
                *
            FROM
                position_timeout_realtime
                <where>
                    1=1
                    <if test="query.department != null and query.department != ''">
                        and department = #{query.department}
                    </if>
                </where>
    </select>

</mapper>
