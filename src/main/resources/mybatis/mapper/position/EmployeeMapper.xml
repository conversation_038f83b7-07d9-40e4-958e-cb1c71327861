<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.EmployeeMapper">

    <select id="getDetail" resultType="com.bdtd.modules.position.entity.Employee">
        select * from employee
        <where>
            <if test="cardId != null and cardId != ''">
                and card_id like '%'||#{cardId}||'%'
            </if>
        </where>
    </select>
</mapper>
