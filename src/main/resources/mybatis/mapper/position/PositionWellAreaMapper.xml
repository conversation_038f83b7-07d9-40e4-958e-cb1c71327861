<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionWellAreaMapper">

    <select id="findPositionWellAreaByAreaCode" resultType="com.bdtd.modules.position.entity.PositionWellArea">
        SELECT
        mine_code,
        mine_name,
        group_code,
        group_name,
        area_type,
        area_name,
        area_code,
        data_time,
        limit_person,
        station_count
        FROM
        position_well_area
        <where>
            1=1
            <if test="areaName != null and areaName != ''">
                and area_name = #{areaName}
            </if>
            <if test="areaCode != null and areaCode != ''">
                and area_code = #{areaCode}
            </if>
        </where>
    </select>

</mapper>
