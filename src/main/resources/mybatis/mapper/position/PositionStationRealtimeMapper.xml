<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionStationRealtimeMapper">


    <!--条件查询：基站编码，基站名称模糊分页查询-->
    <select id="findPage"  resultType="com.bdtd.modules.position.dto.PositionStationRealtimeQuery">
        select r.station_code,
        d.station_name,
        r.run_status,
        r.power_status,
        r.collect_time
        from position_station_realtime r inner join position_station_definition d on  r.station_code=d.station_code
        <where>
            <if test="code != null and code != '' ">
                r.station_code like '%'||#{code}||'%'
            </if>
            <if test="name != null and name != '' ">
                and d.station_name like '%'||#{name}||'%'
            </if>
        </where>
        <if test="pageSize != null and startIndex != null   ">
            limit #{pageSize} offset #{startIndex} ;
        </if>
    </select>

    <!--条件查询：基站编码，基站名称模糊查询-->
    <select id="findList" resultType="com.bdtd.modules.position.dto.PositionStationRealtimeQuery">
        select r.station_code,
        d.station_name,
        r.run_status,
        r.power_status,
        r.collect_time
        from position_station_realtime r inner join position_station_definition d on  r.station_code=d.station_code
        <where>
            <if test="code != null and code != '' ">
                r.station_code like '%'||#{code}||'%'
            </if>
            <if test="name != null and name != '' ">
                and d.station_name like '%'||#{name}||'%'
            </if>
        </where>
    </select>


    <select id="findTotal" resultType="Integer">
        select count(r.station_code)
        from position_station_realtime r inner join position_station_definition d on  r.station_code=d.station_code
        <where>
            <if test="code != null and code != '' ">
                r.station_code like '%'||#{code}||'%'
            </if>
            <if test="name != null and name != '' ">
                and d.station_name like '%'||#{name}||'%'
            </if>
        </where>
    </select>

</mapper>
