<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.position.mapper.PositionRealtimeMapper">
    <resultMap id="PositionRealtimeVoMap" type="com.bdtd.modules.position.dto.PositionRealtimeVo">
        <result column="station_code" property="stationCode" />
        <result column="station_name" property="stationName" />
        <result column="department" property="department" />
        <result column="count" property="count" />

        <!-- collection 标签需放在最后 -->
        <collection property="personalList" resultMap="PersonalVoMap"/>
    </resultMap>

    <resultMap id="PersonalVoMap" type="com.bdtd.modules.position.vo.PersonalVo">
        <result column="person_name" property="personName" />
        <result column="job" property="job" />
        <result column="down_time" property="downTime" />
    </resultMap>

    <!--
    mod by wang<PERSON><PERSON> 修改人员矿领导标识等获取逻辑，优先取人员信息的字段 #I50BD3
    -->
    <select id="findPositionRealtimeByDepartment" resultType="com.bdtd.modules.position.entity.PositionRealtime" parameterType="com.bdtd.modules.position.dto.PositionRealtimeQuery">
        SELECT
            *
        FROM (
            SELECT distinct on (r.card_id)
                r.mine_code,
                r.mine_name,
                r.group_code,
                r.group_name,
                r.card_id,
                r.person_name,
                r.ry_well_code,
                r.down_time,
                r.up_time,
                r.area_code,
                r.area_name,
                r.in_area_time,
                r.station_code,
                r.station_name,
                r.in_station_time,
                r.class_set_type,
                r.station_distance,
                r.work_status,
                coalesce(e.department, r.department) as department,
                coalesce(e.job, r.job) as job,
                coalesce(e.work_kind, r.work_kind) as work_kind,
                coalesce(e.leader, r.leader) as leader,
                coalesce(e.special_person, r.special_person) as special_person,
                r.track,
                r.class_set_code,
                r.class_set_name,
                r.data_time,
                r.insert_db_time,
                r.update_db_time,
                r.collect_status,
                r.geometry_point,
                r.tunnel_code,
                r.tunnel_name,
                r.tunnel_distance,
                r.x,
                r.y,
                r.z
            FROM
                position_realtime r
                LEFT JOIN position_employee e on r.card_id = e.card_id
            <where>
                r.collect_status = 100
                <if test="query.department != null and query.department != ''">
                    and coalesce(e.department, r.department) = #{query.department}
                </if>
                <if test="query.stationCode != null and query.stationCode != ''">
                    and r.station_code = #{query.stationCode}
                </if>
                <if test="query.job != null and query.job != ''">
                    and coalesce(e.job, r.job) = #{query.job}
                </if>
                <if test="query.workKind != null and query.workKind != ''">
                    and coalesce(e.work_kind, r.work_kind) = #{query.workKind}
                </if>
                <if test="query.personName != null and query.personName != ''">
                    and r.person_name like '%'||#{query.personName}||'%'
                </if>
                <if test="query.leader != null">
                    and coalesce(e.leader, r.leader) = #{query.leader}
                </if>
                <if test="query.specialPerson != null ">
                    and coalesce(e.special_person, r.special_person) = #{query.specialPerson}
                </if>
                <if test="query.areaCode != null and query.areaCode != ''">
                    and r.area_code = #{query.areaCode}
                </if>
                <if test="query.areaName != null and query.areaName != ''">
                    and r.area_name = #{query.areaName}
                </if>
                <if test="query.cardId != null and query.cardId != ''">
                    and r.card_id = #{query.cardId}
                </if>
                <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                    and e.deleted_at is null
                </if>
                <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                    and e.deleted_at is not null
                </if>
            </where>
        ) AS f
        ORDER BY data_time DESC
    </select>

    <select id="findPositionRealtimeByStationCode" resultMap="PositionRealtimeVoMap">
        SELECT
            a.station_name,
            a.station_code,
            CASE WHEN b.total is null THEN 0 ELSE b.total END as count,
            c.person_name,c.job,c.down_time
        FROM
            (
                select station_name, station_code
                from position_station_definition
                <where>
                    collect_status = 100
                    <if test="stationName != null and stationName != ''">
                        and station_name like '%'||#{stationName}||'%'
                    </if>
                    <if test="stationCode != null and stationCode != ''">
                        and station_code = #{stationCode}
                    </if>
                    <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                        and deleted_at is null
                    </if>
                    <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                        and deleted_at is not null
                    </if>
                </where>
            ) a
            LEFT JOIN (
                SELECT
                    station_name,
                    COUNT ( * ) AS total,
                    station_code
                FROM
                    position_realtime
                WHERE collect_status = 100
                GROUP BY station_name, station_code
                ORDER BY station_code ASC
            ) b on a.station_code = b.station_code
            LEFT JOIN position_realtime c ON a.station_code = c.station_code
    </select>

    <select id="findDepartment" resultType="com.bdtd.modules.position.dto.PositionRealtimeVo">
        SELECT
            department,
            count(*) as count
        FROM position_realtime
        <where>
            collect_status = 100
            <if test="department != null and department != ''">
                and department like '%'||#{department}||'%'
            </if>
        </where>
        group by department
    </select>

    <select id="departmentCardDetailedList" resultType="com.bdtd.modules.position.entity.PositionRealtime">
        SELECT *
        FROM position_realtime
        <where>
            collect_status = 100
            <if test="department != null and department != ''">
                and department = #{department}
            </if>
        </where>
    </select>

    <select id="stationCodeCardDetailedList" resultType="com.bdtd.modules.position.entity.PositionRealtime">
        SELECT *
        FROM position_realtime
        <where>
            collect_status = 100
            <if test="stationCode != null and stationCode != ''">
                and station_code = #{stationCode}
            </if>
            <if test="personName != null and personName != ''">
                and person_name = #{personName}
            </if>
        </where>
    </select>

    <!--
    mod by wangzhiwei 修复井下不显示矿领导的问题，修改人员矿领导标识等获取逻辑，优先取人员信息的字段 #I4ZFUH
    -->
    <select id="selectPositionRealtimeList" resultType="com.bdtd.modules.position.entity.PositionRealtime">
        SELECT
            r.mine_code,
            r.mine_name,
            r.group_code,
            r.group_name,
            r.card_id,
            r.person_name,
            r.ry_well_code,
            r.down_time,
            r.up_time,
            r.area_code,
            r.area_name,
            r.in_area_time,
            r.station_code,
            r.station_name,
            r.in_station_time,
            r.class_set_type,
            r.station_distance,
            r.work_status,
            COALESCE(e.department, r.department) as department,
            COALESCE(e.job, r.job) as job,
            COALESCE(e.work_kind, r.work_kind) as work_kind,
            COALESCE(e.leader, r.leader) as leader,
            COALESCE(e.special_person, r.special_person) as special_person,
            r.track,
            r.class_set_code,
            r.class_set_name,
            r.data_time,
            r.insert_db_time,
            r.update_db_time,
            r.collect_status,
            r.geometry_point,
            r.tunnel_code,
            r.tunnel_name,
            r.tunnel_distance,
            r.x,
            r.y,
            r.z
        FROM
            position_realtime r
            left join position_employee e on r.card_id = e.card_id
        <where>
            r.collect_status = 100
            <if test="stationCode != null and stationCode != ''">
                and r.station_code = #{stationCode}
            </if>
            <if test="personName != null and personName != ''">
                and r.person_name = #{personName}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and e.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and e.deleted_at is not null
            </if>
        </where>
    </select>

    <select id="findRealtimeListByCardIds" resultType="com.bdtd.modules.position.entity.PositionRealtime">
        SELECT
            r.mine_code,
            r.mine_name,
            r.group_code,
            r.group_name,
            r.card_id,
            r.person_name,
            r.ry_well_code,
            r.down_time,
            r.up_time,
            r.area_code,
            r.area_name,
            r.in_area_time,
            r.station_code,
            r.station_name,
            r.in_station_time,
            r.class_set_type,
            r.station_distance,
            r.work_status,
            COALESCE(e.department, r.department) as department,
            COALESCE(e.job, r.job) as job,
            COALESCE(e.work_kind, r.work_kind) as work_kind,
            COALESCE(e.leader, r.leader) as leader,
            COALESCE(e.special_person, r.special_person) as special_person,
            r.track,
            r.class_set_code,
            r.class_set_name,
            r.data_time,
            r.insert_db_time,
            r.update_db_time,
            r.collect_status,
            r.geometry_point,
            r.tunnel_code,
            r.tunnel_name,
            r.tunnel_distance,
            r.x,
            r.y,
            r.z
        FROM
            position_realtime r
                left join position_employee e on r.card_id = e.card_id
        <where>
            r.collect_status = 100
            and r.card_id IN <foreach collection="cardIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        </where>
    </select>

    <select id="departmentList" resultType="java.lang.String">
        SELECT DISTINCT ON(department) department
        FROM position_employee
        WHERE department != ''
        <if test="key != null and key != ''">
            AND department like '%'||#{key}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>

    <select id="nameList" resultType="java.lang.String">
        SELECT DISTINCT ON(person_name) person_name
        FROM position_employee
        WHERE person_name != ''
        <if test="key != null and key != ''">
            AND person_name like '%'||#{key}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>

    <select id="jobList" resultType="java.lang.String">
        SELECT DISTINCT ON(job) job
        FROM position_employee
        WHERE job != ''
        <if test="key != null and key != ''">
            AND job like '%'||#{key}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>

    <select id="areaNameList" resultType="java.lang.String">
        SELECT DISTINCT ON(area_name) area_name
        FROM position_realtime
        WHERE collect_status = 100
            AND area_name != ''
        <if test="key != null and key != ''">
            AND area_name like '%'||#{key}||'%'
        </if>
    </select>

    <select id="workKindList" resultType="java.lang.String">
        SELECT DISTINCT ON(work_kind) work_kind
        FROM position_employee
        WHERE work_kind != ''
        <if test="key != null and key != ''">
            AND work_kind like '%'||#{key}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>

    <select id="cardList" resultType="java.lang.String">
        SELECT DISTINCT ON(card_id) card_id
        FROM position_employee
        WHERE card_id != ''
        <if test="key != null and key != ''">
            AND card_id like '%'||#{key}||'%'
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
            AND deleted_at is null
        </if>
        <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
            AND deleted_at is not null
        </if>
    </select>

    <!--
    mod by wangzhiwei 修改人员矿领导标识等获取逻辑，优先取人员信息的字段 #I50BD3 -->
    <select id="historyDownList" resultType="com.bdtd.modules.position.entity.PositionRealtime">
        SELECT DISTINCT ON (e.card_id)
            r.mine_code,
            r.mine_name,
            r.group_code,
            r.group_name,
            e.card_id,
            e.person_name,
            r.ry_well_code,
            r.down_time,
            r.up_time,
            r.area_code,
            r.area_name,
            r.in_area_time,
            r.station_code,
            r.station_name,
            r.in_station_time,
            r.class_set_type,
            r.station_distance,
            r.work_status,
            coalesce(e.department, r.department) as department,
            coalesce(e.job, r.job) as job,
            coalesce(e.work_kind, r.work_kind) as work_kind,
            coalesce(e.leader, r.leader) as leader,
            coalesce(e.special_person, r.special_person) as special_person,
            r.track,
            r.class_set_code,
            r.class_set_name,
            r.data_time,
            r.insert_db_time,
            r.update_db_time,
            r.collect_status,
            r.geometry_point,
            r.tunnel_code,
            r.tunnel_name,
            r.tunnel_distance,
            r.x,
            r.y,
            r.z
        FROM
            position_employee e
            LEFT JOIN position_realtime r ON r.card_id = e.card_id
        <where>
            (r.down_time is null and r.collect_status is null or r.down_time is not null and r.collect_status = 100)
            <if test="query.department != null and query.department != ''">
                and e.department = #{query.department}
            </if>
            <if test="query.job != null and query.job != ''">
                and e.job = #{query.job}
            </if>
            <if test="query.workKind != null and query.workKind != ''">
                and e.work_kind = #{query.workKind}
            </if>
            <if test="query.personName != null and query.personName != ''">
                and e.person_name like '%'||#{query.personName}||'%'
            </if>
            <if test="query.leader != null and query.leader != ''">
                and e.leader = #{query.leader}
            </if>
            <if test="query.specialPerson != null and query.specialPerson != ''">
                and e.special_person = #{query.specialPerson}
            </if>
            <if test="query.stationCode != null and query.stationCode != ''">
                and r.station_code = #{query.stationCode}
            </if>
            <if test="query.areaCode != null and query.areaCode != ''">
                and r.area_code = #{query.areaCode}
            </if>
            <if test="query.areaName != null and query.areaName != ''">
                and r.area_name = #{query.areaName}
            </if>
            <if test="query.cardId != null and query.cardId != ''">
                and r.card_id = #{query.cardId}
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 1">
                and e.deleted_at is null
            </if>
            <if test="deletedAtIsNull != null and deletedAtIsNull == 0">
                and e.deleted_at is not null
            </if>
        </where>
    </select>

    <select id="realtimeCardList" resultType="java.lang.String">
        select distinct on(card_id) card_id
        from position_realtime
        where collect_status = 100 and card_id != ''
    </select>
</mapper>
