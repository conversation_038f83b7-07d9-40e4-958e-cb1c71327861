<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WfpMineLocationManufacturerMapper">
    <select id="findById" resultType="com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer">
        select
            wmlm.id,
            wmlm.group_code,
            wmlm.mine_code,
            wmlm.manufacturer_id,
            coalesce(wmlm.feat_signal, wlm.feat_signal) as feat_signal,
            coalesce(wmlm.feat_power, wlm.feat_power) as feat_power,
            coalesce(wmlm.feat_power_alarm, wlm.feat_power_alarm) as feat_power_alarm,
            wmlm.create_time,
            wmlm.create_user,
            wmlm.update_time,
            wmlm.update_user,
            wmlm.deleted_at,
            cmg."name" as group_name,
            cmn."name" as mine_name,
            wlm."name" as manufacturer_name,
            wlm.full_name as manufacturer_full_name,
            wlm.contact as manufacturer_contact,
            wlm.contact_tel as manufacturer_contact_tel
        from wfp_mine_location_manufacturer wmlm
            left join cms_mine_group cmg on wmlm.group_code = cmg.code
            left join cms_mine_name cmn on wmlm.mine_code = cmn.code
            left join wfp_location_manufacturer wlm on wmlm.manufacturer_id = wlm.id
        <where>
            <if test="id != null and id != ''">
                and wmlm.id = #{id}
            </if>
        </where>
    </select>

    <select id="findList" resultType="com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer">
        select
            wmlm.id,
            wmlm.group_code,
            wmlm.mine_code,
            wmlm.manufacturer_id,
            coalesce(wmlm.feat_signal, wlm.feat_signal) as feat_signal,
            coalesce(wmlm.feat_power, wlm.feat_power) as feat_power,
            coalesce(wmlm.feat_power_alarm, wlm.feat_power_alarm) as feat_power_alarm,
            wmlm.create_time,
            wmlm.create_user,
            wmlm.update_time,
            wmlm.update_user,
            wmlm.deleted_at,
            cmg."name" as group_name,
            cmg.sort as group_sort,
            cmn."name" as mine_name,
            cmn.sort as mine_sort,
            wlm."name" as manufacturer_name,
            wlm.full_name as manufacturer_full_name,
            wlm.contact as manufacturer_contact,
            wlm.contact_tel as manufacturer_contact_tel
        from wfp_mine_location_manufacturer wmlm
            left join cms_mine_group cmg on wmlm.group_code = cmg.code
            left join cms_mine_name cmn on wmlm.mine_code = cmn.code
            left join wfp_location_manufacturer wlm on wmlm.manufacturer_id = wlm.id
        ${ew.customSqlSegment}
    </select>

    <select id="findPage" resultType="com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer">
        select
            wmlm.id,
            wmlm.group_code,
            wmlm.mine_code,
            wmlm.manufacturer_id,
            coalesce(wmlm.feat_signal, wlm.feat_signal) as feat_signal,
            coalesce(wmlm.feat_power, wlm.feat_power) as feat_power,
            coalesce(wmlm.feat_power_alarm, wlm.feat_power_alarm) as feat_power_alarm,
            wmlm.create_time,
            wmlm.create_user,
            wmlm.update_time,
            wmlm.update_user,
            wmlm.deleted_at,
            cmg."name" as group_name,
            cmg.sort as group_sort,
            cmn."name" as mine_name,
            cmn.sort as mine_sort,
            wlm."name" as manufacturer_name,
            wlm.full_name as manufacturer_full_name,
            wlm.contact as manufacturer_contact,
            wlm.contact_tel as manufacturer_contact_tel
        from wfp_mine_location_manufacturer wmlm
            left join cms_mine_group cmg on wmlm.group_code = cmg.code
            left join cms_mine_name cmn on wmlm.mine_code = cmn.code
            left join wfp_location_manufacturer wlm on wmlm.manufacturer_id = wlm.id
        ${ew.customSqlSegment}
    </select>

</mapper>
