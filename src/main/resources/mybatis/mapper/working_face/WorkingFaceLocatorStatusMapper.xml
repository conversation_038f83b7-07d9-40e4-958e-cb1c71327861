<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceLocatorStatusMapper">

    <select id="selectCompositedPage"
            resultType="com.bdtd.modules.working_face.dto.WorkingFaceLocatorStatusComposite">
        select
            wfpd.group_code
          , cmg."name" as group_name
          , wfpd.mine_code
          , cmn."name" as mine_name
          , wfpd.system_name as working_face_name
          , t.working_face_id
          , t.label_code
          , t.state
          , t.data_time
          , t.collect_time
          , t.power_value
          , t.power_alarm
          , t.signal_value
          , wfpd.work_mode
          , coalesce(wmlm.feat_signal, wlm.feat_signal) as feat_signal
          , coalesce(wmlm.feat_power, wlm.feat_power) as feat_power
          , coalesce(wmlm.feat_power_alarm, wlm.feat_power_alarm) as feat_power_alarm
          , wfld.device_type
        from
          crosstab(
            $$
              with temp_a as (
                select
                  *,
                  row_number() over(
                    partition by working_face_id, label_code, monitor_type
                    order by data_time desc
                  ) as rank
                from working_face_locator_status
                where monitor_type in ('power_value', 'power_alarm', 'signal_value')
                  <if test="query.groupCode != null and query.groupCode != ''">
                    and group_code = '${query.groupCode}'
                  </if>
                  <if test="query.mineCode != null and query.mineCode != ''">
                    and mine_code = '${query.mineCode}'
                  </if>
                  <if test="query.workingFaceId != null and query.workingFaceId != ''">
                    and working_face_id = '${query.workingFaceId}'
                  </if>
                  <if test="query.workingFaceIdList != null and query.workingFaceIdList.size > 0">
                    and working_face_id in
                    <foreach collection="query.workingFaceIdList" item="item" open="(" separator="," close=")">'${item}'</foreach>
                  </if>
                  <if test="query.state != null and query.state != '' and query.state != 'all'">
                    and state = '${query.state}'
                  </if>
              )
              select
                array[working_face_id , label_code]::text[]
                , working_face_id
                , label_code
                , state
                , data_time
                , collect_time
                , monitor_type
                , value
              from temp_a
              where rank = 1
              order by 1, 2, 3;
            $$,
            $$  values ('power_value'), ('power_alarm'), ('signal_value');  $$
          ) as t (
              rn text[]
            , working_face_id text
            , label_code text
            , state text
            , data_time timestamp
            , collect_time timestamp
            , power_value text
            , power_alarm text
            , signal_value text
          )      join working_face_progress_definition wfpd on t.working_face_id = wfpd.working_face_id
            left join cms_mine_group cmg on wfpd.group_code = cmg.code
            left join cms_mine_name cmn on wfpd.mine_code = cmn.code
            left join wfp_mine_location_manufacturer wmlm on wfpd.mine_code = wmlm.mine_code
            left join wfp_location_manufacturer wlm on wmlm.manufacturer_id = wlm.id
            left join working_face_locator_device wfld on t.working_face_id = wfld.working_face_id and t.label_code = wfld.device_code
        <where>
          <if test='query.systemFlag != null and query.systemFlag == "1"'>
            and exists (select 1 from model_system ms where t.working_face_id = ms.source_system_code and ms.flag = '1')
          </if>
          <if test='query.systemFlag != null and query.systemFlag == "0"'>
            and exists (select 1 from model_system ms where t.working_face_id = ms.source_system_code and ms.flag = '0')
          </if>
          <if test="query.workingFaceType != null and query.workingFaceType != ''">
            and wfpd.working_face_type = #{query.workingFaceType}
          </if>
          <if test="query.workModeIsNull != null and query.workModeIsNull == 1">
            and wfpd.work_mode is null
          </if>
          <if test="query.workModeIsNull != null and query.workModeIsNull == 0">
            and wfpd.work_mode is not null
          </if>
        </where>
        order by t.data_time desc, wfpd.group_code, wfpd.mine_code
    </select>

    <!--
    <select id="selectPageTest1"
            resultType="com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus">
        select *
        from working_face_locator_status
        <where>
          <if test="qe.groupCode != null and qe.groupCode != ''">
            and group_code = #{qe.groupCode}
          </if>
          <if test="qe.mineCode != null and qe.mineCode != ''">
            and mine_code = #{qe.mineCode}
          </if>
        </where>
    </select>
    <select id="selectPageTest2"
            resultType="com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus">
        select *
        from working_face_locator_status
        ${qw.customSqlSegment}
    </select> -->
</mapper>
