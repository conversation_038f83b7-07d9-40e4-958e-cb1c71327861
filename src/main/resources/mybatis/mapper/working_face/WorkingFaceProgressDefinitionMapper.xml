<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceProgressDefinitionMapper">
    <!-- 通用查询映射结果 -->
    <resultMap
            id="workingFaceProgressDefinitionResultMap"
            type="com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition"
    >
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="working_face_id" property="workingFaceId"/> <!-- 工作面ID -->
        <result column="working_face_type" property="workingFaceType"/> <!-- 工作面类型 -->
        <result column="working_face_name" property="workingFaceName"/> <!-- 工作面名称 -->
        <result column="station_code" property="stationCode"/> <!-- 基站编码 -->
        <result column="station_name" property="stationName"/> <!-- 基站名称 -->
        <result column="tunnel_name" property="tunnelName"/> <!-- 基站所在巷道名称 -->
        <result
                column="tunnel_coordinates"
                property="tunnelCoordinates"
                typeHandler="com.bdtd.util.sql.GeometryTypeHandler"
        /> <!-- 巷道位置 -->
        <result column="memo" property="memo"/> <!-- 备注 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
        <result column="deleted_at" property="deletedAt"/> <!-- 删除时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="workingFaceProgressDefinitionResultMap">
        select *
        from working_face_progress_definition
        <if test="systemId != null and systemId !=''">
            WHERE system_code LIKE '%'|| #{systemId} ||'%'
        </if>
    </select>

    <select id="selectListWithValues" resultType="java.util.Map">
        select wsd.group_code,
               wsd.group_name,
               wsd.mine_code,
               wsd.mine_name,
               wsd.system_code,
               wsd.system_name,
               wsd.working_face_id,
               wsd.working_face_type,
               wsd.working_face_name,
               wsd.station_code,
               wsd.station_name,
               wsd.tunnel_name,
               ST_ASGEOJSON(wsd.tunnel_coordinates) AS tunnel_coordinates,
               wsd.memo,
               ST_ASGEOJSON(wsr.machine_pos)        AS machine_pos,
               ST_ASGEOJSON(wsr.trans_pos)          AS trans_pos,
               ST_ASGEOJSON(wsr.air_pos)            AS air_pos,
               wsr.average_speed,
               wsr.sequence_number,
               wsr.footage,
               wsr.alarm_distance,
               wsr.compensation,
               wsr.state,
               wsr.data_time,
               wsr.collect_time,
               wsr.collect_status
        from working_face_progress_definition wsd
             join working_face_progress_realtime wsr on wsd.working_face_id = wsr.working_face_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectListAcqEnabled" resultType="com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition">
        select wsd.*
        from working_face_progress_definition wsd
            join model_system ms on
                    wsd.working_face_id = ms.source_system_code
                and ms.sid = 990 
                and ms.flag = '1'
        ${ew.customSqlSegment}
    </select>

    <select id="selectPageWithValues" resultType="java.util.Map">
        select wsd.group_code,
               wsd.group_name,
               wsd.mine_code,
               wsd.mine_name,
               wsd.system_code,
               wsd.system_name,
               wsd.working_face_id,
               wsd.working_face_type,
               wsd.working_face_name,
               wsd.station_code,
               wsd.station_name,
               wsd.tunnel_name,
               ST_ASGEOJSON(wsd.tunnel_coordinates) AS tunnel_coordinates,
               wsd.memo,
               ST_ASGEOJSON(wsr.machine_pos)        AS machine_pos,
               ST_ASGEOJSON(wsr.trans_pos)          AS trans_pos,
               ST_ASGEOJSON(wsr.air_pos)            AS air_pos,
               wsr.average_speed,
               wsr.sequence_number,
               wsr.footage,
               wsr.alarm_distance,
               wsr.compensation,
               wsr.state,
               wsr.data_time,
               wsr.collect_time,
               wsr.collect_status
        from working_face_progress_definition wsd
             join working_face_progress_realtime wsr on wsd.working_face_id = wsr.working_face_id
        ${ew.customSqlSegment}
    </select>

    <insert id="upsertWfpdBatch">
        INSERT INTO working_face_progress_definition (
            group_code,
            group_name,
            mine_code,
            mine_name,
            system_code,
            system_name,
            working_face_id,
            working_face_type,
            working_face_name,
            work_mode,
            work_mode_changed,
            work_mode_changed_at,
            work_state,
            memo,
            data_time,
            collect_time,
            collect_status,
            created_at,
            updated_at,
            deleted_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.groupCode, jdbcType=VARCHAR},
                #{item.groupName, jdbcType=VARCHAR},
                #{item.mineCode, jdbcType=VARCHAR},
                #{item.mineName, jdbcType=VARCHAR},
                #{item.systemCode, jdbcType=VARCHAR},
                #{item.systemName, jdbcType=VARCHAR},
                #{item.workingFaceId, jdbcType=VARCHAR},
                #{item.workingFaceType, jdbcType=VARCHAR},
                #{item.workingFaceName, jdbcType=VARCHAR},
                #{item.workMode, jdbcType=VARCHAR},
                #{item.workModeChanged, jdbcType=VARCHAR},
                #{item.workModeChangedAt, jdbcType=TIMESTAMP},
                #{item.workState, jdbcType=VARCHAR},
                #{item.memo, jdbcType=VARCHAR},
                #{item.dataTime, jdbcType=TIMESTAMP},
                #{item.collectTime, jdbcType=TIMESTAMP},
                #{item.collectStatus, jdbcType=INTEGER},
                #{item.createdAt, jdbcType=TIMESTAMP},
                #{item.updatedAt, jdbcType=TIMESTAMP},
                #{item.deletedAt, jdbcType=TIMESTAMP}
            </trim>
        </foreach>
        on conflict(working_face_id) do update set
            working_face_type = excluded.working_face_type,
            working_face_name = excluded.working_face_name,
            memo = excluded.memo,
            data_time = excluded.data_time,
            collect_time = excluded.collect_time,
            updated_at = case when excluded.updated_at is not null then excluded.updated_at else now() end,
            deleted_at = case when excluded.deleted_at is not null then excluded.deleted_at else working_face_progress_definition.deleted_at end
    </insert>

    <!-- 旧版使用SQL判断工作模式变更
    <insert id="upsertWfpdBatchWithWorkMode">
        INSERT INTO working_face_progress_definition (
            group_code,
            group_name,
            mine_code,
            mine_name,
            system_code,
            system_name,
            working_face_id,
            working_face_type,
            working_face_name,
            work_mode,
            work_mode_changed,
            work_mode_changed_at,
            memo,
            data_time,
            collect_time,
            collect_status,
            created_at,
            updated_at,
            deleted_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.groupCode, jdbcType=VARCHAR},
                #{item.groupName, jdbcType=VARCHAR},
                #{item.mineCode, jdbcType=VARCHAR},
                #{item.mineName, jdbcType=VARCHAR},
                #{item.systemCode, jdbcType=VARCHAR},
                #{item.systemName, jdbcType=VARCHAR},
                #{item.workingFaceId, jdbcType=VARCHAR},
                #{item.workingFaceType, jdbcType=VARCHAR},
                #{item.workingFaceName, jdbcType=VARCHAR},
                #{item.workMode, jdbcType=VARCHAR},
                #{item.workModeChanged, jdbcType=VARCHAR},
                #{item.workModeChangedAt, jdbcType=TIMESTAMP},
                #{item.memo, jdbcType=VARCHAR},
                #{item.dataTime, jdbcType=TIMESTAMP},
                #{item.collectTime, jdbcType=TIMESTAMP},
                #{item.collectStatus, jdbcType=INTEGER},
                #{item.createdAt, jdbcType=TIMESTAMP},
                #{item.updatedAt, jdbcType=TIMESTAMP},
                #{item.deletedAt, jdbcType=TIMESTAMP}
            </trim>
        </foreach>
        on conflict(working_face_id) do update set
            working_face_type = excluded.working_face_type,
            working_face_name = excluded.working_face_name,
            work_mode = excluded.work_mode,
            work_mode_changed = case when excluded.work_mode = working_face_progress_definition.work_mode then working_face_progress_definition.work_mode_changed else '1' end,
            work_mode_changed_at = case when excluded.work_mode = working_face_progress_definition.work_mode then working_face_progress_definition.work_mode_changed_at else now() end,
            memo = excluded.memo,
            data_time = excluded.data_time,
            collect_time = excluded.collect_time,
            updated_at = excluded.updated_at,
            deleted_at = excluded.deleted_at
    </insert> -->
    <insert id="upsertWfpdBatchWithWorkMode">
        INSERT INTO working_face_progress_definition (
            group_code,
            group_name,
            mine_code,
            mine_name,
            system_code,
            system_name,
            working_face_id,
            working_face_type,
            working_face_name,
            work_mode,
            work_mode_changed,
            work_mode_changed_at,
            work_state,
            memo,
            data_time,
            collect_time,
            collect_status,
            created_at,
            updated_at,
            deleted_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.groupCode, jdbcType=VARCHAR},
                #{item.groupName, jdbcType=VARCHAR},
                #{item.mineCode, jdbcType=VARCHAR},
                #{item.mineName, jdbcType=VARCHAR},
                #{item.systemCode, jdbcType=VARCHAR},
                #{item.systemName, jdbcType=VARCHAR},
                #{item.workingFaceId, jdbcType=VARCHAR},
                #{item.workingFaceType, jdbcType=VARCHAR},
                #{item.workingFaceName, jdbcType=VARCHAR},
                #{item.workMode, jdbcType=VARCHAR},
                #{item.workModeChanged, jdbcType=VARCHAR},
                #{item.workModeChangedAt, jdbcType=TIMESTAMP},
                #{item.workState, jdbcType=VARCHAR},
                #{item.memo, jdbcType=VARCHAR},
                #{item.dataTime, jdbcType=TIMESTAMP},
                #{item.collectTime, jdbcType=TIMESTAMP},
                #{item.collectStatus, jdbcType=INTEGER},
                #{item.createdAt, jdbcType=TIMESTAMP},
                #{item.updatedAt, jdbcType=TIMESTAMP},
                #{item.deletedAt, jdbcType=TIMESTAMP}
            </trim>
        </foreach>
        on conflict(working_face_id) do update set
            working_face_type = excluded.working_face_type,
            working_face_name = excluded.working_face_name,
            work_mode = excluded.work_mode,
            work_mode_changed = case when excluded.work_mode = working_face_progress_definition.work_mode then '0' else '1' end,
            work_mode_changed_at = case when excluded.work_mode = working_face_progress_definition.work_mode then working_face_progress_definition.work_mode_changed_at else now() end,
            work_state = excluded.work_state,
            memo = excluded.memo,
            data_time = excluded.data_time,
            collect_time = excluded.collect_time,
            updated_at = case when excluded.updated_at is not null then excluded.updated_at else now() end,
            deleted_at = case when excluded.deleted_at is not null then excluded.deleted_at else working_face_progress_definition.deleted_at end
    </insert>

</mapper>
