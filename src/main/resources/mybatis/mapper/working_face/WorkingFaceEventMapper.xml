<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkFaceEventMapper">
    <select id="selectWorkFaceEventPage" resultType="com.bdtd.modules.working_face.entity.WorkingFaceEvent">
        SELECT *
        FROM working_face_event
        <where>
            <if test="workFaceId != null and workFaceId != ''">
                AND (work_face_id = #{workFaceId} AND work_face_type = #{workFaceType})
            </if>
            <if test="evenType != null and evenType != ''">
                AND even_type = #{evenType}
            </if>
            <if test="recordType != null and recordType != ''">
                AND record_type = #{recordType}
            </if>
            <if test="startTime != null and startTime != ''">
                AND (start_time &gt;= #{startTime} AND start_time &lt;= #{endTime})
            </if>
        </where>
    </select>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO working_face_event (
            id, work_face_id, work_face_type, record_type, event_type, value, description, begin_time, end_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            COALESCE(#{item.id}, nextval('working_face_event_id_seq')),
            #{item.workFaceId},
            #{item.workFaceType},
            #{item.recordType},
            #{item.eventType},
            #{item.value},
            #{item.description},
            #{item.beginTime},
            #{item.endTime}
        )
        </foreach>
        ON CONFLICT(work_face_id, work_face_type, event_type, begin_time) DO UPDATE
        SET
            work_face_id = EXCLUDED.work_face_id,
            work_face_type = EXCLUDED.work_face_type,
            record_type = EXCLUDED.record_type,
            event_type = EXCLUDED.event_type,
            value = EXCLUDED.value,
            description = EXCLUDED.description,
            begin_time = EXCLUDED.begin_time,
            end_time = EXCLUDED.end_time;
    </insert>
</mapper>
