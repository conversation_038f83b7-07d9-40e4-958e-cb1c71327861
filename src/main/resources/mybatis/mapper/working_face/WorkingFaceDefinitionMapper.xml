<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceDefinitionMapper">

  <select id="selectLastUpdateTime" resultType="java.sql.Timestamp">
    select max(updated_at) as last_update_time
    from working_face_definition
  </select>

  <insert id="upsertBatchWfDef">
    INSERT INTO working_face_definition (
      id,
      face_type,
      work_face_id,
      work_face_name,
      face_status,
      created_at,
      updated_at
    )
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      <trim prefix=" (" suffix=")" suffixOverrides=",">
        #{item.id, jdbcType=VARCHAR},
        #{item.faceType, jdbcType=VARCHAR},
        #{item.workFaceId, jdbcType=VARCHAR},
        #{item.workFaceName, jdbcType=VARCHAR},
        #{item.faceStatus, jdbcType=VARCHAR},
        now(),
        now()
      </trim>
    </foreach>
    on conflict(face_type, work_face_id) do update set
      id = excluded.id,
      work_face_name = excluded.work_face_name,
      face_status = excluded.face_status,
      updated_at = case
        when working_face_definition.work_face_name != excluded.work_face_name or working_face_definition.face_status != excluded.face_status then now()
        else working_face_definition.updated_at
      end
  </insert>

  <!-- region 根据更新时间更新工作面的状态等信息 -->

  <update id="updateModelSystemByUpdatedWf">
    update model_system ms
    set
      "name" = case when ms.id like '990%' then wfd.work_face_name else concat(wfd.work_face_name, '定位设备监测') end,
      updated_at = now()
    from working_face_definition wfd
    where ms.source_system_code = wfd.work_face_id
      and ms.category = wfd.face_type
      <if test="startTime != null">
        and wfd.updated_at <![CDATA[ > ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and wfd.updated_at <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
  </update>

  <update id="updateModelSystemAliveByUpdatedWf">
    update model_system_alive msa
    set
      system_name = case when ms.id like '990%' then wfd.work_face_name else concat(wfd.work_face_name, '定位设备监测') end,
      updated_user = '工作面基本信息变更通知处理',
      updated_at = now()
    from model_system ms
        join working_face_definition wfd on ms.source_system_code = wfd.work_face_id and ms.category = wfd.face_type
    where msa.system_id = ms.id
      <if test="startTime != null">
        and wfd.updated_at <![CDATA[ > ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and wfd.updated_at <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
  </update>

  <update id="updateModelSystemAliveStatisticsByUpdatedWf">
    update model_system_alive_statistics msas
    set
      recovery_time = wfd.updated_at,
      memo = '工作面基本信息变更通知处理, 结束禁用工作面的断线记录',
      recovery_check_time = now()
    from model_system ms
        join working_face_definition wfd on ms.source_system_code = wfd.work_face_id and ms.category = wfd.face_type
    where msas.system_id = ms.id
      <if test="startTime != null">
        and wfd.updated_at <![CDATA[ > ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and wfd.updated_at <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
      and wfd.face_status IN ('已暂停', '停掘', '暂停', '停采')
      and msas.recovery_time IS NULL
  </update>

  <update id="updateDataSystemMonitorByUpdatedWf">
    update data_system_monitor dsm
    set
        system_name = case when ms.id like '990%' then wfd.work_face_name else concat(wfd.work_face_name, '定位设备监测') end,
        status = case when wfd.face_status IN ('已暂停', '停掘', '暂停', '停采') then 9 else dsm.status end
    from model_system ms
        join working_face_definition wfd on ms.source_system_code = wfd.work_face_id and ms.category = wfd.face_type
    where dsm.system_code = ms.id
      <if test="startTime != null">
        and wfd.updated_at <![CDATA[ > ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and wfd.updated_at <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
  </update>

  <update id="updateWorkingFaceProgressDefinitionByUpdatedWf">
    update working_face_progress_definition wfpd
    set
      system_name = wfd.work_face_name,
      working_face_name = wfd.work_face_name,
      memo = '工作面基本信息变更通知处理',
      updated_at = now()
    from working_face_definition wfd
    where wfpd.working_face_id = wfd.work_face_id
      and wfpd.working_face_type = wfd.face_type
      <if test="startTime != null">
        and wfd.updated_at <![CDATA[ > ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and wfd.updated_at <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
  </update>

  <!-- endregion 根据更新时间更新工作面的状态等信息 -->

  <!-- region 根据指定工作面更新工作面状态等 -->

  <update id="updateDataSystemMonitorStatus">
    update data_system_monitor dsm
    set status = 9
    <where>
      (status = 0 or status = 1)
      <if test="mineCode != null and mineCode != ''">
        and mine_code = #{mineCode, jdbcType=VARCHAR}
      </if>
      <if test="set != null and set.size > 0">
        and system_code in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('990', #{item, jdbcType=VARCHAR}::text)
        </foreach>
        and system_code in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('991', #{item, jdbcType=VARCHAR}::text)
        </foreach>
      </if>
    </where>
  </update>

  <update id="updateModelSystemAliveStatistics">
    update model_system_alive_statistics msas
    set
      recovery_time = now(),
      disconnection_duration = EXTRACT(EPOCH FROM (now() - disconnection_time)),
      memo = '全量同步工作面信息，统一结束禁用工作面的断线记录',
      recovery_check_time = now()
    <where>
      recovery_time IS NULL
      <if test="mineCode != null and mineCode != ''">
        and mine_id = #{mineCode, jdbcType=VARCHAR}
      </if>
      <if test="set != null and set.size > 0">
        and system_id in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('990', #{item, jdbcType=VARCHAR}::text)
        </foreach>
        and system_id in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('991', #{item, jdbcType=VARCHAR}::text)
        </foreach>
      </if>
    </where>
  </update>

  <!-- endregion 根据指定工作面更新工作面状态等 -->

  <!-- region 根据启用的工作面更新工作面状态等 -->

  <update id="updateDataSystemMonitorStatusWithEnabledWfd">
    update data_system_monitor dsm
    set status = 9
    <where>
      (status = 0 or status = 1)
      <if test="mineCode != null and mineCode != ''">
        and mine_code = #{mineCode, jdbcType=VARCHAR}
      </if>
      <if test="set != null and set.size > 0">
        and system_code not in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('990', #{item, jdbcType=VARCHAR}::text)
        </foreach>
        and system_code not in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('991', #{item, jdbcType=VARCHAR}::text)
        </foreach>
      </if>
    </where>
  </update>

  <update id="updateModelSystemAliveStatisticsWithEnabledWfd">
    update model_system_alive_statistics msas
    set
        recovery_time = now(),
        disconnection_duration = EXTRACT(EPOCH FROM (now() - disconnection_time)),
        memo = '全量同步工作面信息，统一结束禁用工作面的断线记录',
        recovery_check_time = now()
    <where>
      recovery_time IS NULL
      <if test="mineCode != null and mineCode != ''">
        and mine_id = #{mineCode, jdbcType=VARCHAR}
      </if>
      <if test="set != null and set.size > 0">
        and system_id not in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('990', #{item, jdbcType=VARCHAR}::text)
        </foreach>
        and system_id not in
        <foreach item="item" collection="set" open="(" separator="," close=")">
          concat('991', #{item, jdbcType=VARCHAR}::text)
        </foreach>
      </if>
    </where>
  </update>

  <!-- endregion 根据启用的工作面更新工作面状态等 -->

</mapper>
