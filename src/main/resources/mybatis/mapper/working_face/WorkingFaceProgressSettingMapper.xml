<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceProgressSettingMapper">
    <resultMap id="DepartmentResultMap" type="com.bdtd.modules.working_face.dto.DepartmentDto">
        <id column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_name" jdbcType="INTEGER" property="deptName"/>
    </resultMap>

    <select id="selectCompanyList" resultMap="DepartmentResultMap">
        select *
        from (
            select
                distinct
                company_code as dept_code,
                FIRST_VALUE(company_name) over (partition by company_code order by company_code ASC) as dept_name
            from working_face_progress_settings
            group by company_code, company_name ) m
        order by dept_code asc
    </select>

    <select id="selectMineList" resultMap="DepartmentResultMap">
        select *
        from (
            select
                distinct
                mine_code as dept_code,
                FIRST_VALUE(mine_name) over (partition by mine_code order by mine_code ASC) as dept_name
            from working_face_progress_settings
                <if test="companyCode != null and companyCode != ''">
                    where company_code = #{companyCode}
                </if>
            group by mine_code, mine_name ) m
        order by dept_code asc
    </select>

    <!--
    WITH summary AS (
        SELECT p.id,
        p.customer,
        p.total,
        ROW_NUMBER() OVER(PARTITION BY p.customer ORDER BY p.total DESC) AS ranks
        FROM PURCHASES p
    )
    SELECT s.*
    FROM summary s
    WHERE s.ranks = 1 -->
    <select id="selectLatestSetting" resultType="com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting">
        SELECT DISTINCT ON (mine_code)
            id, group_code, company_code, mine_code, settings, update_time, created_at, updated_at
        FROM working_face_progress_settings
        <where>
            <if test="mineCode != null and mineCode != ''">
                and mine_code = #{mineCode}
            </if>
        </where>
    </select>

    <select id="selectWithMineCodeAndUpdateTime" resultType="com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting">
        SELECT DISTINCT ON (mine_code, update_time)
            id, group_code, company_code, mine_code, settings, update_time, created_at, updated_at
        FROM working_face_progress_settings
        <where>
            <if test="mineCode != null and mineCode != ''">
                and mine_code = #{mineCode}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        ORDER BY mine_code, update_time desc
    </select>

    <select id="selectLatestUpdateTime" resultType="java.lang.Long">
        select extract (EPOCH from max(update_time)) * 1000 as update_time
        from working_face_progress_settings
    </select>


</mapper>
