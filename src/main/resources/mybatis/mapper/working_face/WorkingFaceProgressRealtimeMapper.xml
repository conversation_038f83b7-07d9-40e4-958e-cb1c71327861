<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceProgressRealtimeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="workingFaceProgressResultMap"
               type="com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime">
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="working_face_id" property="workingFaceId"/> <!-- 工作面ID -->
        <result column="station_code" property="stationCode"/> <!-- 基站编码 -->
        <result column="machine_pos" property="machinePos" typeHandler="com.bdtd.util.sql.GeometryTypeHandler"/> <!-- 掘进机头位置 -->
        <result column="trans_pos" property="transPos" typeHandler="com.bdtd.util.sql.GeometryTypeHandler"/> <!-- 运输巷位置 -->
        <result column="air_pos" property="airPos" typeHandler="com.bdtd.util.sql.GeometryTypeHandler"/> <!-- 回风巷位置 -->
        <result column="average_speed" property="averageSpeed"/> <!-- 进尺平均速度 -->
        <result column="sequence_number" property="sequenceNumber"/> <!-- 自增序号 -->
        <result column="footage" property="footage"/> <!-- 进尺数值（包含补偿值） -->
        <result column="alarm_distance" property="alarmDistance"/> <!-- 预警报警距离, 单位米 -->
        <result column="compensation" property="compensation"/> <!-- 补偿值, 单位厘米 -->
        <result column="state" property="state"/> <!-- 采集值状态 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="workingFaceProgressResultMap">
        select *
        from working_face_progress_realtime
    </select>

    <select id="getLatestRealtimeList" resultType="com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime">
        with temp_a as (
            select
                *,
                row_number() over(partition by working_face_id order by data_time desc) as rank
            from working_face_progress_realtime
            ${ew.customSqlSegment}
        )
        select *
        from temp_a
        where rank = 1
    </select>

    <select id="getAllLatestDataTime" resultType="java.sql.Timestamp">
        select max(data_time) as data_time
        from working_face_progress_realtime
        ${ew.customSqlSegment}
    </select>

</mapper>
