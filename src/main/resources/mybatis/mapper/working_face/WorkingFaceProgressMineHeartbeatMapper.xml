<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.working_face.dao.WorkingFaceProgressMineHeartbeatMapper">

    <insert id="upsertBatch">
        INSERT INTO working_face_progress_mine_heartbeat (
            mine_code,
            hb_time,
            update_time,
            time_limit,
            offline,
            service_state_monitor_time,
            service_state,
            connect_state_monitor_time,
            connect_state,
            created_at,
            updated_at
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.mineCode},
                #{item.hbTime},
                #{item.updateTime},
                #{item.timeLimit},
                #{item.offline},
                #{item.serviceStateMonitorTime},
                #{item.serviceState},
                #{item.connectStateMonitorTime},
                #{item.connectState},
                now(),
                now()
            </trim>
        </foreach>
        ON conflict(mine_code) do update set
            hb_time = excluded.hb_time,
            update_time = excluded.update_time,
            service_state_monitor_time = excluded.service_state_monitor_time,
            service_state = excluded.service_state,
            connect_state_monitor_time = excluded.connect_state_monitor_time,
            connect_state = excluded.connect_state,
            updated_at = now()
    </insert>

    <update id="saveCheckResult">
        update working_face_progress_mine_heartbeat
        set offline = data_table.offline,
            check_desc = data_table.check_desc,
            service_state_monitor_time = data_table.service_state_monitor_time,
            connect_state_monitor_time = data_table.connect_state_monitor_time,
            updated_at = data_table.updated_at
        from (
            select
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">'${item.mineCode}',</trim>
                </foreach>]) as mine_code,
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">
                        <if test="item.offline == null">null,</if>
                        <if test="item.offline != null">'${item.offline}',</if>
                    </trim>
                </foreach>]) as offline,
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">
                        <if test='item.checkDesc == null or item.checkDesc == ""'>null,</if>
                        <if test="item.checkDesc != null">'${item.checkDesc}',</if>
                    </trim>
                </foreach>]) as check_desc,
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">
                        <if test="item.serviceStateMonitorTime == null">null::timestamp,</if>
                        <if test="item.serviceStateMonitorTime != null">'${item.serviceStateMonitorTime}'::timestamp,</if>
                    </trim>
                </foreach>]) as service_state_monitor_time,
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">
                        <if test="item.connectStateMonitorTime == null">null::timestamp,</if>
                        <if test="item.connectStateMonitorTime != null">'${item.connectStateMonitorTime}'::timestamp,</if>
                    </trim>
                </foreach>]) as connect_state_monitor_time,
                unnest(array[<foreach collection="list" item="item" index="index" separator=",">
                    <trim suffixOverrides=",">
                        <if test="item.updatedAt == null">null::timestamp,</if>
                        <if test="item.updatedAt != null">'${item.updatedAt}'::timestamp,</if>
                    </trim>
                </foreach>]) as updated_at
        ) as data_table
        where working_face_progress_mine_heartbeat.mine_code = data_table.mine_code;
    </update>

    <select id="getLatestSyncTime" resultType="java.sql.Timestamp">
        select max(update_time) as update_time
        from working_face_progress_mine_heartbeat
        ${ew.customSqlSegment}
    </select>
</mapper>
