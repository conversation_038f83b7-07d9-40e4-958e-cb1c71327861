<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.AnemometerDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="anemometerDefinitionResultMap" type="com.bdtd.modules.ventilation.entity.AnemometerDefinition">
        <result column="id" property="id"/> <!-- ID -->
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="device_code" property="deviceCode"/> <!-- 装置编码 -->
        <result column="device_name" property="deviceName"/> <!-- 装置名称 -->
        <result column="install_pos" property="installPos"/> <!-- 安装位置 -->
        <result column="wind_speed_unit" property="windSpeedUnit"/> <!-- 风速单位：m/s(-99.99时，异常) -->
        <result column="temperature_unit" property="temperatureUnit"/> <!-- 温度单位：℃(大于80°时，异常) -->
        <result column="transverse_area_unit" property="transverseAreaUnit"/> <!-- 横截面积单位：平方米 -->
        <result column="wind_amount_unit" property="windAmountUnit"/> <!-- 风量单位：立方米/分钟 -->
        <result column="humidity_unit" property="humidityUnit"/> <!-- 湿度单位：% -->
        <result column="barometric_unit" property="barometricUnit"/> <!-- 气压单位：Pa -->
        <result column="memo" property="memo"/> <!-- 设备描述 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
        <result column="deleted_at" property="deletedAt"/> <!-- 删除时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="anemometerDefinitionResultMap">
        select * from anemometer_definition
    </select>

    <select id="selectListWithValues" resultType="java.util.Map">
        select
            ad.system_code,
            ad.system_name,
            ad.device_code,
            ad.device_name,
            ad.install_pos,
            ad.wind_speed_unit,
            ad.temperature_unit,
            ad.transverse_area_unit,
            ad.wind_amount_unit,
            ad.humidity_unit,
            ad.barometric_unit,
            ad.wind_direction_unit,
            ar.wind_speed,
            ar.temperature,
            ar.transverse_area,
            ar.wind_amount,
            ar.humidity,
            ar.barometric,
            ar.wind_direction,
            ar.online_status,
            ar.online_status_text,
            ar.data_time,
            ar.collect_time,
            ar.collect_status,
            ar.updated_at
        from anemometer_definition ad
            join anemometer_realtime ar on ad.device_code = ar.device_code
        ${ew.customSqlSegment}
    </select>

    <select id="selectPageWithValues" resultType="java.util.Map">
        select
            ad.system_code,
            ad.system_name,
            ad.device_code,
            ad.device_name,
            ad.install_pos,
            ad.wind_speed_unit,
            ad.temperature_unit,
            ad.transverse_area_unit,
            ad.wind_amount_unit,
            ad.humidity_unit,
            ad.barometric_unit,
            ad.wind_direction_unit,
            ar.wind_speed,
            ar.temperature,
            ar.transverse_area,
            ar.wind_amount,
            ar.humidity,
            ar.barometric,
            ar.wind_direction,
            ar.online_status,
            ar.online_status_text,
            ar.data_time,
            ar.collect_time,
            ar.collect_status,
            ar.updated_at
        from anemometer_definition ad
            join anemometer_realtime ar on ad.device_code = ar.device_code
        ${ew.customSqlSegment}
    </select>

</mapper>
