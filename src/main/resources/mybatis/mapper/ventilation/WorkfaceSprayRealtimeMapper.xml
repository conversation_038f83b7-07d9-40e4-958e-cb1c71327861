<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.WorkfaceSprayRealtimeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="workfaceSprayRealtimeResultMap" type="com.bdtd.modules.ventilation.entity.WorkfaceSprayRealtime">
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="point_id" property="pointId"/> <!-- 设备编码 -->
        <result column="work_mode" property="workMode"/> <!-- 工作模式 0000:自动, 0001:闭锁, 0002:手动 -->
        <result column="work_mode_text" property="workModeText"/> <!-- 工作模式描述 -->
        <result column="connect_mode" property="connectMode"/> <!-- 通讯模式, 0000:RS485通讯, 0001:WIFI通讯, 0002:以太网通讯 -->
        <result column="connect_mode_text" property="connectModeText"/> <!-- 通讯模式描述 -->
        <result column="connect_status" property="connectStatus"/> <!-- 设备连接状态, 0000:未连接, 0001:已连接 -->
        <result column="connect_status_text" property="connectStatusText"/> <!-- 设备连接状态描述 -->
        <result column="water_status" property="waterStatus"/> <!-- 喷雾状态, FF00:开启, 0000:关闭 -->
        <result column="water_status_text" property="waterStatusText"/> <!-- 喷雾状态描述 -->
        <result column="air_status" property="airStatus"/> <!-- 排污状态, FF00:开启, 0000:关闭 -->
        <result column="air_status_text" property="airStatusText"/> <!-- 排污状态描述 -->
        <result column="temperature" property="temperature"/> <!-- 温度值 -->
        <result column="humidity" property="humidity"/> <!-- 湿度值 -->
        <result column="dust_concentration" property="dustConcentration"/> <!-- 粉尘浓度 -->
        <result column="water_pressure" property="waterPressure"/> <!-- 水压值 -->
        <result column="air_pressure" property="airPressure"/> <!-- 气压值 -->
        <result column="holder_count" property="holderCount"/> <!-- 支架主机数量 -->
        <result column="holder_status" property="holderStatus"/> <!-- 支架状态-二进制, 0:关闭, 1:开启 -->
        <result column="holder_delayed" property="holderDelayed"/> <!-- 支架延时 -->
        <result column="holder_link_count" property="holderLinkCount"/> <!-- 支架联动数量 -->
        <result column="holder_location" property="holderLocation"/> <!-- 主机所在支架的位置 -->
        <result column="app_scene" property="appScene"/> <!-- 应用场景, 0000:巷道喷雾, 0001:综采喷雾, 0002:放炮喷雾, 0003:风水联动; 0004:水汽分配 -->
        <result column="app_scene_text" property="appSceneText"/> <!-- 应用场景描述 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="workfaceSprayRealtimeResultMap">
        select * from workface_spray_realtime
    </select>

</mapper>
