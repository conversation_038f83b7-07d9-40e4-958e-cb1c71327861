<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.ChargingCabinetRealtimeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="chargingCabinetRealtimeResultMap" type="com.bdtd.modules.ventilation.entity.ChargingCabinetRealtime">
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="point_id" property="pointId"/> <!-- 充电柜称号.柜门号 -->
        <result column="stand_code" property="standCode"/> <!-- 充电柜称号 -->
        <result column="door_code" property="doorCode"/> <!-- 柜门号 -->
        <result column="person_card" property="personCard"/> <!-- 员工号 -->
        <result column="door_state" property="doorState"/> <!-- 门的状态 0：门关闭，1：门打开，2：通信故障 -->
        <result column="door_state_text" property="doorStateText"/> <!-- 门的状态描述 -->
        <result column="lamp_state" property="lampState"/> <!-- 灯的状态 0：下架，1：充电中，2：充满，3：充电故障 -->
        <result column="lamp_state_text" property="lampStateText"/> <!-- 灯的状态描述 -->
        <result column="lamp_on_time" property="lampOnTime"/> <!-- 矿灯上架时间 -->
        <result column="lamp_off_time" property="lampOffTime"/> <!-- 矿灯下架时间 -->
        <result column="lamp_change_time" property="lampChangeTime"/> <!-- 状态变化时间   0、1、2、3状态变化的时间 -->
        <result column="self_rescuer_state" property="selfRescuerState"/> <!-- 自救器状态  0：离架，1：在架 -->
        <result column="self_rescuer_state_text" property="selfRescuerStateText"/> <!-- 自救器状态描述 -->
        <result column="charge_count" property="chargeCount"/> <!-- 充电次数 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="chargingCabinetRealtimeResultMap">
        select * from charging_cabinet_realtime
    </select>

</mapper>