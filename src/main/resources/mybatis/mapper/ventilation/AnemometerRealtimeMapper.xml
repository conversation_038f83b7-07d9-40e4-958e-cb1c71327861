<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.AnemometerRealtimeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="anemometerRealtimeResultMap" type="com.bdtd.modules.ventilation.entity.AnemometerRealtime">
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="device_code" property="deviceCode"/> <!-- 装置编码 -->
        <result column="wind_speed" property="windSpeed"/> <!-- 风速 -->
        <result column="temperature" property="temperature"/> <!-- 温度 -->
        <result column="transverse_area" property="transverseArea"/> <!-- 横截面积 -->
        <result column="wind_amount" property="windAmount"/> <!-- 风量 -->
        <result column="humidity" property="humidity"/> <!-- 湿度 -->
        <result column="barometric" property="barometric"/> <!-- 气压 -->
        <result column="online_status" property="onlineStatus"/> <!-- 在线状态 -->
        <result column="online_status_text" property="onlineStatusText"/> <!-- 在线状态描述 -->
        <result column="memo" property="memo"/> <!-- 设备描述 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="anemometerRealtimeResultMap">
        select * from anemometer_realtime
    </select>

</mapper>
