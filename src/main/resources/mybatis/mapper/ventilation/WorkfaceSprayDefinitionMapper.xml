<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.WorkfaceSprayDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="workfaceSprayDefinitionResultMap" type="com.bdtd.modules.ventilation.entity.WorkfaceSprayDefinition">
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="point_id" property="pointId"/> <!-- 设备编码 -->
        <result column="device_id" property="deviceId"/> <!-- 设备ID -->
        <result column="device_name" property="deviceName"/> <!-- 设备名称 -->
        <result column="device_ip" property="deviceIp"/> <!-- 设备IP -->
        <result column="data_type" property="dataType"/> <!-- 数据类型, int float -->
        <result column="memo" property="memo"/> <!-- 设备描述 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
        <result column="deleted_at" property="deletedAt"/> <!-- 删除时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="workfaceSprayDefinitionResultMap">
        select * from workface_spray_definition
    </select>

    <select id="selectListWithValues" resultType="java.util.Map">
        select
            wsd.system_code,
            wsd.system_name,
            wsd.point_id,
            wsd.device_id,
            wsd.device_name,
            wsd.device_ip,
            wsd.data_type,
            wsr.work_mode,
            wsr.work_mode_text,
            wsr.connect_mode,
            wsr.connect_mode_text,
            wsr.connect_status,
            wsr.connect_status_text,
            wsr.water_status,
            wsr.water_status_text,
            wsr.air_status,
            wsr.air_status_text,
            wsr.temperature,
            wsr.humidity,
            wsr.dust_concentration,
            wsr.water_pressure,
            wsr.air_pressure,
            wsr.holder_count,
            wsr.holder_status,
            wsr.holder_delayed,
            wsr.holder_link_count,
            wsr.holder_location,
            wsr.app_scene,
            wsr.app_scene_text,
            wsr.data_time,
            wsr.collect_time,
            wsr.collect_status,
            wsr.updated_at
        from
            workface_spray_definition wsd
                join workface_spray_realtime wsr on wsd.point_id = wsr.point_id
            ${ew.customSqlSegment}
    </select>

    <select id="selectPageWithValues" resultType="java.util.Map">
        select
            wsd.system_code,
            wsd.system_name,
            wsd.point_id,
            wsd.device_id,
            wsd.device_name,
            wsd.device_ip,
            wsd.data_type,
            wsr.work_mode,
            wsr.work_mode_text,
            wsr.connect_mode,
            wsr.connect_mode_text,
            wsr.connect_status,
            wsr.connect_status_text,
            wsr.water_status,
            wsr.water_status_text,
            wsr.air_status,
            wsr.air_status_text,
            wsr.temperature,
            wsr.humidity,
            wsr.dust_concentration,
            wsr.water_pressure,
            wsr.air_pressure,
            wsr.holder_count,
            wsr.holder_status,
            wsr.holder_delayed,
            wsr.holder_link_count,
            wsr.holder_location,
            wsr.app_scene,
            wsr.app_scene_text,
            wsr.data_time,
            wsr.collect_time,
            wsr.collect_status,
            wsr.updated_at
        from
            workface_spray_definition wsd
                join workface_spray_realtime wsr on wsd.point_id = wsr.point_id
            ${ew.customSqlSegment}
    </select>

</mapper>
