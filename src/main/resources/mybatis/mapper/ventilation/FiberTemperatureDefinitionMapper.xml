<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.FiberTemperatureDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fiberTemperatureDefinitionResultMap" type="com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition">
        <result column="mine_code" property="mineCode"/> <!-- 矿编码,矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称,矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="section_code" property="sectionCode"/> <!-- 分区编码 -->
        <result column="section_name" property="sectionName"/> <!-- 分区名称 -->
        <result column="fiber_start" property="fiberStart"/> <!-- 光缆起点 -->
        <result column="fiber_end" property="fiberEnd"/> <!-- 光缆终点 -->
        <result column="fiber_length" property="fiberLength"/> <!-- 光缆长度 -->
        <result column="change_length" property="changeLength"/> <!-- 通道长度变化，配合channelUpflag使用 -->
        <result column="orange_warn_limit" property="orangeWarnLimit"/> <!-- 橙色警报线 -->
        <result column="red_warn_limit" property="redWarnLimit"/> <!-- 红色警报线 -->
        <result column="substation_code" property="substationCode"/> <!-- 分站编码 -->
        <result column="substation_name" property="substationName"/> <!-- 分站名称 -->
        <result column="substation_ip" property="substationIp"/> <!-- 分站ip地址 -->
        <result column="substation_location" property="substationLocation"/> <!-- 分站安装位置 -->
        <result column="substation_state" property="substationState"/> <!-- 分站连接状态，0正常，-1断开 -->
        <result column="channel_code" property="channelCode"/> <!-- 通道编码 -->
        <result column="channel_name" property="channelName"/> <!-- 通道名称 -->
        <result column="channel_upflag" property="channelUpflag"/> <!-- 通道更新状态；0 长度正常，-1长度减少，1长度增加 -->
        <result column="data_time" property="dataTime"/> <!-- 数据变化时间 -->
        <result column="collect_time" property="collectTime"/> <!-- 数据采集时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="fiberTemperatureDefinitionResultMap">
        select * from fiber_temperature_definition
    </select>

    <select id="selectListWithValues" resultType="java.util.Map">
        select
            fd.section_code,
            fd.section_name,
            fd.fiber_start,
            fd.fiber_end,
            fd.fiber_length,
            fd.change_length,
            fd.channel_upflag,
            fd.orange_warn_limit,
            fd.red_warn_limit,
            fd.substation_code,
            fd.substation_name,
            fd.substation_state,
            fd.channel_code,
            fd.channel_name,
            fr.temp_str,
            fr.temp_max,
            fr.temp_max_position,
            fr.temp_min,
            fr.temp_min_position,
            fr.temp_avg,
            fr.alarm_flag,
            fr.alarm_flag_text,
            fr.data_time,
            fr.collect_time
        from fiber_temperature_definition fd
                 join fiber_temperature_realtime fr on fd.section_code = fr.section_code
            ${ew.customSqlSegment}
    </select>

    <select id="selectPageWithValues" resultType="java.util.Map">
        select
            fd.section_code,
            fd.section_name,
            fd.fiber_start,
            fd.fiber_end,
            fd.fiber_length,
            fd.change_length,
            fd.channel_upflag,
            fd.orange_warn_limit,
            fd.red_warn_limit,
            fd.substation_code,
            fd.substation_name,
            fd.substation_state,
            fd.channel_code,
            fd.channel_name,
            fr.temp_str,
            fr.temp_max,
            fr.temp_max_position,
            fr.temp_min,
            fr.temp_min_position,
            fr.temp_avg,
            fr.alarm_flag,
            fr.alarm_flag_text,
            fr.data_time,
            fr.collect_time
        from fiber_temperature_definition fd
                 join fiber_temperature_realtime fr on fd.section_code = fr.section_code
            ${ew.customSqlSegment}
    </select>

</mapper>
