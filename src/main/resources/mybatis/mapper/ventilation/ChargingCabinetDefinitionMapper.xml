<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.ventilation.dao.ChargingCabinetDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="chargingCabinetDefinitionResultMap" type="com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition">
        <result column="id" property="id"/> <!-- ID -->
        <result column="mine_code" property="mineCode"/> <!-- 矿编码 -->
        <result column="mine_name" property="mineName"/> <!-- 矿名称 -->
        <result column="group_code" property="groupCode"/> <!-- 集团编码 -->
        <result column="group_name" property="groupName"/> <!-- 集团名称 -->
        <result column="system_code" property="systemCode"/> <!-- 系统编码 -->
        <result column="system_name" property="systemName"/> <!-- 系统名称 -->
        <result column="point_id" property="pointId"/> <!-- 充电柜称号.柜门号 -->
        <result column="stand_code" property="standCode"/> <!-- 智能充电柜系统号 -->
        <result column="door_code" property="doorCode"/> <!-- 柜门号 -->
        <result column="person_card" property="personCard"/> <!-- 员工号 -->
        <result column="key_card_code" property="keyCardCode"/> <!-- 钥匙卡号 -->
        <result column="lamp_equip_time" property="lampEquipTime"/> <!-- 矿灯配备时间 -->
        <result column="lamp_charge_times" property="lampChargeTimes"/> <!-- 矿灯充电次数 -->
        <result column="locate_card" property="locateCard"/> <!-- 定位号 -->
        <result column="name" property="name"/> <!-- 名字 -->
        <result column="department" property="department"/> <!-- 部门 -->
        <result column="work_kind" property="workKind"/> <!-- 工种 -->
        <result column="id_card" property="idCard"/> <!-- 身份证号 -->
        <result column="profile_photo" property="profilePhoto"/> <!-- 个人照片 -->
        <result column="bg_photo" property="bgPhoto"/> <!-- 背景照片 -->
        <result column="memo" property="memo"/> <!-- 设备描述 -->
        <result column="data_time" property="dataTime"/> <!-- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_time" property="collectTime"/> <!-- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss -->
        <result column="collect_status" property="collectStatus"/> <!-- 采集状态, 100正常, 90自动结束 -->
        <result column="created_at" property="createdAt"/> <!-- 插入时间 -->
        <result column="updated_at" property="updatedAt"/> <!-- 更新时间 -->
        <result column="deleted_at" property="deletedAt"/> <!-- 删除时间 -->
    </resultMap>

    <select id="selectBySystemId" resultMap="chargingCabinetDefinitionResultMap">
        select * from charging_cabinet_definition
    </select>

    <select id="selectListWithValues" resultType="java.util.Map">
        select
            ccd.system_code,
            ccd.system_name,
            ccd.point_id,
            ccd.stand_code,
            ccd.door_code,
            ccd.person_card,
            ccd.key_card_code,
            ccd.lamp_equip_time,
            ccd.lamp_charge_times,
            ccd.locate_card,
            ccd.name,
            ccd.department,
            ccd.work_kind,
            ccd.id_card,
            ccd.profile_photo,
            ccd.bg_photo,
            ccr.door_state,
            ccr.door_state_text,
            ccr.lamp_state,
            ccr.lamp_state_text,
            ccr.lamp_on_time,
            ccr.lamp_off_time,
            ccr.lamp_change_time,
            ccr.self_rescuer_state,
            ccr.self_rescuer_state_text,
            ccr.charge_count,
            ccr.data_time,
            ccr.collect_time,
            ccr.collect_status,
            ccr.updated_at
        from charging_cabinet_definition ccd join charging_cabinet_realtime ccr
            on ccd.point_id = ccr.point_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectPageWithValues" resultType="java.util.Map">
        select
            ccd.system_code,
            ccd.system_name,
            ccd.point_id,
            ccd.stand_code,
            ccd.door_code,
            ccd.person_card,
            ccd.key_card_code,
            ccd.lamp_equip_time,
            ccd.lamp_charge_times,
            ccd.locate_card,
            ccd.name,
            ccd.department,
            ccd.work_kind,
            ccd.id_card,
            ccd.profile_photo,
            ccd.bg_photo,
            ccr.door_state,
            ccr.door_state_text,
            ccr.lamp_state,
            ccr.lamp_state_text,
            ccr.lamp_on_time,
            ccr.lamp_off_time,
            ccr.lamp_change_time,
            ccr.self_rescuer_state,
            ccr.self_rescuer_state_text,
            ccr.charge_count,
            ccr.data_time,
            ccr.collect_time,
            ccr.collect_status,
            ccr.updated_at
        from charging_cabinet_definition ccd join charging_cabinet_realtime ccr
            on ccd.point_id = ccr.point_id
        ${ew.customSqlSegment}
    </select>

</mapper>