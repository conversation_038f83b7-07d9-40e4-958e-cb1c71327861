<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.DeriveTreeMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.point.entity.DeriveTree">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="source_point_id" jdbcType="VARCHAR" property="sourcePointId"/>
        <result column="source_node_label" jdbcType="VARCHAR" property="sourceNodeLabel"/>
        <result column="derive_point_id" jdbcType="VARCHAR" property="derivePointId"/>
        <result column="derive_node_label" jdbcType="VARCHAR" property="deriveNodeLabel"/>
        <result column="path" typeHandler="com.bdtd.util.sql.LTreeTypeHandler" property="path"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.bdtd.modules.point.entity.DeriveTree">
        <selectKey keyProperty="id" resultType="int" order="BEFORE">
            SELECT nextval('data_derive_tree_id_seq'::regclass) as id
        </selectKey>
        insert into data_derive_tree (id, source_point_id, source_node_label, derive_point_id, derive_node_label, "path")
        values (
            #{id,jdbcType=VARCHAR},
            #{sourcePointId,jdbcType=VARCHAR},
            #{sourceNodeLabel,jdbcType=VARCHAR},
            #{derivePointId,jdbcType=VARCHAR},
            #{deriveNodeLabel,jdbcType=VARCHAR},
            #{path,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.LTreeTypeHandler}
        )
    </insert>

    <select id="selectMatch" resultType="com.bdtd.modules.point.entity.DeriveTree">
        SELECT *
        FROM data_derive_tree
        WHERE "path" ~ #{matchPath}
    </select>

    <delete id="deleteMatch">
        DELETE FROM data_derive_tree
        WHERE "path" ~ #{matchPath}
    </delete>

    <delete id="deleteMatches">
        DELETE FROM data_derive_tree
        <where>
            <foreach item="item" index="index" collection="matchPaths">
                OR "path" ~ #{item}
            </foreach>
        </where>
    </delete>

</mapper>
