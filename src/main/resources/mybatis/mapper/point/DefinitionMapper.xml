<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.DefinitionMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.common.entity.Definition">
        <result column="point_id" jdbcType="VARCHAR" property="pointId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="system_id" jdbcType="VARCHAR" property="systemId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="point_type" jdbcType="INTEGER" property="pointType"/>
    </resultMap>
    <select id="select" parameterType="String" resultType="java.util.Map">
        ${value}
    </select>
    <select id="count" parameterType="String" resultType="Integer">
        ${value}
    </select>
    <insert id="insert" parameterType="String">
        ${value}
    </insert>
    <delete id="delete" parameterType="String">
        ${value}
    </delete>
    <update id="update" parameterType="String">
        ${value}
    </update>

    <!--
    <insert id="testInsert">
       insert into data_alarm ( device_name, area_name,alarm_state,alarm_level,alarm_type,alarm_time)
       values
       <foreach collection="list" item="item" index="index" separator="," >
         (#{item.device_name,jdbcType=VARCHAR}, #{item.area_name,jdbcType=VARCHAR}
         , #{item.alarm_state,jdbcType=INTEGER}, #{item.alarm_level,jdbcType=VARCHAR}
         , #{item.alarm_type,jdbcType=VARCHAR}, #{item.alarm_time,jdbcType=TIMESTAMP})
       </foreach>
       ON conflict(device_name) do  update set
       area_name = excluded.area_name, alarm_state = excluded.alarm_state,
       alarm_level = excluded.alarm_level, alarm_type = excluded.alarm_type,
       alarm_time = excluded.alarm_time
     </insert> -->
</mapper>