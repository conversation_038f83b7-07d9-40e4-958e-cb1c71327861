<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.DefinitionConfMapper">
    <resultMap id="BaseResultMap" type="com.bdtd.modules.point.entity.DefinitionConf">
        <result column="point_id" jdbcType="VARCHAR" property="pointId"/>
        <result column="other_name" jdbcType="VARCHAR" property="otherName"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="reserve" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="reserve"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="group_code" jdbcType="VARCHAR" property="groupCode"/>
        <result column="expression" jdbcType="VARCHAR" property="expression"/>
        <result column="expression_params" typeHandler="com.bdtd.util.sql.ArrayJsonHandler" property="expressionParams"/>
        <result column="translate" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="translate"/>
        <result column="warn" typeHandler="com.bdtd.util.sql.ArrayJsonHandler" property="warn"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unit_id" jdbcType="VARCHAR" property="unitId"/>
        <result column="unit_display_symbol" jdbcType="VARCHAR" property="unitDisplaySymbol"/>
        <result column="unit_short_name" jdbcType="VARCHAR" property="unitShortName"/>
        <result column="point_type" jdbcType="INTEGER" property="pointType"/>
        <result column="node_label" jdbcType="VARCHAR" property="nodeLabel"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        point_id, other_name, short_name,reserve, created_at, updated_at, company_code,
        group_code, expression, translate, unit,point_type,warn, unit_id, unit_display_symbol, unit_short_name,
        expression_params, node_label
    </sql>

    <select id="selectByExample" parameterType="com.bdtd.modules.point.dto.DefinitionConfExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from data_definition_conf
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectOneByExample" parameterType="com.bdtd.modules.point.dto.DefinitionConfExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from data_definition_conf
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>

    <select id="selectMaxNodeLabel" parameterType="com.bdtd.modules.point.dto.DefinitionConfExample" resultType="String">
        select max(node_label)
        from data_definition_conf
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <delete id="deleteByPointId" parameterType="java.lang.String">
        delete
        from data_definition_conf
        where point_id = #{pointId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByExample" parameterType="com.bdtd.modules.point.dto.DefinitionConfExample">
        delete from data_definition_conf
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>

    <insert id="insert" parameterType="com.bdtd.modules.point.entity.DefinitionConf">
        insert into data_definition_conf (
            point_id, other_name,
            reserve, company_code, group_code,
            expression, translate, unit, point_type, warn, short_name,
            unit_id, unit_display_symbol, unit_short_name,
            expression_params, node_label
        )
        values (
            #{pointId,jdbcType=VARCHAR},
            #{otherName,jdbcType=VARCHAR},
            #{reserve,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            #{companyCode,jdbcType=VARCHAR},
            #{groupCode,jdbcType=VARCHAR},
            #{expression,jdbcType=VARCHAR},
            #{translate,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            #{unit,jdbcType=VARCHAR},
            #{pointType,jdbcType=INTEGER},
            #{warn,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            #{shortName,jdbcType=VARCHAR},
            #{unitId,jdbcType=VARCHAR},
            #{unitDisplaySymbol,jdbcType=VARCHAR},
            #{unitShortName,jdbcType=VARCHAR},
            #{expressionParams,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            #{nodeLabel,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateByExampleSelective" parameterType="map">
        update data_definition_conf
        <set>
            <if test="record.pointId != null">
                point_id = #{record.pointId,jdbcType=VARCHAR},
            </if>
            <if test="record.otherName != null">
                other_name = #{record.otherName,jdbcType=VARCHAR},
            </if>
            <if test="record.shortName != null">
                short_name = #{record.shortName,jdbcType=VARCHAR},
            </if>
            <if test="record.reserve != null">
                reserve = #{record.reserve,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            </if>
            <if test="record.companyCode != null">
                company_code = #{record.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="record.groupCode != null">
                group_code = #{record.groupCode,jdbcType=VARCHAR},
            </if>
            <if test="record.expression != null">
                expression = #{record.expression,jdbcType=VARCHAR},
            </if>
            <if test="record.translate != null">
                translate = #{record.translate,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            </if>
            <if test="record.unit != null">
                unit = #{record.unit,jdbcType=VARCHAR},
            </if>
            <if test="record.pointType != null">
                point_type = #{record.pointType,jdbcType=INTEGER},
            </if>
            <if test="record.warn != null">
                warn = #{record.warn,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            </if>
            <if test="record.unitId != null">
                unit_id = #{record.unitId,jdbcType=VARCHAR},
            </if>
            <if test="record.unitDisplaySymbol != null">
                unit_display_symbol = #{record.unitDisplaySymbol,jdbcType=VARCHAR},
            </if>
            <if test="record.unitShortName != null">
                unit_short_name = #{record.unitShortName,jdbcType=VARCHAR},
            </if>
            <if test="record.expressionParams != null">
                expression_params = #{record.expressionParams,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            </if>
            <if test="record.nodeLabel != null">
                node_label = #{record.nodeLabel,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>

    <insert id="upsertById">
        insert into data_definition_conf (
            point_id, other_name, reserve, company_code, group_code,
            expression, translate, unit, point_type, warn, unit_id, unit_display_symbol,
            unit_short_name, expression_params, node_label
        )
        values (
            #{pointId,jdbcType=VARCHAR},
            #{otherName,jdbcType=VARCHAR},
            #{reserve,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ObjectJsonHandler},
            #{companyCode,jdbcType=VARCHAR},
            #{groupCode,jdbcType=VARCHAR},
            #{expression,jdbcType=VARCHAR},
            #{translate,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            #{unit,jdbcType=VARCHAR},
            #{pointType,jdbcType=INTEGER},
            #{warn,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            #{unitId,jdbcType=VARCHAR},
            #{unitDisplaySymbol,jdbcType=VARCHAR},
            #{unitShortName,jdbcType=VARCHAR},
            #{expressionParams,jdbcType=OTHER,typeHandler=com.bdtd.util.sql.ArrayJsonHandler},
            #{nodeLabel,jdbcType=VARCHAR}
        ) ON conflict(point_id) do update set
            point_type = excluded.point_type,
            other_name = excluded.other_name,
            reserve = excluded.reserve,
            company_code = excluded.company_code,
            group_code = excluded.group_code,
            expression = excluded.expression,
            translate = excluded.translate,
            unit = excluded.unit,
            point_type = excluded.point_type,
            warn = excluded.warn,
            unit_id = excluded.unit_id,
            unit_display_symbol = excluded.unit_display_symbol,
            unit_short_name = excluded.unit_short_name,
            expression_params = excluded.expression_params,
            node_label = excluded.node_label
    </insert>

</mapper>