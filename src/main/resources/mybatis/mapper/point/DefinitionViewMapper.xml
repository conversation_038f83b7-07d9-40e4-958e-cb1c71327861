<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.DefinitionViewMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.point.entity.DefinitionView">
    <result column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="point_type" jdbcType="INTEGER" property="pointType" />
    <result column="extra_type" jdbcType="VARCHAR" property="extraType" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="def_unit" jdbcType="VARCHAR" property="defUnit" />
    <result column="translate" jdbcType="VARCHAR" property="translate" />
    <result column="parent_system_id" jdbcType="VARCHAR" property="parentSystemId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    point_id, "name", data_type, system_id, created_at, updated_at, point_type, extra_type, "location", def_unit,
    translate, parent_system_id
  </sql>
  <select id="selectByExample" parameterType="com.bdtd.modules.point.dto.DefinitionViewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from definition_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="insert" parameterType="com.bdtd.modules.point.entity.DefinitionView">
    insert into definition_view (point_id, name, data_type, 
      system_id, created_at, updated_at, 
      point_type, def_unit, translate, 
      parent_system_id)
    values (#{pointId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR}, 
      #{systemId,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{pointType,jdbcType=INTEGER}, #{defUnit,jdbcType=VARCHAR}, #{translate,jdbcType=VARCHAR}, 
      #{parentSystemId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bdtd.modules.point.entity.DefinitionView">
    insert into definition_view
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        point_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="pointType != null">
        point_type,
      </if>
      <if test="defUnit != null">
        def_unit,
      </if>
      <if test="translate != null">
        translate,
      </if>
      <if test="parentSystemId != null">
        parent_system_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="pointType != null">
        #{pointType,jdbcType=INTEGER},
      </if>
      <if test="defUnit != null">
        #{defUnit,jdbcType=VARCHAR},
      </if>
      <if test="translate != null">
        #{translate,jdbcType=VARCHAR},
      </if>
      <if test="parentSystemId != null">
        #{parentSystemId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>