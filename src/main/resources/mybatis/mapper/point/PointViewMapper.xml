<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.PointViewMapper">
    <select id="exeuetSql" parameterType="string" resultType="hashmap">
        ${sql}
    </select>

    <select id="selectByPoints" parameterType="string" resultType="hashmap">
        select * from point_view where system_id =#{systemId} order by point_type
    </select>

    <select id="selectPointsBySystemType" parameterType="string" resultType="hashmap">
        select * from point_view where SUBSTRING(system_id, 0, 4) =#{systemType} order by system_id, point_type, point_id
    </select>

    <select id="selectSensorTypeBySystemId" resultType="java.util.HashMap">
        SELECT DISTINCT system_id, point_type, type
        FROM point_view
        WHERE type is not null
        <if test="systemId != null and systemId != ''">
            and system_id = #{systemId}
        </if>
        ORDER BY point_type
    </select>

    <select id="selectDisasterPointList" resultType="java.util.HashMap">
        select
            pv.point_id,
            pv."name",
            pv."type",
            pv.system_id,
            pv.value,
            pv.unit,
            dwrc.rules,
            dwrc.indicator_id
        from point_view pv
            left join data_warn_rule_conf dwrc on pv.point_id =dwrc.point_id
        where 1=1
        <if test="query.ifConfigure != null and query.ifConfigure == 0 ">
            and ( dwrc.indicator_id != #{query.indicatorId,jdbcType=VARCHAR} or dwrc.rules is null )
        </if>
        <if test="query.ifConfigure != null and query.ifConfigure == 1 ">
            and dwrc.indicator_id = #{query.indicatorId,jdbcType=VARCHAR}
        </if>
        <if test="query.systemId != null and query.systemId != ''">
            and pv.system_id = #{query.systemId}
        </if>
        <if test="query.type != null and query.type != ''">
            and pv.type = #{query.type}
        </if>
        <if test="query.pointName != null and query.pointName != ''">
            and pv.name like '%' || #{query.pointName,jdbcType=VARCHAR}
        </if>
        order by  dwrc.updated_at desc
    </select>

    <select id="getDisasterPointPage" resultType="java.util.HashMap">
        select
            pv.point_id,
            pv."name",
            pv."type",
            pv.system_id,
            pv.value,
            pv.unit,
            dwrc.rules,
            dwrc.indicator_id
        from point_view pv
            left join data_warn_rule_conf dwrc on pv.point_id =dwrc.point_id
        where 1=1
        <if test="query.ifConfigure != null and query.ifConfigure == 0 ">
            and ( dwrc.indicator_id != #{query.indicatorId,jdbcType=VARCHAR} or dwrc.rules is null )
        </if>
        <if test="query.ifConfigure != null and query.ifConfigure == 1 ">
            and dwrc.indicator_id = #{query.indicatorId,jdbcType=VARCHAR}
        </if>
        <if test="query.systemId != null and query.systemId != ''">
            and pv.system_id = #{query.systemId}
        </if>
        <if test="query.type != null and query.type != ''">
            and pv.type = #{query.type}
        </if>
        <if test="query.pointName != null and query.pointName != ''">
            and pv.name like '%' || #{query.pointName,jdbcType=VARCHAR}
        </if>
        order by dwrc.updated_at desc
    </select>

</mapper>
