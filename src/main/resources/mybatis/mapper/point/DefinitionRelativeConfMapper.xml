<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdtd.modules.point.mapper.DefinitionRelativeConfMapper">
  <resultMap id="BaseResultMap" type="com.bdtd.modules.point.entity.DefinitionRelativeConf">
    <result column="point_id" jdbcType="VARCHAR" property="pointId" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="mine_code" jdbcType="VARCHAR" property="mineCode" />
    <result column="parent_system_id" jdbcType="VARCHAR" property="parentSystemId" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="point_type" jdbcType="INTEGER" property="pointType" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="other_name" jdbcType="VARCHAR" property="otherName" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="reserve" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="reserve" />
    <result column="translate" typeHandler="com.bdtd.util.sql.ObjectJsonHandler" property="translate" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="expression" jdbcType="VARCHAR" property="expression" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="attribute_name" jdbcType="VARCHAR" property="attributeName" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="warn" typeHandler="com.bdtd.util.sql.ArrayJsonHandler" property="warn" />
    <result column="def_unit" jdbcType="VARCHAR" property="defUnit" />
    <result column="extra_type" jdbcType="VARCHAR" property="extraType" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="expression_params" typeHandler="com.bdtd.util.sql.ArrayJsonHandler" property="expressionParams" />
    <result column="node_label" jdbcType="VARCHAR" property="nodeLabel"/>
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    point_id, point_type, group_code, mine_code, system_id, system_name, "name", point_type, data_type, type, created_at, updated_at,
    other_name, reserve, translate, unit, expression, attribute_id, device_id, business_id, 
    attribute_name, device_name, business_name, warn, extra_type, "location", def_unit, short_name, parent_system_id,
    expression_params, node_label
  </sql>

  <sql id="Base_Value_Column_List">
    drc.point_id,
    drc.point_type,
    drc.group_code,
    drc.mine_code,
    drc.system_id,
    drc.system_name,
    drc."name",
    drc.point_type,
    drc.data_type,
    drc.type,
    drc.created_at,
    drc.updated_at,
    drc.other_name,
    drc.reserve,
    drc.translate,
    drc.unit,
    drc.expression,
    drc.attribute_id,
    drc.device_id,
    drc.business_id,
    drc.attribute_name,
    drc.device_name,
    drc.business_name,
    drc.warn,
    drc.extra_type,
    drc."location",
    drc.def_unit,
    drc.short_name,
    drc.parent_system_id,
    drc.expression_params,
    drc.node_label,
    pv.value
  </sql>

  <select id="selectByExample" parameterType="com.bdtd.modules.point.dto.DefinitionRelativeConfExample" resultMap="BaseResultMap">
    select
      <if test="distinct">distinct</if>
      <include refid="Base_Column_List" />
    from definition_relative_conf
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="orderByClause != null">
        order by ${orderByClause}
      </if>
  </select>

  <select id="selectWithValuesByExample" parameterType="com.bdtd.modules.point.dto.DefinitionRelativeConfExample" resultMap="BaseResultMap">
    select
      <if test="distinct">distinct</if>
      <include refid="Base_Value_Column_List" />
    from definition_relative_conf drc
        left join point_view pv on drc.point_id = pv.point_id
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="orderByClause != null">
        order by ${orderByClause}
      </if>
  </select>

</mapper>