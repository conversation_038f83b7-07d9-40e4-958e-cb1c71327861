package ${package.Controller};

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${package.Entity}.${entity};
import ${package.Service}.I${entity}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.bdtd.util.StatusEnum;

import java.util.List;

/**
 * ${table.comment} 控制器
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@RestController
@RequestMapping("/${package.ModuleName}/${entity?uncap_first}")
@Api(value = "${entity?uncap_first}", tags = "${table.comment}管理模块")
public class ${entity}Controller
{

    private final I${entity}Service ${entity?uncap_first}Service;

    public ${entity}Controller(I${entity}Service ${entity?uncap_first}Service) {
        this.${entity?uncap_first}Service = ${entity?uncap_first}Service;
    }

    /**
     * 列表查询
     *
     * @param ${entity?uncap_first}Entity ${table.comment}
     * @return Result
     */
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> get${entity}List(${entity} ${entity?uncap_first}Entity) {
        Msg msg;
        try {
            QueryWrapper<${entity}> queryWrap = Wrappers.query(${entity?uncap_first}Entity);
            queryWrap.orderByAsc("id");

            List<${entity}> listResult = ${entity?uncap_first}Service.list(queryWrap);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param ${entity?uncap_first}Entity ${table.comment}
     * @return Result
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> get${entity}Page(Page<${entity}> page, ${entity} ${entity?uncap_first}Entity) {
        Msg msg;
        try {
            Page<${entity}> pageResult = ${entity?uncap_first}Service.page(page, Wrappers.query(${entity?uncap_first}Entity));
            msg = ReturnMsg.success(pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过 ID 查询${table.comment}
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}")
    public ResponseEntity<Msg> getById(@PathVariable("id") Integer id) {
        Msg msg;
        try {
            ${entity} ${entity?uncap_first}Result = ${entity?uncap_first}Service.getById(id);
            msg = ReturnMsg.success(${entity?uncap_first}Result);
        }
        catch (Exception e) {
            log.error("对象查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 新增${table.comment}
     *
     * @param ${entity?uncap_first}Entity ${table.comment}
     * @return Result
     */
    @ApiOperation(value = "新增${table.comment}", notes = "新增${table.comment}")
    @PostMapping
    public ResponseEntity<Msg> save(@RequestBody ${entity} ${entity?uncap_first}Entity) {
        Msg msg;
        try {
            // 是否存在
            ${entity} existed = ${entity?uncap_first}Service.getById(${entity?uncap_first}Entity.pkVal());
            if (existed != null) {
                return new ResponseEntity<>(ReturnMsg.fail("对象已存在"), HttpStatus.OK);
            }

            boolean saveResult = ${entity?uncap_first}Service.save(${entity?uncap_first}Entity);
            msg = ReturnMsg.success(saveResult);
        }
        catch (Exception e) {
            log.error("新增保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 修改${table.comment}
     *
     * @param ${entity?uncap_first}Entity ${table.comment}
     * @return Result
     */
    @ApiOperation(value = "修改${table.comment}", notes = "修改${table.comment}")
    @PutMapping
    public ResponseEntity<Msg> updateById(@RequestBody ${entity} ${entity?uncap_first}Entity) {
        Msg msg;
        try {
            // 是否存在
            ${entity} existed = ${entity?uncap_first}Service.getById(${entity?uncap_first}Entity.pkVal());
            if (existed == null) {
                return new ResponseEntity<>(ReturnMsg.fail("更新对象不存在"), HttpStatus.OK);
            }

            boolean updateResult = ${entity?uncap_first}Service.updateById(${entity?uncap_first}Entity);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过id删除${table.comment}
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id删除${table.comment}", notes = "通过id删除${table.comment}")
    @DeleteMapping("/{id}")
    public ResponseEntity<Msg> removeById(@PathVariable Integer id) {
        Msg msg;
        try {
            boolean delResult = ${entity?uncap_first}Service.removeById(id);
            msg = ReturnMsg.success(delResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
