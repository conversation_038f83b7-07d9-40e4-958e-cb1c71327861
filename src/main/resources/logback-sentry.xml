<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!--
    <include resource="logback-${spring.profiles.active}.xml"/> -->
    <springProperty scope="context" name="application_name" source="spring.application.name" defaultValue="timescale-monitor" />

    <!-- 日志目录 -->
    <property name="LOG_HOME" value="D:/logs/${application_name}"/>
    <springProfile name="dev">
        <property name="LOG_HOME" value="D:/logs/${application_name}"/>
    </springProfile>
    <springProfile name="pro">
        <property name="LOG_HOME" value="/home/<USER>/${application_name}"/>
    </springProfile>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr"
                    converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr([${application_name},%X{traceId}]){yellow} %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan}[line:%line] %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){magenta} %clr(%-5level) %clr(%logger{50}){cyan} %clr(-){faint} %clr([${application_name},%X{traceId}]){yellow} %msg%n"/>

    <appender name="LOG_DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!--
            <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}] [ %-5level] %logger{96} - %msg%n</pattern> -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log-debug_%d{yyyyMMdd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>2</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!--
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter> -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <prudent>false</prudent>
    </appender>

    <appender name="LOG_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!--
            <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}] [ %-5level] %logger{96} - %msg%n</pattern> -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log-info_%d{yyyyMMdd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>2</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!--
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter> -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <prudent>false</prudent>
    </appender>

    <appender name="LOG_WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!--
            <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}] [ %-5level] %logger{96}[line:%line] - %msg%n</pattern> -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log-warn_%d{yyyyMMdd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>5</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!--
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter> -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <prudent>false</prudent>
    </appender>

    <appender name="LOG_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!--
            <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}] [ %-5level] %logger{96}[line:%line] - %msg%n</pattern> -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log-error_%d{yyyyMMdd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>5</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- Sentry
    <appender name="Sentry" class="com.getsentry.raven.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">、
            <level>WARN</level>
        </filter>
    </appender>
    <logger name="logback.SentryAppenderIT" level="INFO">
        <appender-ref ref="Sentry"/>
    </logger> -->

    <!-- Console Output -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <root level="DEBUG">
        <!-- 控制台输出 -->
        <appender-ref ref="STDOUT"/>
        <!-- 文件输出
        <appender-ref ref="LOG_DEBUG"/> -->
        <appender-ref ref="LOG_INFO"/>
        <appender-ref ref="LOG_WARN"/>
        <appender-ref ref="LOG_ERROR"/>
        <!--
        <appender-ref ref="Sentry"/> -->
    </root>
</configuration>