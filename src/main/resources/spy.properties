# http://p6spy.readthedocs.io/en/latest/configandusage.html#common-property-file-settings
# Use p6spy driver as proxy
deregisterdrivers=true

# Real driver
driverlist=org.postgresql.Driver

# Base settings
autoflush=false
dateformat=yyyy-MM-dd HH:mm:ss
reloadproperties=false
reloadpropertiesinterval=60
#modulelist=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.test.P6TestFactory
validationQuery=select 1

# Custom log
#appender=com.ming.base.orm.P6spyDbAndLogAppender
#appender=com.p6spy.engine.spy.appender.Slf4JLogger

# Log format
logMessageFormat=com.bdtd.util.sql.P6SpyLogFormatter
#customLogMessageFormat=%(executionTime)ms | %(sqlSingleLine)

# Database date
databaseDialectDateFormat=yyyy-MM-dd HH:mm:ss
databaseDialectBooleanFormat=boolean

# Filter SQL
filter=true
exclude=
excludecategories=info,debug,result,resultset,commit,rollback

# Slow query
outagedetection=true
outagedetectioninterval=2

# Others
# see: http://about.travis-ci.org/docs/user/database-setup/
#url=*************************************************************************************************************************************************************************
#user=postgres
#password=pg-dev123*
#poolingDataSourceClass=org.postgresql.ds.PGConnectionPoolDataSource
#xaDataSource=org.postgresql.xa.PGXADataSource