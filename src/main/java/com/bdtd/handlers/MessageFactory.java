package com.bdtd.handlers;

import com.bdtd.conf.RabbitTopicConfig;
import lombok.Data;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 消息处理分发
 *
 * <AUTHOR>
 */
@Component
@Data
public class MessageFactory
{

    @Value("${biz.sync-system-monitor-to-mes}")
    private Boolean doSendMonitorInfoToMesService;

    @Autowired
    private MesSystemMonitorHandler mesSystemMonitorHandler;
    @Autowired
    private BdataAlertIndicatorHandler bdataAlertIndicatorHandler;

    public ChannelAwareMessageListener createMessageListener(String name) {
        switch (name) {
            case RabbitTopicConfig.SYSTEM_ALIVE_PUSH_QUEUE:
                // 预警报警（暂无用处）
                return bdataAlertIndicatorHandler;
            case RabbitTopicConfig.SYSTEM_ALIVE_QUEUE:
                // 子系统存活监测
                if (doSendMonitorInfoToMesService) {
                    return mesSystemMonitorHandler;
                }
                else {
                    return null;
                }
            case RabbitTopicConfig.WORKING_FACE_PROGRESS_SETTING_UPDATE_QUEUE:
            default:
                return null;
        }
    }

}
