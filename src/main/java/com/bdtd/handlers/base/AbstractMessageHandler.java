package com.bdtd.handlers.base;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public abstract class AbstractMessageHandler implements ChannelAwareMessageListener
{

    @Autowired
    protected ConcurrentHashMap<String, JSONObject> definitionViewMap;

    /**
     * for define flow: insert & update define table.
     * for realtime flow: if no <PERSON><PERSON>, ignore it.
     *
     * @param msgs          历史消息
     * @param routingKey    RoutingKey
     */
    protected abstract void updateTable(List<JSONObject> msgs, String routingKey);

    /**
     * Get key name
     *
     * @return /
     */
    protected abstract String getKeyName();

    /**
     * 消息处理
     *
     * @param message   消息内容
     * @param channel   通道
     */
    @Override
    public void onMessage(Message message, Channel channel) {
        long start = System.currentTimeMillis();
        try {
            // convert List string to object
            String routingKey = message.getMessageProperties().getReceivedRoutingKey();
            JSONArray msgs = JSONArray.parseArray(new String(message.getBody()));

            log.info("[{}] monitor receive size: {}", routingKey, msgs.size());
            if (msgs.isEmpty()) {
                log.info(routingKey + "数据为空");
                // ack message
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            List<JSONObject> cMsgs = preHandle(msgs.toJavaList(JSONObject.class));
            log.debug(String.format(" [%s] json解析耗时 : " + (System.currentTimeMillis() - start), routingKey));

            // check routing key
            handleDefine(cMsgs, routingKey);

            // ack message
            if (channel.isOpen()) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            log.debug(String.format(" [%s] 总耗时finish cost time : " + (System.currentTimeMillis() - start), routingKey));
        }
        catch (Exception e) {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            catch (IOException e1) {
                log.error("ack exception consume message error. retry it.", e);
            }
            log.error(message.getMessageProperties().getReceivedRoutingKey() + " consume message error. retry it.", e);
        }
    }

    /**
     * 去重自定义函数
     *
     * @param keyExtractor  key的获取函数
     * @param <T>
     * @return /
     */
    public static <T> Predicate<T> distinctByField(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>(16);
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * handle previous task. Default is return msgs.
     *
     * @param msgs         消费消息
     * @return /
     */
    protected abstract List<JSONObject> preHandle(List<JSONObject> msgs);

    /**
     * 写入历史库
     *
     * @param msgs          消费消息
     * @param routingKey    RoutingKey
     */
    protected abstract void insertHistoryTable(List<JSONObject> msgs, String routingKey);

    /**
     * 处理定义数据
     * 1. update db
     * 2. update redis
     * 3. update cache
     *
     * @param msgs          历史消息
     * @param routingKey    RoutingKey
     */
    private void handleDefine(List<JSONObject> msgs, String routingKey) {
        updateTable(msgs, routingKey);
    }

    /**
     * 处理实时数据
     * 1. check define whether exits or not.
     * 2. pre handle.
     * 3. update realtime table. Note: just for harsura.
     * 4. insert in history table.
     *
     * @param msgs          历史消息
     * @param routingKey    RoutingKey
     */
    private void handleRtData(List<JSONObject> msgs, String routingKey) {
        updateTable(msgs, routingKey);
        insertHistoryTable(msgs, routingKey);
    }

}
