package com.bdtd.handlers;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.conf.MesBusinessEnum;
import com.bdtd.feign.MesBizFeignService;
import com.bdtd.handlers.base.AbstractMessageHandler;
import com.bdtd.modules.monitor.entity.MesSystemMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class MesSystemMonitorHandler extends AbstractMessageHandler implements ChannelAwareMessageListener
{

    @Autowired
    MesBizFeignService mesBizFeignService;

    @Override
    protected void updateTable(List<JSONObject> msgs, String routingKey) {
        if (msgs == null || msgs.size() == 0) {
            log.error(routingKey + " message jsonArray is null ");
            return;
        }

        log.info("mes-biz：mes_system_monitor receive message :" + msgs.size());

        MesSystemMonitor item = null;
        for (int i = 0; i < msgs.size(); i++) {
            try {
                // item.setLastUploadDate(LocalDateTime.parse(d, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                item = JSONObject.parseObject(msgs.get(i).toString(), MesSystemMonitor.class);
            }
            catch (Exception ex) {
                log.error("在线系统监控消息转换失败：" + ex.getMessage(), ex);
                continue;
            }

            if (item == null) { continue; }

            // try {
            //     mesBizFeignService.insertSystemMonitor(item, MesBusinessEnum.HEADER.desc);
            // }
            // catch (Exception e) {
            //     log.error("在线监测子系统mes接口调用异常" + e);
            // }
            CompletableFuture<JSONObject> future = insertSystemMonitorFuture(item);
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("mes接口(insertSystemMonitor, 写入系统监控状态)调用异常: " + throwable.getMessage(), throwable);
                }
            });
        }
    }

    @Override
    protected List<JSONObject> preHandle(List<JSONObject> msgs) {
        return msgs;
    }

    @Override
    protected void insertHistoryTable(List<JSONObject> msgs, String routingKey) {
        // define flow, ignore.
    }

    @Override
    protected String getKeyName() {
        return "monitor_id";
    }

    private CompletableFuture<JSONObject> insertSystemMonitorFuture(MesSystemMonitor item) {
        return CompletableFuture.supplyAsync(() -> mesBizFeignService.insertSystemMonitor(item, MesBusinessEnum.HEADER.desc));
    }
}
