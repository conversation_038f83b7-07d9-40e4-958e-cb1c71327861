package com.bdtd.handlers;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.conf.MesBusinessEnum;
import com.bdtd.feign.MesBizFeignService;
import com.bdtd.handlers.base.AbstractMessageHandler;
import com.bdtd.modules.alarm.entity.GisWarningAlarmInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 预警报警消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BdataAlertIndicatorHandler extends AbstractMessageHandler implements ChannelAwareMessageListener
{

    @Autowired
    MesBizFeignService mesBizFeignService;

    @Override
    protected void updateTable(List<JSONObject> msgs, String routingKey) {
        if (msgs == null || msgs.size() == 0) {
            log.error(routingKey + " message jsonArray is null ");
            return;
        }

        log.info("auto_definition receive message :" + msgs.size());

        GisWarningAlarmInfo item = null;
        for (int i = 0; i < msgs.size(); i++) {
            item = JSONObject.parseObject(msgs.get(i).toString(), GisWarningAlarmInfo.class);
            item.setPushMessageJumpCode("00001");

            // try {
            //     mesBizFeignService.addWarninAlarmInfo(item, MesBusinessEnum.HEADER.desc);
            // }
            // catch (Exception e) {
            //     log.error("mes接口调用异常" + e);
            // }
            CompletableFuture<JSONObject> future = addWarningAlarmInfoFuture(item);
            future.whenComplete((result, ex) -> {
                if (ex != null) {
                    log.error("mes接口(addWarninAlarmInfo, 写入预警报警)调用异常: " + ex.getMessage(), ex);
                }
            });
        }
    }

    @Override
    protected List<JSONObject> preHandle(List<JSONObject> msgs) {
        return msgs;
    }

    @Override
    protected void insertHistoryTable(List<JSONObject> msgs, String routingKey) {
        // define flow, ignore.
    }

    @Override
    protected String getKeyName() {
        return "indictor_id";
    }

    private CompletableFuture<JSONObject> addWarningAlarmInfoFuture(GisWarningAlarmInfo item) {
        return CompletableFuture.supplyAsync(() -> mesBizFeignService.addWarninAlarmInfo(item, MesBusinessEnum.HEADER.desc));
    }
}
