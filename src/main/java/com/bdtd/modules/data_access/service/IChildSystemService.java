package com.bdtd.modules.data_access.service;


import com.bdtd.modules.data_access.entity.ChildSystem;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;

import java.util.List;


public interface IChildSystemService
{
    Msg save(ChildSystem childSystem);

    Msg update(ChildSystem childSystem);

    PageBean<ChildSystem> list(Integer currentPage, Integer pageSize);

    Msg delete(List<String> ids);
}
