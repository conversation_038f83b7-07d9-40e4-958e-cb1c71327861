package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.dto.ItemAlarmStatusQuery;
import com.bdtd.modules.data_access.entity.ItemAlarmStatus;
import com.bdtd.util.web.Msg;

/**
 * <p>
 * 报警状态表 服务类
 * </p>
 */
public interface IItemAlarmStatusService extends IService<ItemAlarmStatus> {

    Msg findAlarmStatusList(ItemAlarmStatusQuery itemAlarmStatus);

}
