package com.bdtd.modules.data_access.service;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.dto.DataDictionaryQuery;
import com.bdtd.modules.data_access.entity.DataDictionary;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;


public interface IDataDictionaryService
{
    PageBean<DataDictionary> list(DataDictionaryQuery dataDictionaryQuery);

    Msg save(DataDictionary dataDictionary);

    Msg update(DataDictionary dataDictionary);

    Msg deleteBatch(JSONObject jsonObject);
}
