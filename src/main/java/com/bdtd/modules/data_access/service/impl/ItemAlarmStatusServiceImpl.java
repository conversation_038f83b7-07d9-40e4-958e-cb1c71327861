package com.bdtd.modules.data_access.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.data_access.dto.ItemAlarmStatusQuery;
import com.bdtd.modules.data_access.entity.ItemAlarmStatus;
import com.bdtd.modules.data_access.mapper.ItemAlarmStatusMapper;
import com.bdtd.modules.data_access.service.IItemAlarmStatusService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 报警状态表 服务实现类
 * </p>
 */
@Service
public class ItemAlarmStatusServiceImpl extends ServiceImpl<ItemAlarmStatusMapper, ItemAlarmStatus> implements IItemAlarmStatusService {

    @Autowired
    private ItemAlarmStatusMapper itemAlarmStatusMapper;

    @Override
    public Msg findAlarmStatusList(ItemAlarmStatusQuery query) {
        QueryWrapper<ItemAlarmStatus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(query.getAlarmStatusCode()), "alarm_status_code", query.getAlarmStatusCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getAlarmStatusName()), "alarm_status_name", query.getAlarmStatusName());
        List<ItemAlarmStatus> itemAlarmStatuses = itemAlarmStatusMapper.selectList(queryWrapper);
        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", itemAlarmStatuses);
    }
}
