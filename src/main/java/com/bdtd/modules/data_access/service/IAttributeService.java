package com.bdtd.modules.data_access.service;


import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.entity.Attribute;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;

import java.util.List;
import java.util.Map;


public interface IAttributeService
{

    Msg save(Attribute attribute);

    Msg update(Attribute attribute);

    Msg findTypeById(String id);

    PageBean<Attribute> list(Integer currentPage, Integer pageSize, String name, String typeId);

    List<Map<String, Object>> listPointId();

    Map<String, Attribute> map();

    Map<Object, Object> attributeToTypeId();

    Msg remove(JSONObject jsonObject);
}
