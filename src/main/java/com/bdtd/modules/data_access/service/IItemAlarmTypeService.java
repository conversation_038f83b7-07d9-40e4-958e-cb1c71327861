package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.dto.ItemAlarmTypeQuery;
import com.bdtd.modules.data_access.entity.ItemAlarmType;
import com.bdtd.util.web.Msg;

/**
 * <p>
 * 报警类型表 服务类
 * </p>
 */
public interface IItemAlarmTypeService extends IService<ItemAlarmType> {

    Msg findItemAlarmTypeList(ItemAlarmTypeQuery query);

}
