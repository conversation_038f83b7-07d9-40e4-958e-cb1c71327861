package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.entity.ModelSystem;
import com.bdtd.util.web.Msg;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface IModelSystemService extends IService<ModelSystem>
{

    Msg getList(ModelSystem model);

    Msg updateByKey(ModelSystem modelSystem);

    /**
     * 根据煤矿编码、原系统ID禁用接入系统
     *
     * @param mineCode          煤矿编码
     * @param sourceSystemCodes 原系统ID
     * @return /
     */
    boolean disableBySourceSystemCodes(String mineCode, Set<String> sourceSystemCodes);

    /**
     * 根据煤矿编码、启用的原系统ID禁用接入系统
     *
     * @param mineCode                 煤矿编码
     * @param enabledSourceSystemCodes 原系统ID
     * @return /
     */
    boolean disableByEnabledSourceSystemCodes(String mineCode, Set<String> enabledSourceSystemCodes);

    /**
     * 根据煤矿编码禁用接入系统
     *
     * @param mineCodes 煤矿编码
     * @return /
     */
    boolean disableByEnabledMineCodes(Set<String> mineCodes);

    /**
     * 新增或者更新子系统信息
     *
     * @param list 子系统信息列表
     * @return /
     */
    boolean upsertBatch(List<ModelSystem> list);

    /**
     * 新增或者更新子系统信息 (更新时只可能开启数据接入，不关闭)
     *
     * @param list 子系统信息列表
     * @return /
     */
    boolean upsertBatchOnlyEnableRoute(List<ModelSystem> list);

}
