package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.entity.DingApproval;
import com.bdtd.util.web.Msg;

import java.util.List;

/**
 * <p>
 * 钉钉审批表 服务类
 * </p>
 */
public interface IDingApprovalService extends IService<DingApproval> {
    Msg approval(List<String> ids, String telephone, Integer approvalType, String name) throws Exception;

}
