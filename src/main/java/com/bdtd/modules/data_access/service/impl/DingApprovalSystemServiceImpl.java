package com.bdtd.modules.data_access.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.data_access.entity.DingApprovalSystem;
import com.bdtd.modules.data_access.mapper.DingApprovalSystemMapper;
import com.bdtd.modules.data_access.service.IDingApprovalSystemService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 */
@Service
public class DingApprovalSystemServiceImpl extends ServiceImpl<DingApprovalSystemMapper, DingApprovalSystem> implements IDingApprovalSystemService {

}
