package com.bdtd.modules.data_access.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.dto.DataDictionaryExample;
import com.bdtd.modules.data_access.dto.DataDictionaryQuery;
import com.bdtd.modules.data_access.entity.DataDictionary;
import com.bdtd.modules.data_access.mapper.DataDictionaryMapper;
import com.bdtd.modules.data_access.service.IDataDictionaryService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DataDictionaryServiceImpl implements IDataDictionaryService
{
    @Autowired
    private DataDictionaryMapper dataDictionaryMapper;

    @Override

    public PageBean<DataDictionary> list(DataDictionaryQuery dataDictionaryQuery) {
        DataDictionaryExample quickQueryExample = new DataDictionaryExample();
        DataDictionaryExample.Criteria criteria = quickQueryExample.createCriteria();
        Integer currentPage = dataDictionaryQuery.getCurrentPage();
        Integer pageSize = dataDictionaryQuery.getPageSize();
        if (dataDictionaryQuery.getName() != null) {
            criteria.andNameLike("%" + dataDictionaryQuery.getName() + "%");
        }
        if (dataDictionaryQuery.getType() != null) {
            criteria.andTypeLike("%" + dataDictionaryQuery.getType() + "%");
        }
        if (dataDictionaryQuery.getId() != null) {
            criteria.andIdEqualTo(dataDictionaryQuery.getId());
        }
        quickQueryExample.setOrderByClause("created_at DESC");
        List<DataDictionary> content;
        // 总共数量
        int totalSize;
        int totalPage;
        Page<DataDictionary> page;
        if (currentPage == null || pageSize == null) {
            currentPage = 1;
            pageSize = 0;
            page = PageHelper.startPage(currentPage, pageSize);
        }
        else {
            if (currentPage <= 0) {
                throw new RuntimeException("当前页必须大于0");
            }
            if (pageSize <= 0) {
                throw new RuntimeException("每页条数必须大于0");
            }
            page = PageHelper.startPage(currentPage, pageSize);
        }
        dataDictionaryMapper.selectByExample(quickQueryExample);
        content = page.getResult();
        totalSize = new Long(page.getTotal()).intValue();
        totalPage = page.getPages();
        currentPage = page.getPageNum();
        if (content.size() != 0 && pageSize == 0) {
            totalPage = 1;
        }
        return PageBean.<DataDictionary>builder()
                       .content(content)
                       .totalElements(totalSize)// 总条数
                       .totalPage(totalPage)// 总页数
                       .page(currentPage)
                       .size(content.size())// 本页条数
                       .build();
    }

    @Override
    public Msg save(DataDictionary dataDictionary) {
        dataDictionaryMapper.insert(dataDictionary);
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", null);
        return msg;
    }

    @Override
    public Msg update(DataDictionary dataDictionary) {
        Msg msg;
        if (dataDictionary.getId() == null) {
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "id is null", null);
            return msg;
        }
        DataDictionaryExample findExampleById = new DataDictionaryExample();
        findExampleById.createCriteria().andIdEqualTo(dataDictionary.getId());
        List<DataDictionary> listById = dataDictionaryMapper.selectByExample(findExampleById);
        // 传入的id查不到
        if (listById.size() == 0) {
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "update   result  is not exits", null);
            return msg;
        }
        dataDictionaryMapper.updateByPrimaryKeySelective(dataDictionary);
        msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", null);
        return msg;
    }

    @Override
    public Msg deleteBatch(JSONObject jsonObject) {
        JSONArray array = jsonObject.getJSONArray("ids");
        List<Integer> list = JSONObject.parseArray(array.toJSONString(), Integer.class);
        if (!list.isEmpty()) {
            dataDictionaryMapper.deleteBatch(list);
            return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", null);
        }
        else {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "id不能为空 ", null);
        }

    }
}
