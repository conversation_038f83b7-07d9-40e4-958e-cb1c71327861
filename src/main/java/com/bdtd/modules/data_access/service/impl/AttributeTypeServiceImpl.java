package com.bdtd.modules.data_access.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.dto.AttributeTypeExample;
import com.bdtd.modules.data_access.entity.AttributeType;
import com.bdtd.modules.data_access.mapper.AttributeTypeMapper;
import com.bdtd.modules.data_access.service.IAttributeTypeService;
import com.bdtd.util.Common;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class AttributeTypeServiceImpl implements IAttributeTypeService
{

    @Autowired
    AttributeTypeMapper attributeTypeMapper;

    @Transactional
    @Override
    public Msg save(AttributeType model) {
        Msg msg;
        AttributeTypeExample findAll = new AttributeTypeExample();
        findAll.createCriteria().andNameEqualTo(model.getName());
        List<AttributeType> list = attributeTypeMapper.selectByExample(findAll);
        int existCount = list.size();
        if (existCount == 0) {
            model.setId(Common.generateShortUuid());
            attributeTypeMapper.insert(model);
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", model);
        } else {
            msg = ReturnMsg.resultMsg(StatusEnum.REPEATED.value, "  name  not repeat ", null);
        }
        return msg;
    }

    @Transactional
    @Override
    public Msg update(AttributeType model) {
        Msg msg;
        if (model.getId() == null) {
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "id is null", null);
            return msg;
        }
        AttributeTypeExample findExampleById = new AttributeTypeExample();
        findExampleById.createCriteria().andIdEqualTo(model.getId());
        List<AttributeType> listById = attributeTypeMapper.selectByExample(findExampleById);
        //传入的id查不到
        if (listById.size() == 0) {
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "update   result  is not exits", null);
            return msg;
        }
        if (model.getName() == null) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, " no need update ", null);
        }
        AttributeTypeExample findExampleByName = new AttributeTypeExample();
        findExampleByName.createCriteria().andNameEqualTo(model.getName());
        List<AttributeType> listByName = attributeTypeMapper.selectByExample(findExampleByName);
        if (listByName.size() == 0 || model.getId().equals(listByName.get(0).getId())) {
            attributeTypeMapper.updateByPrimaryKeySelective(model);
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", null);
        } else {
            msg = ReturnMsg.resultMsg(StatusEnum.REPEATED.value, "code or name  not repeat", null);
        }
        return msg;
    }


    @Override
    public PageBean<AttributeType> list(Integer currentPage, Integer pageSize, String name) {
        AttributeTypeExample attributeTypeExample = new AttributeTypeExample();
        AttributeTypeExample.Criteria criteria = attributeTypeExample.createCriteria();
        if (name != null) {
            criteria.andNameLike("%" + name + "%");
        }
        attributeTypeExample.setOrderByClause("created_at DESC");
        List<AttributeType> content;
        //总共数量
        int totalSize;
        int totalPage;
        Page<AttributeType> page;
        if (currentPage == null || pageSize == null) {
            currentPage = 1;
            pageSize = 0;
            page = PageHelper.startPage(currentPage, pageSize);
        } else {
            if (currentPage <= 0) {
                throw new RuntimeException("当前页必须大于0");
            }
            if (pageSize <= 0) {
                throw new RuntimeException("每页条数必须大于0");
            }
            page = PageHelper.startPage(currentPage, pageSize);
        }
        attributeTypeMapper.selectByExample(attributeTypeExample);
        content = page.getResult();

        totalSize = new Long(page.getTotal()).intValue();
        totalPage = page.getPages();
        currentPage = page.getPageNum();
        if (content.size() != 0 && pageSize == 0) {
            totalPage = 1;
        }
        return PageBean.<AttributeType>builder()
                .content(content)
                .totalElements(totalSize)//本页条数
                .totalPage(totalPage)//总页数
                .page(currentPage)
                .size(content.size())//总条数
                .build();
    }

    @Override
    public Map<String, AttributeType> map() {
        AttributeTypeExample findAll = new AttributeTypeExample();
        findAll.createCriteria();
        findAll.setOrderByClause("created_at DESC");
        List<AttributeType> listAll = attributeTypeMapper.selectByExample(findAll);
        Map<String, AttributeType> map = new HashMap<>();
        for (AttributeType model : listAll) {
            map.put(model.getName(), model);
        }
        return map;
    }

    @Override
    public String[] array() {
        AttributeTypeExample findAll = new AttributeTypeExample();
        findAll.createCriteria();
        findAll.setOrderByClause("created_at DESC");
        List<AttributeType> list = attributeTypeMapper.selectByExample(findAll);
        String[] array = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            array[i] = list.get(i).getName();
        }
        return array;
    }

    @Transactional
    @Override
    public Msg remove(JSONObject jsonObject) {
        JSONArray array = jsonObject.getJSONArray("ids");
        for (int i = 0; i < array.size(); i++) {
            String id = array.getString(i);
            int deleteSuccess = attributeTypeMapper.deleteByPrimaryKey(id);
            if (deleteSuccess == 0) {
                return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "delete is fail", null);
            }
        }
        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", null);
    }
}
