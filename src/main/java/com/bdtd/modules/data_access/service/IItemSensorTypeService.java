package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.dto.ItemSensorTypeQuery;
import com.bdtd.modules.data_access.entity.ItemSensorType;
import com.bdtd.util.web.Msg;

/**
 * <p>
 * 传感器分类字典表 服务类
 * </p>
 */
public interface IItemSensorTypeService extends IService<ItemSensorType> {

    Msg findItemSensorTypeList(ItemSensorTypeQuery query);
}
