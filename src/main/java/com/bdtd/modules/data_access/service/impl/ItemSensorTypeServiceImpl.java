package com.bdtd.modules.data_access.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.data_access.dto.ItemSensorTypeQuery;
import com.bdtd.modules.data_access.entity.ItemSensorType;
import com.bdtd.modules.data_access.mapper.ItemSensorTypeMapper;
import com.bdtd.modules.data_access.service.IItemSensorTypeService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 传感器分类字典表 服务实现类
 * </p>
 */
@Service
public class ItemSensorTypeServiceImpl extends ServiceImpl<ItemSensorTypeMapper, ItemSensorType> implements IItemSensorTypeService {

    @Autowired
    private ItemSensorTypeMapper itemSensorTypeMapper;

    @Override
    public Msg findItemSensorTypeList(ItemSensorTypeQuery query) {
        List<ItemSensorType> itemSensorTypes = new ArrayList<>();
        if("0".equals(query.getIfFilter())){
            QueryWrapper<ItemSensorType> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(query.getSensorTypeCode()), "sensor_type_code", query.getSensorTypeCode());
            queryWrapper.eq(StringUtils.isNotBlank(query.getSensorTypeName()), "sensor_type_name", query.getSensorTypeName());
            queryWrapper.eq(StringUtils.isNotBlank(query.getPointValueTypeCode()), "point_value_type_code", query.getPointValueTypeCode());
            itemSensorTypes = itemSensorTypeMapper.selectList(queryWrapper);
        }else {
            itemSensorTypes = itemSensorTypeMapper.selectSensorTypeList(query);
        }
        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", itemSensorTypes);
    }

}
