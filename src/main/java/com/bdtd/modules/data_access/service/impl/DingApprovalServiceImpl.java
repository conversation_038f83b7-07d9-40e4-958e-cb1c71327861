package com.bdtd.modules.data_access.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.conf.Constant;
import com.bdtd.feign.DataRouteApprovalFeignService;
import com.bdtd.modules.data_access.entity.DingApproval;
import com.bdtd.modules.data_access.entity.DingApprovalSystem;
import com.bdtd.modules.data_access.entity.DingSystem;
import com.bdtd.modules.data_access.mapper.DingApprovalMapper;
import com.bdtd.modules.data_access.service.IDingApprovalService;
import com.bdtd.modules.data_access.service.IDingApprovalSystemService;
import com.bdtd.util.Common;
import com.bdtd.util.SendUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 钉钉审批表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DingApprovalServiceImpl extends ServiceImpl<DingApprovalMapper, DingApproval> implements IDingApprovalService
{

    @Value("${dataroute.approval.minecode}")
    private Integer mineCode;

    @Autowired
    private IDingApprovalSystemService dingApprovalSystemService;
    @Autowired
    private DataRouteApprovalFeignService dataRouteFeignService;

    @Override
    public Msg approval(List<String> ids, String telephone, Integer approvalType, String name) throws Exception {
        // 第一次请求数据接入后，先向本地插入一条审批记录
        DingApproval dingApproval = new DingApproval();

        LocalDateTime time = LocalDateTime.now(Clock.system(ZoneId.of("Asia/Shanghai")));
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String format = time.format(df);
        LocalDateTime beginTime = LocalDateTime.parse(format, df);

        dingApproval.setBeginTime(beginTime);
        dingApproval.setEndTime(null);
        dingApproval.setNodeRed(null);
        dingApproval.setNote(null);
        dingApproval.setSql(null);
        dingApproval.setId(Common.generateShortUuid());
        dingApproval.setMineCode(mineCode);
        dingApproval.setState(Constant.APPROVAL_PROCESSING);
        dingApproval.setIpAddress("127.0.0.1");
        dingApproval.setApprovalType(approvalType);

        List<DingApprovalSystem> list = dingApprovalSystemService.listByIds(ids);
        if (list != null && !list.isEmpty()) {
            JSONArray array = JSONArray.parseArray(JSON.toJSONString(list));
            dingApproval.setSystem(array);
        }
        else {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "无需要审批的系统", null);
        }

        dingApproval.setTelephone(telephone);
        dingApproval.setName(name);

        try {
            this.save(dingApproval);
        }
        catch (Exception e) {
            log.error("save ding approval error: " + e.getMessage(), e);
            throw new Exception("审批信息保存失败");
        }

        LinkedHashMap result = dataRouteFeignService.approval(dingApproval);
        log.info("cms request: " + dingApproval.toString());

        if ("99999".equals(result.get("status").toString())) {
            log.error("approval error, status: " + result.get("status").toString());
            throw new Exception("审批失败");
        }

        Msg msg = ReturnMsg.resultMsg(result.get("status").toString(), result.get("msg").toString(), null);

        // 发起人收到通知, 阿里短信
        Map<String, Object> map = new HashMap<>();
        map.put("phoneNumber", telephone);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", mineCode);

        String mineName;
        try {
            Map mineNameMap = dataRouteFeignService.findMineName(jsonObject);
            mineName = ((Map) ((List) ((Map) mineNameMap.get("data")).get("content")).get(0)).get("name").toString();
        }
        catch (Exception e) {
            log.error("findMineName error: " + e.getMessage(), e);
            throw new Exception("审批失败");
        }

        // 在"数据接入审批"中带上煤矿信息及需要接入的子系统
        StringBuffer buffer = new StringBuffer();

        // 根据system json 串提取接入系统信息
        List<DingSystem> dingSystems = JSONObject.parseArray(dingApproval.getSystem().toJSONString(), DingSystem.class);
        for (DingSystem system : dingSystems) {
            String systemName = system.getSystemName();
            buffer.append(systemName + ",");
        }

        String substring = buffer.substring(0, buffer.length() - 1);
        map.put("num", dingApproval.getId() + "的" + mineName + ":" + substring);
        String localTime = df.format(beginTime);
        map.put("starTime", localTime);
        map.put("type", Constant.APPROVAL_PROCESSING);

        try {
            SendUtil.send(map);
        }
        catch (Exception e) {
            log.warn("send sms failed.", e);
        }

        return msg;
    }
}
