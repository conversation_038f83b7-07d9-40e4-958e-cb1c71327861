package com.bdtd.modules.data_access.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.data_access.entity.DataDeviceModelConf;
import com.bdtd.modules.data_access.mapper.DataDeviceModelConfMapper;
import com.bdtd.modules.data_access.service.IDataDeviceModelConfService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * DataDeviceModelConf表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class DataDeviceModelConfServiceImpl extends ServiceImpl<DataDeviceModelConfMapper, DataDeviceModelConf> implements IDataDeviceModelConfService {

}