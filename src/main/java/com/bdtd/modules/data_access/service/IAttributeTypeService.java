package com.bdtd.modules.data_access.service;


import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.entity.AttributeType;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;

import java.util.Map;


public interface IAttributeTypeService
{
    Msg save(AttributeType attributeType);

    Msg update(AttributeType attributeType);

    PageBean<AttributeType> list(Integer currentPage, Integer pageSize, String name);

    Map<String, AttributeType> map();

    String[] array();

    Msg remove(JSONObject jsonObject);
}
