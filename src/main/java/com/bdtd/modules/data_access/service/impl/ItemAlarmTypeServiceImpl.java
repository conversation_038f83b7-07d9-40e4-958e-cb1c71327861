package com.bdtd.modules.data_access.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.data_access.dto.ItemAlarmTypeQuery;
import com.bdtd.modules.data_access.entity.ItemAlarmType;
import com.bdtd.modules.data_access.mapper.ItemAlarmTypeMapper;
import com.bdtd.modules.data_access.service.IItemAlarmTypeService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 报警类型表 服务实现类
 * </p>
 */
@Service
public class ItemAlarmTypeServiceImpl extends ServiceImpl<ItemAlarmTypeMapper, ItemAlarmType> implements IItemAlarmTypeService {

    @Autowired
    private ItemAlarmTypeMapper itemAlarmTypeMapper;

    @Override
    public Msg findItemAlarmTypeList(ItemAlarmTypeQuery query) {
        QueryWrapper<ItemAlarmType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(query.getAlarmTypeCode()), "alarm_type_code", query.getAlarmTypeCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getAlarmTypeName()), "alarm_type_name", query.getAlarmTypeName());
        List<ItemAlarmType> list = itemAlarmTypeMapper.selectList(queryWrapper);
        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
    }
}
