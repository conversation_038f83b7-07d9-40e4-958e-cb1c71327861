package com.bdtd.modules.data_access.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.data_access.dto.PatchLogCreateEntity;
import com.bdtd.modules.data_access.entity.PatchLog;
import com.bdtd.util.exception.BadRequestException;

/**
 * <p>
 * 工作面进尺定义 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IPatchLogService extends IService<PatchLog>
{
    /**
     * 新增补采请求
     *
     * @param creatEntity 新增用实体
     * @return /
     */
    Boolean create(PatchLogCreateEntity creatEntity) throws BadRequestException;

    /**
     * 更新补采日志
     *
     * @param patchLog 补采日志
     * @return 插入数量
     */
    Boolean updateLog(PatchLog patchLog);

    /**
     * 新增补采日志
     *
     * @param patchLog 补采日志
     * @return 插入数量
     */
    Boolean insertLog(PatchLog patchLog);

}
