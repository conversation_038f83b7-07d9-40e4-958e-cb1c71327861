package com.bdtd.modules.data_access.service;


import com.bdtd.modules.data_access.dto.SystemModelNoTable;
import com.bdtd.modules.data_access.entity.SystemModel;
import com.bdtd.util.web.Msg;

import java.util.List;
import java.util.Map;


public interface ISystemModelService
{
    Msg save(SystemModelNoTable system);

    List<SystemModelNoTable> list(SystemModel model);

    List<SystemModel> listDataAccessSystem(SystemModel model);

    String[] array();

    Msg update(SystemModelNoTable model);

    Map<String, String> map();

    List<SystemModel> listAll(String id);

    SystemModel getOneById(String id);

    String getHistoryTable(String id);

    String getUpdateTable(String systemId);

    String getDefinitionTable(String systemId);
}
