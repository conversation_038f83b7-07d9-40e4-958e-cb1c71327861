package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.dto.PatchLogCreateEntity;
import com.bdtd.modules.data_access.entity.ChildSystem;
import com.bdtd.modules.data_access.service.IPatchLogService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据补采日志
 *
 * <AUTHOR>
 */
@Api(tags = "数据补采日志")
@Slf4j
@RestController
@RequestMapping(value = { "/dataAccess/patchLog" })
public class PatchLogController
{
    @Autowired
    private IPatchLogService patchLogService;

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ChildSystem.class, responseContainer = "List") })
    @PostMapping("/create")
    public ResponseEntity<Object> create(@RequestBody(required = true) PatchLogCreateEntity creatEntity) {
        String msg = "操作失败";
        Boolean result = false;
        try {
            result = patchLogService.create(creatEntity);
        }
        catch (BadRequestException bre) {
            msg = bre.getMessage();
        }
        catch (Exception ex) {
            log.warn("Failed to create patch log, " + ex.getMessage(), ex);
        }

        return new ResponseEntity<>(
                result
                        ? ReturnMsg.success(true)
                        : ReturnMsg.fail(msg),
                HttpStatus.OK
        );
    }

}
