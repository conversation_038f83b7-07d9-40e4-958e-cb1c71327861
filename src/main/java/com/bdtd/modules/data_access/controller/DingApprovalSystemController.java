package com.bdtd.modules.data_access.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.conf.Constant;
import com.bdtd.modules.data_access.entity.DingApprovalSystem;
import com.bdtd.modules.data_access.service.IDingApprovalSystemService;
import com.bdtd.util.Common;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 数据接入系统控制器
 * </p>
 *
 * <AUTHOR>
 */
@Api(tags = "数据接入系统")
@RestController
@RequestMapping({ "/dataAccess/dingApprovalSystem", "/dingApprovalSystem" })
public class DingApprovalSystemController
{

    @Autowired
    private IDingApprovalSystemService dingApprovalSystemService;

    @Value("${dataroute.approval.minecode}")
    private Integer mineCode;

    /**
     * 配置需要数据接入的子系统
     */
    @PostMapping("/approvalSystemInsert")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity save(@RequestBody DingApprovalSystem entity) {
        QueryWrapper<DingApprovalSystem> wrapper = new QueryWrapper<>();
        wrapper.eq("system_name", entity.getSystemName());
        DingApprovalSystem one = dingApprovalSystemService.getOne(wrapper);
        if (one != null) {
            Msg msg = ReturnMsg.resultMsg(StatusEnum.REPEATED.value, "System Already Exsits", null);
            return new ResponseEntity<>(msg, HttpStatus.OK);
        }
        //新增新系统审批状态为未提交
        entity.setId(Common.generateShortUuid());
        entity.setApprovalState(Constant.APPROVAL_UNCOMMITED);
        entity.setMineCode(mineCode);
        dingApprovalSystemService.save(entity);
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "SUCCESS", null);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 数据接入系统展示
     */
    @PostMapping("/approvalSystemPageList")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity list(@RequestBody DingApprovalSystem entity) {
        QueryWrapper<DingApprovalSystem> wrapper = new QueryWrapper<>();
        if (entity.getSelectType() == 1) {
            //未提交和审批驳回
            wrapper.eq("business_type", entity.getBusinessType()).and(i ->
                    i.eq("approval_state", Constant.APPROVAL_REJECT)
                     .or().eq("approval_state", Constant.APPROVAL_UNCOMMITED));
        }
        else if (entity.getSelectType() == 2) {
            //审批通过和审批中
            wrapper.eq("business_type", entity.getBusinessType()).and(i ->
                    i.eq("approval_state", Constant.APPROVAL_PASSED)
                     .or().eq("approval_state", Constant.APPROVAL_PROCESSING));
        } /*else if (entity.getSelectType() == 1 && Constant.MODEL_NEW_MINE_SYSTEM.equals(entity.getBusinessType())) {
            //未提交和审批驳回
            wrapper.eq("approval_state", Constant.APPROVAL_UNCOMMITED)
                    .or(i -> i.eq("approval_state", Constant.APPROVAL_REJECT));
        } else if (entity.getSelectType() == 2 && Constant.MODEL_NEW_MINE_SYSTEM.equals(entity.getBusinessType())) {
            //审批通过和审批中
            wrapper.eq("approval_state", Constant.APPROVAL_PASSED)
                    .or(i -> i.eq("approval_state", Constant.APPROVAL_PROCESSING));
        } */
        // else {
        //     //查所有
        // }

        wrapper.orderByDesc("updated_at");
        IPage<DingApprovalSystem> page = new Page<>(entity.getPageNum(), entity.getPageSize());
        IPage<DingApprovalSystem> dingApprovalSystemPage = dingApprovalSystemService.page(page, wrapper);
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "SUCCESS", dingApprovalSystemPage);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 删除系统
     */
    @PostMapping("/approvalSystemRemove")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity remove(@RequestParam String id) {
        DingApprovalSystem byId = dingApprovalSystemService.getById(id);
        if (Constant.APPROVAL_PASSED.equals(byId.getApprovalState()) ||
                Constant.APPROVAL_PROCESSING.equals(byId.getApprovalState())) {
            Msg msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "审批中或者审批通过记录不允许删除", null);
            return new ResponseEntity<>(msg, HttpStatus.OK);
        }
        dingApprovalSystemService.removeById(id);
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "SUCCESS", null);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 更新系统其他字段
     */
    @PostMapping("/approvalSystemUpdate")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity update(@RequestBody DingApprovalSystem entity) {
        Msg msg;
        DingApprovalSystem byId = dingApprovalSystemService.getById(entity.getId());
        if (byId == null) {
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "NO ENTITY", null);
        }
        else {
            dingApprovalSystemService.updateById(entity);
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "SUCCESS", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
