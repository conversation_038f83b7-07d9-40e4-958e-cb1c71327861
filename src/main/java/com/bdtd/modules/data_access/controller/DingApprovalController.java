package com.bdtd.modules.data_access.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.conf.Constant;
import com.bdtd.feign.DataRouteApprovalFeignService;
import com.bdtd.modules.data_access.entity.DingApproval;
import com.bdtd.modules.data_access.entity.DingApprovalSystem;
import com.bdtd.modules.data_access.service.IDingApprovalService;
import com.bdtd.modules.data_access.service.IDingApprovalSystemService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 钉钉审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Api(tags = "数据接入审批请求")
@RestController
@RequestMapping({ "/dataAccess/dingApproval", "/dingApproval" })
@Slf4j
public class DingApprovalController
{
    @Value("${dataroute.approval.minecode}")
    private Integer mineCode;

    @Autowired
    private IDingApprovalService dingApprovalService;
    @Autowired
    private IDingApprovalSystemService dingApprovalSystemService;
    @Autowired
    private DataRouteApprovalFeignService feignService;

    /**
     * 已有主系统提交
     *
     * @param systems 提交系统信息
     * @return /
     */
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DingApproval.class) })
    @PostMapping("/existMineSystemApproval")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> existSystemApproval(@RequestBody JSONObject systems) throws Exception {
        // 提交已有主系统下的子系统
        JSONArray ids = systems.getJSONArray("ids");
        List<String> systemIds = JSONObject.parseArray(ids.toJSONString(), String.class);

        String telephone = systems.getString("telephone");
        String name = systems.getString("name");

        List<DingApprovalSystem> listDingApprovalSystem = new ArrayList<>();
        for (String id : systemIds) {
            // 更改数据接入系统表的审批状态：未提交->审批中
            DingApprovalSystem dingApprovalSystem = dingApprovalSystemService.getById(id);
            if (!Constant.MODEL_EXIST_MINE_SYSTEM.equals(dingApprovalSystem.getBusinessType())) {
                throw new Exception("必须为已有主业务系统的提交");
            }
            if (!Constant.APPROVAL_UNCOMMITED.equals(dingApprovalSystem.getApprovalState())
                    && !Constant.APPROVAL_REJECT.equals(dingApprovalSystem.getApprovalState())) {
                throw new Exception("提交系统的审批状态有误，应为未提交或驳回");
            }

            dingApprovalSystem.setApprovalState(Constant.APPROVAL_PROCESSING);

            listDingApprovalSystem.add(dingApprovalSystem);
        }

        dingApprovalSystemService.saveOrUpdateBatch(listDingApprovalSystem);

        return new ResponseEntity<>(
                dingApprovalService.approval(systemIds, telephone, Constant.APPROVAL_EXIST_MINE_SYSTEM, name),
                HttpStatus.OK
        );
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DingApproval.class) })
    @PostMapping("/newMineSystemApproval")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> newMineSystemApproval(@RequestBody JSONObject systems) throws Exception {
        //提交新主系统
        JSONArray ids = systems.getJSONArray("ids");
        List<String> systemIds = JSONObject.parseArray(ids.toJSONString(), String.class);
        String telephone = systems.getString("telephone");
        String name = systems.getString("name");
        List<DingApprovalSystem> listDingApprovalSystem = new ArrayList<>();

        for (int i = 0; i < systemIds.size(); i++) {
            //更改数据接入系统表的审批状态：未提交->审批中
            String id = systemIds.get(i);
            DingApprovalSystem dingApprovalSystem = dingApprovalSystemService.getById(id);
            if (i == 0) {
                //新主系统判断是否过期
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", dingApprovalSystem.getMineSystemName());
                Boolean newMineSystem = feignService.findMineSystemByName(jsonObject);
                if (!newMineSystem) {
                    Msg msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "mine system exist", null);
                    return new ResponseEntity<>(msg, HttpStatus.OK);
                }
                //判断该主系统为提交中或者提交通过
            }
            if (!Constant.MODEL_NEW_MINE_SYSTEM.equals(dingApprovalSystem.getBusinessType())) {
                throw new Exception("必须为新主业务系统的提交");
            }
            if (!Constant.APPROVAL_UNCOMMITED.equals(dingApprovalSystem.getApprovalState())
                    && !Constant.APPROVAL_REJECT.equals(dingApprovalSystem.getApprovalState())) {
                throw new Exception("提交系统的审批状态有误，应为未提交或驳回");
            }
            dingApprovalSystem.setApprovalState(Constant.APPROVAL_PROCESSING);
            listDingApprovalSystem.add(dingApprovalSystem);
        }
        dingApprovalSystemService.saveOrUpdateBatch(listDingApprovalSystem);
        Integer approvalType = Constant.APPROVAL_NEW_MINE_SYSTEM;
        Msg msg = dingApprovalService.approval(systemIds, telephone, approvalType, name);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 审批历史记录
     *
     * @return
     */
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DingApproval.class) })
    @PostMapping("/history")
    public ResponseEntity<Object> list(@RequestBody DingApproval dingApproval) {
        QueryWrapper<DingApproval> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("begin_time");
        IPage<DingApproval> page = new Page<>(dingApproval.getPageNum(), dingApproval.getPageSize());
        IPage<DingApproval> dingApprovalPage = dingApprovalService.page(page, wrapper);
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "data not null", dingApprovalPage);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = String.class) })
    @GetMapping("/mineCode")
    public ResponseEntity<Object> mineCode() {
        Integer code = mineCode;
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "data not null", code);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DingApproval.class) })
    @PostMapping("/mineSystem/list")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> getMineSystemList(@RequestBody JSONObject postForm) throws Exception {
        JSONObject resultObj = feignService.findMineSystemList(postForm);
        return new ResponseEntity<>(ReturnMsg.success(resultObj), HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DingApproval.class) })
    @PostMapping("/modelSystem/list")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> getModelSystemList(@RequestBody JSONObject postForm) throws Exception {
        JSONObject resultObj = feignService.findModelSystemList(postForm);
        return new ResponseEntity<>(ReturnMsg.success(resultObj), HttpStatus.OK);
    }

}
