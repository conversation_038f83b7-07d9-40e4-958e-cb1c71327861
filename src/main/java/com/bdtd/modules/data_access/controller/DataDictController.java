package com.bdtd.modules.data_access.controller;

import com.bdtd.modules.data_access.entity.DataDict;
import com.bdtd.modules.data_access.mapper.DataDictMapper;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据字典控制器
 *
 * <AUTHOR>
 */
@Api(tags = "全量字典")
@RestController
@RequestMapping({ "/dataAccess/dataDict", "/dataDict" })
public class DataDictController
{

    @Autowired
    private DataDictMapper dataDictMapper;

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success") })
    @GetMapping("/list")
    public ResponseEntity<Object> list() {
        List<DataDict> list = dataDictMapper.list();
        Map<String, List<DataDict>> map = list.stream().collect(Collectors.groupingBy(o -> o.getType()));
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", map);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }


}
