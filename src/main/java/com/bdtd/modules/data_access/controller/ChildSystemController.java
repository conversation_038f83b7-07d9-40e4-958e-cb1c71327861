package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.entity.ChildSystem;
import com.bdtd.modules.data_access.service.IChildSystemService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.DeleteIdsModel;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 子系统控制器
 *
 * <AUTHOR>
 */
@Api(tags = "子系统")
@RestController
@RequestMapping(value = { "/dataAccess/childSystem", "/childSystem" })
public class ChildSystemController
{
    @Autowired
    private IChildSystemService childSystemService;

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ChildSystem.class, responseContainer = "List") })
    @GetMapping("/list")
    public ResponseEntity<Object> list(@RequestParam(required = false) Integer currentPage, @RequestParam(required = false) Integer pageSize) {
        PageBean<ChildSystem> list = childSystemService.list(currentPage, pageSize);
        if (list.getContent() == null || list.getContent().size() == 0) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ChildSystem.class) })
    @PostMapping("/add")
    public ResponseEntity<Object> add(@RequestBody ChildSystem childSystem) {
        Msg msg = childSystemService.save(childSystem);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @PostMapping("/delete")
    public ResponseEntity<Object> delete(@RequestBody DeleteIdsModel model) {
        Msg msg = childSystemService.delete(model.getIds());
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ChildSystem.class) })
    @PutMapping("/update")
    public ResponseEntity<Object> update(@RequestBody ChildSystem model) {
        Msg msg = childSystemService.update(model);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
