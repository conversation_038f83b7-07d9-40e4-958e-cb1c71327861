package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.entity.ModelSystem;
import com.bdtd.modules.data_access.service.IModelSystemService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Api(tags = "业务子系统")
@RestController
@RequestMapping({ "/dataAccess/modelSystem", "/modelSystem" })
public class ModelSystemController
{
    private final IModelSystemService service;

    @Autowired
    public ModelSystemController(IModelSystemService service) { this.service = service; }

    @ApiOperation("业务子系统列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Msg.class) })
    @PostMapping("/list")
    public ResponseEntity<Msg> list(@RequestBody ModelSystem model) {
        return new ResponseEntity<>(
                service.getList(model),
                HttpStatus.OK
        );
    }

    @ApiOperation("业务子系统配置信息")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Msg.class) })
    @GetMapping("/{systemId}")
    public ResponseEntity<Msg> showConfig(@PathVariable String systemId) {
        ModelSystem modelSystem = service.getById(systemId);
        // 前端不展示这些 CMS 端控制的信息
        if (modelSystem != null) {
            modelSystem.setConfTable(null);
            modelSystem.setUpdateTable(null);
            modelSystem.setHistoryTable(null);
            modelSystem.setConfTableStruct(null);
            modelSystem.setExchange(null);
            modelSystem.setDefinitionRoutingKey(null);
            modelSystem.setUpdateRoutingKey(null);
        }

        return new ResponseEntity<>(
                ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", modelSystem),
                HttpStatus.OK
        );
    }

    @ApiOperation("业务子系统接入配置")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Msg.class) })
    @PostMapping("/update")
    public ResponseEntity<Msg> updateConfig(@RequestBody ModelSystem modelSystem) {
        return new ResponseEntity<>(
                service.updateByKey(modelSystem),
                HttpStatus.OK
        );
    }
}
