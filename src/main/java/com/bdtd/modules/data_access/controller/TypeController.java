package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.entity.Type;
import com.bdtd.modules.data_access.service.ITypeService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@Api(tags = "类型字典表")
@RestController
@RequestMapping(value = { "/dataAccess/type", "/type" })
public class TypeController
{

    @Autowired
    private ITypeService service;

    @ApiOperation(value = "查询类型字典表数据")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Type.class, responseContainer = "List") })
    @PostMapping("/list")
    public ResponseEntity list(@RequestBody Type model) {
        PageBean<Type> list = service.list(model);
        if (list.getContent() == null || list.getContent().size() == 0) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiOperation(value = "更新类型字典表数据")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Type.class) })
    @PostMapping("/update")
    public ResponseEntity update(@RequestBody Type model) {
        Msg msg = service.update(model);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiOperation(value = "添加name不同的类型字典表数据，")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Type.class) })
    @PostMapping("/add")
    public ResponseEntity add(@RequestBody Type model) {
        Msg msg = service.save(model);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiOperation(value = "根据id删除类型字典表数据")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = Type.class) })
    @DeleteMapping("/delete")
    public ResponseEntity delete(@RequestParam("id") String id) {
        Msg msg = service.delete(id);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /*
    @ApiResponses(value = {@ApiResponse(code = 200, message = "success", response = Type.class)})
    @PostMapping("/test")
    public ResponseEntity test() {
        Msg msg = service.test();
        return new ResponseEntity<>(msg, HttpStatus.OK);
    } */

}
