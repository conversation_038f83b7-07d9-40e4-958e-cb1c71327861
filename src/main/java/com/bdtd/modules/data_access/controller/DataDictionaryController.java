package com.bdtd.modules.data_access.controller;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.data_access.dto.DataDictionaryQuery;
import com.bdtd.modules.data_access.entity.DataDictionary;
import com.bdtd.modules.data_access.service.IDataDictionaryService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.PageBean;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 数据字典控制器
 *
 * <AUTHOR>
 */
@Api(tags = "数据-字典")
@RestController
@RequestMapping({ "/dataAccess/dataDictionary", "/dataDictionary" })
public class DataDictionaryController
{

    @Autowired
    private IDataDictionaryService dataDictionaryService;

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = DataDictionary.class, responseContainer = "List") })
    @PostMapping("/list")
    public ResponseEntity<Object> list(@RequestBody DataDictionaryQuery dataDictionaryQuery) {
        PageBean<DataDictionary> list = dataDictionaryService.list(dataDictionaryQuery);
        if (list.getContent() == null || list.getContent().size() == 0) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success") })
    @PostMapping("/add")
    public ResponseEntity<Object> add(@RequestBody DataDictionary dataDictionary) {
        Msg msg = dataDictionaryService.save(dataDictionary);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success") })
    @PutMapping("/update")
    public ResponseEntity<Object> update(@RequestBody DataDictionary dataDictionary) {
        Msg msg = dataDictionaryService.update(dataDictionary);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @PostMapping("/delete")
    public ResponseEntity<Object> remove(@RequestBody JSONObject jsonObject) {
        Msg msg = dataDictionaryService.deleteBatch(jsonObject);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }


    //    @ApiResponses(value = {@ApiResponse(code = 200, message = "success", response = ImageModel.class, responseContainer = "List")})
    //    @PostMapping("/list")
    //    public ResponseEntity<Object> all() {
    //        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
    //        return new ResponseEntity<>(msg, HttpStatus.OK);
    //    }
}
