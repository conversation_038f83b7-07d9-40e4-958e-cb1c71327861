package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.dto.SystemModelNoTable;
import com.bdtd.modules.data_access.entity.SystemModel;
import com.bdtd.modules.data_access.service.ISystemModelService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 系统模型
 *
 * <AUTHOR>
 */
@Api(tags = "模型-系统")
@RestController
@RequestMapping(value = { "/dataAccess/system", "/system" })
public class SystemModelController
{

    @Autowired
    private ISystemModelService service;

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class, responseContainer = "List") })
    @GetMapping("/list")
    public ResponseEntity<Object> list(SystemModel model) {
        List<SystemModelNoTable> list = service.list(model);
        if (list == null || list.isEmpty()) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class, responseContainer = "List") })
    @GetMapping("/listDataAccessSystem")
    public ResponseEntity<Object> listDataAccessSystem(SystemModel model) {
        List<SystemModel> list = service.listDataAccessSystem(model);
        if (list == null || list.isEmpty()) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class) })
    @PostMapping("/add")
    public ResponseEntity<Object> add(@RequestBody SystemModelNoTable system) {
        Msg msg = service.save(system);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class) })
    @PutMapping("/update")
    public ResponseEntity<Object> update(@RequestBody SystemModelNoTable system) {
        Msg msg = service.update(system);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModel.class) })
    @GetMapping("/listall/{id}")
    public ResponseEntity<Object> listAll(@PathVariable String id) {
        List<SystemModel> list = service.listAll(id);
        if (list == null || list.isEmpty()) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", list), HttpStatus.OK);
        }
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class) })
    @GetMapping("/map")
    public ResponseEntity<Object> map() {
        Map<String, String> map = service.map();
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", map);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = SystemModelNoTable.class) })
    @GetMapping("/array")
    public ResponseEntity<Object> array() {
        String[] array = service.array();
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", array);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /*
    @ApiResponses(value = {@ApiResponse(code = 200, message = "success", response = SystemModel.class)})
    @PutMapping("/mapByTname")
    public ResponseEntity<Object> sourceToSystemId() {
        Map<String, String> map = service.mapTname();
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", map);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    } */
}
