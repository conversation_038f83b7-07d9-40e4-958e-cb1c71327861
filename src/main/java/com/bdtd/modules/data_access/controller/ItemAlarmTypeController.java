package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.dto.ItemAlarmTypeQuery;
import com.bdtd.modules.data_access.entity.ItemAlarmType;
import com.bdtd.modules.data_access.service.IItemAlarmTypeService;
import com.bdtd.util.web.Msg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 报警类型表 前端控制器
 * </p>
 * <AUTHOR>
 */
@RestController
@Api(tags = "报警状态-iItemAlarmType")
@RequestMapping({ "/dataAccess/itemAlarmType", "/itemAlarmType" })
public class ItemAlarmTypeController
{

    @Autowired
    private IItemAlarmTypeService iItemAlarmTypeService;

    @ApiOperation("查询报警状态列表展示")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ItemAlarmType.class) })
    @GetMapping("/list")
    public ResponseEntity<Object> findAlarmStatusList(ItemAlarmTypeQuery query) {
        Msg msg = iItemAlarmTypeService.findItemAlarmTypeList(query);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
