package com.bdtd.modules.data_access.controller;


import com.bdtd.modules.data_access.dto.ItemSensorTypeQuery;
import com.bdtd.modules.data_access.entity.ItemSensorType;
import com.bdtd.modules.data_access.service.IItemSensorTypeService;
import com.bdtd.util.web.Msg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 传感器分类字典表 前端控制器
 * </p>
 * <AUTHOR>
 */
@RestController
@Api(tags = "传感器分类字典表")
@RequestMapping({ "/dataAccess/itemSensorType", "/itemSensorType" })
public class ItemSensorTypeController
{

    @Autowired
    private IItemSensorTypeService iItemSensorTypeService;

    @ApiOperation("查询传感器分类列表展示")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "success", response = ItemSensorType.class) })
    @GetMapping("/list")
    public ResponseEntity<Object> findItemSensorTypeList(ItemSensorTypeQuery query) {
        Msg msg = iItemSensorTypeService.findItemSensorTypeList(query);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }


}
