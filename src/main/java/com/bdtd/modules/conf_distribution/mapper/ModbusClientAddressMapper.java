package com.bdtd.modules.conf_distribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ModbusClientAddress;
import com.bdtd.modules.conf_distribution.vo.ModbusClientAddressVO;

import java.util.List;

/**
 * Mapper 接口
 */
public interface ModbusClientAddressMapper extends BaseMapper<ModbusClientAddress>
{

    /**
     * 自定义分页
     *
     * @param page
     * @param modbusClientAddress
     * @return
     */
    List<ModbusClientAddressVO> selectModbusClientAddressPage(IPage page, ModbusClientAddressVO modbusClientAddress);

}
