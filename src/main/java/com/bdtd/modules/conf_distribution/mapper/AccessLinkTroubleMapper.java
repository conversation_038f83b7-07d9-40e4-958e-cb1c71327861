package com.bdtd.modules.conf_distribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.conf_distribution.entity.ControlSource;

/**
 * <p>
 * 控制源管理  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface AccessLinkTroubleMapper extends BaseMapper<ControlSource> {

    IPage<ControlSource> selectPage(Page page, String ip, String port, String userName);

}