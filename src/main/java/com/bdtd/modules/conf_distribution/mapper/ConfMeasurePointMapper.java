package com.bdtd.modules.conf_distribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.dto.ConfMeasurePointDTO;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测点
 *
 * <AUTHOR>
 */
@Mapper
public interface ConfMeasurePointMapper extends BaseMapper<ConfMeasurePoint> {

    List<ConfMeasurePoint> selectWhitePointPages(IPage<ConfMeasurePoint> page, @Param("param") ConfMeasurePointDTO confMeasurePointDTO);

}
