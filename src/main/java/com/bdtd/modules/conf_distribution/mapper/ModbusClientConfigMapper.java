package com.bdtd.modules.conf_distribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.vo.ModbusClientConfigVO;

import java.util.List;

/**
 * Mapper 接口
 */
public interface ModbusClientConfigMapper extends BaseMapper<ModbusClientConfig>
{

    /**
     * 自定义分页
     *
     * @param page
     * @param modbusClientConfig
     * @return
     */
    List<ModbusClientConfigVO> selectModbusClientConfigPage(IPage page, ModbusClientConfigVO modbusClientConfig);

}
