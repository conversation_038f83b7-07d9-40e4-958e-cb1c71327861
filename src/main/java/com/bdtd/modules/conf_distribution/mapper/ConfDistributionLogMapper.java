package com.bdtd.modules.conf_distribution.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组态下发日志 Mapper 接口
 */
@Mapper
public interface ConfDistributionLogMapper extends BaseMapper<ConfDistributionLog>
{

    /**
     * 自定义分页
     *
     * @param page
     * @param confDistributionLog
     * @return
     */
    List<ConfDistributionLogVO> selectConfDistributionLogPage(IPage page, @Param("param") ConfDistributionLogVO confDistributionLog);

    /**
     * 方法描述: 分页查询（连接查询）
     *
     * @param page
     * @param param
     * @throws
     * @Return {@link IPage< ConfDistributionLogVO>}
     * <AUTHOR>
    IPage<ConfDistributionLogVO> selectPageConditional(IPage<ConfDistributionLogVO> page, @Param("param") ConfDistributionLogVO param);

}
