package com.bdtd.modules.conf_distribution.support;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode
@Data
@NoArgsConstructor
public class Query
{

    @ApiModelProperty("当前页")
    private Integer current;

    @ApiModelProperty("每页的数量")
    private Integer size;

    @ApiModelProperty(hidden = true)
    private String ascs;

    @ApiModelProperty(hidden = true)
    private String descs;

}
