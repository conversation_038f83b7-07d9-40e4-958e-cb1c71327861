package com.bdtd.modules.conf_distribution.support;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.bean.BeanUtil;

import java.util.HashMap;
import java.util.Map;


/**
 * 查询条件
 *
 * <AUTHOR>
 */
public class Condition
{
    public Condition() {
    }

    public static <T> IPage<T> getPage(Query query) {
        Page<T> page = new Page((long) FuncUtil.toInt(query.getCurrent(), 1), (long) FuncUtil.toInt(query.getSize(), 10));
        page.setAsc(FuncUtil.toStrArray(SqlKeyword.filter(query.getAscs())));
        page.setDesc(FuncUtil.toStrArray(SqlKeyword.filter(query.getDescs())));
        return page;
    }

    public static <T> QueryWrapper<T> getQueryWrapper(T entity) {
        return new QueryWrapper(entity);
    }

    public static <T> QueryWrapper<T> getQueryWrapper(Map<String, Object> query, Class<T> clazz) {
        Map<String, Object> exclude = new HashMap()
        {{
            put("Blade-Auth", "Blade-Auth");
            put("current", "current");
            put("size", "size");
            put("ascs", "ascs");
            put("descs", "descs");
        }};
        exclude.forEach((k, v) -> {
            query.remove(k);
        });
        QueryWrapper<T> qw = new QueryWrapper();
        qw.setEntity(BeanUtil.newInstance(clazz));
        SqlKeyword.buildCondition(query, qw);
        return qw;
    }


}
