package com.bdtd.modules.conf_distribution.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 后管配置
 *
 * <AUTHOR>
 */
@Component
@Data
public class BackendConfig
{

    @Value("${blade.backend.url}")
    private String baseUrl;

    @Value("${backend.context-path}")
    private String contextPath;

    @Value("${backend.url.currentuser}")
    private String currentUserUrl;

    @Value("${backend.url.userinfo}")
    private String userInfoUrl;

    @Value("${backend.url.userlist}")
    private String userListUrl;

    @Value("${backend.url.userbyid}")
    private String userByIdUrl;

}
