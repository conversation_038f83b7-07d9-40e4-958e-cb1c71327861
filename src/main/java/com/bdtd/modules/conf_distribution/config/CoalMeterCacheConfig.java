package com.bdtd.modules.conf_distribution.config;

import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 配置
 *
 * <AUTHOR>
 */
@Configuration
public class CoalMeterCacheConfig
{

    @Bean("modbusClientMap")
    public ConcurrentHashMap<Long, ModbusClientConfig> modbusMasterStaticMap() {
        return new ConcurrentHashMap<>(16);
    }
}
