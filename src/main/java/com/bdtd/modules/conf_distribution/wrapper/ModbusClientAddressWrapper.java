/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.conf_distribution.entity.ModbusClientAddress;
import com.bdtd.modules.conf_distribution.vo.ModbusClientAddressVO;
import com.bdtd.util.bean.BeanUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 */
public class ModbusClientAddressWrapper extends AbstractBaseEntityWrapper<ModbusClientAddress, ModbusClientAddressVO>
{

    public static ModbusClientAddressWrapper build() {
        return new ModbusClientAddressWrapper();
    }

    @Override
    public ModbusClientAddressVO entityVO(ModbusClientAddress modbusClientAddress) {
        ModbusClientAddressVO modbusClientAddressVO = Objects.requireNonNull(BeanUtil.copy(modbusClientAddress, ModbusClientAddressVO.class));
        // VO字段填充
        return modbusClientAddressVO;
    }

    @Override
    public List<ModbusClientAddressVO> listVO(List<ModbusClientAddress> list) {
        return list.stream().map(this::entityVO).collect(Collectors.toList());
    }

    @Override
    public IPage<ModbusClientAddressVO> pageVO(IPage<ModbusClientAddress> pages) {
        List<ModbusClientAddressVO> records = listVO(pages.getRecords());
        IPage<ModbusClientAddressVO> pageVo = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

}
