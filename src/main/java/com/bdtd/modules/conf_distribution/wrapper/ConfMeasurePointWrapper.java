package com.bdtd.modules.conf_distribution.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.vo.ConfMeasurePointVO;
import com.bdtd.util.bean.BeanUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 测点
 */
public class ConfMeasurePointWrapper extends AbstractBaseEntityWrapper<ConfMeasurePoint, ConfMeasurePointVO>
{

    public static ConfMeasurePointWrapper build() {
        return new ConfMeasurePointWrapper();
    }

    @Override
    public ConfMeasurePointVO entityVO(ConfMeasurePoint entity) {
        ConfMeasurePointVO confAuthoritiesVO = Objects.requireNonNull(BeanUtil.copy(entity, ConfMeasurePointVO.class));
        return confAuthoritiesVO;
    }

    @Override
    public List<ConfMeasurePointVO> listVO(List<ConfMeasurePoint> list) {
        return list.stream().map(this::entityVO).collect(Collectors.toList());
    }

    @Override
    public IPage<ConfMeasurePointVO> pageVO(IPage<ConfMeasurePoint> pages) {
        List<ConfMeasurePointVO> records = listVO(pages.getRecords());
        IPage<ConfMeasurePointVO> pageVo = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }
}
