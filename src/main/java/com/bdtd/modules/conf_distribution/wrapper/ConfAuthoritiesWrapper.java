package com.bdtd.modules.conf_distribution.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.base.entity.User;
import com.bdtd.modules.conf_distribution.entity.ConfAuthorities;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.modules.conf_distribution.vo.ConfAuthoritiesVO;
import com.bdtd.modules.conf_distribution.vo.UserSimpleVO;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StringUtil;
import com.bdtd.util.bean.BeanUtil;
import com.bdtd.util.spring.SpringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 测点权限wrapper
 */
public class ConfAuthoritiesWrapper extends AbstractBaseEntityWrapper<ConfAuthorities, ConfAuthoritiesVO>
{

    // public static final UserService USER_SERVICE;
    public static final ConfMeasurePointService CONF_MEASURE_POINT_SERVICE;

    static {
        // USER_SERVICE = SpringUtils.getBean(UserService.class);
        CONF_MEASURE_POINT_SERVICE = SpringUtils.getBean(ConfMeasurePointService.class);
    }

    public static ConfAuthoritiesWrapper build() {
        return new ConfAuthoritiesWrapper();
    }


    @Override
    public ConfAuthoritiesVO entityVO(ConfAuthorities entity) {
        ConfAuthoritiesVO confAuthoritiesVO = Objects.requireNonNull(BeanUtil.copy(entity, ConfAuthoritiesVO.class));
        List<UserSimpleVO> userSimpleVO = new ArrayList<>();
        String userId = StringUtil.substring(entity.getUserIds(), 1, entity.getUserIds().length() - 1).replace(" ", "");
        List<Long> userIds = FuncUtil.toLongList(userId);
        List<User> users = BackendUtil.getInstance().getUserListByUserId(userIds);

        users.forEach(user -> {
            UserSimpleVO vo = new UserSimpleVO();
            vo.setId(user.getId());
            vo.setName(user.getName());
            userSimpleVO.add(vo);
        });

        ConfMeasurePoint measurePoint = CONF_MEASURE_POINT_SERVICE.getById(entity.getPointId());
        confAuthoritiesVO.setName(null == measurePoint ? "" : measurePoint.getName());
        confAuthoritiesVO.setUsers(userSimpleVO);
        return confAuthoritiesVO;
    }

    @Override
    public List<ConfAuthoritiesVO> listVO(List<ConfAuthorities> list) {
        return list.stream().map(this::entityVO).collect(Collectors.toList());
    }

    @Override
    public IPage<ConfAuthoritiesVO> pageVO(IPage<ConfAuthorities> pages) {
        List<ConfAuthoritiesVO> records = listVO(pages.getRecords());
        IPage<ConfAuthoritiesVO> pageVo = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }


}
