package com.bdtd.modules.conf_distribution.wrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.conf_distribution.entity.ConfBlackList;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.modules.conf_distribution.vo.ConfBlackListVO;
import com.bdtd.util.bean.BeanUtil;
import com.bdtd.util.spring.SpringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 测点黑名单
 *
 *
 *
 * <AUTHOR>
public class ConfBlackListWrapper extends AbstractBaseEntityWrapper<ConfBlackList, ConfBlackListVO>
{
    public static ConfBlackListWrapper build() {
        return new ConfBlackListWrapper();
    }

    public static final ConfMeasurePointService CONF_MEASURE_POINT_SERVICE;

    static {
        CONF_MEASURE_POINT_SERVICE = SpringUtils.getBean(ConfMeasurePointService.class);
    }

    @Override
    public ConfBlackListVO entityVO(ConfBlackList entity) {
        ConfBlackListVO confBlackListVO = Objects.requireNonNull(BeanUtil.copy(entity, ConfBlackListVO.class));
        ConfMeasurePoint measurePoint = CONF_MEASURE_POINT_SERVICE.getById(entity.getPointId());
        confBlackListVO.setName(null==measurePoint?"":measurePoint.getName());
        confBlackListVO.setOutPointId(null==measurePoint?"":measurePoint.getOutPointId());
        return confBlackListVO;
    }

    @Override
    public List<ConfBlackListVO> listVO(List<ConfBlackList> list) {
        return list.stream().map(this::entityVO).collect(Collectors.toList());
    }

    @Override
    public IPage<ConfBlackListVO> pageVO(IPage<ConfBlackList> pages) {
        List<ConfBlackListVO> records = listVO(pages.getRecords());
        IPage<ConfBlackListVO> pageVo = new Page<>(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }
}
