package com.bdtd.modules.conf_distribution.feign;

import com.alibaba.fastjson.JSONObject;
import feign.Headers;
import feign.QueryMap;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.Map;

/**
 *  Feign 调用接口
 */
public interface ConfDistributionFeignClient {

    @RequestLine("POST")
    @Headers("Content-Type: application/json")
    JSONObject postSomething(URI baseUri, @RequestBody Map<String, Object> paramMap);


    @RequestLine("GET")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    JSONObject getSomething(URI baseUri, @QueryMap Map<String, Object> paramMap);
}
