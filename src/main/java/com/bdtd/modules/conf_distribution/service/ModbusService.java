package com.bdtd.modules.conf_distribution.service;


import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.entity.ModbusMasterModel;
import com.serotonin.modbus4j.exception.ErrorResponseException;
import com.serotonin.modbus4j.exception.ModbusTransportException;

import java.util.List;

/**
 * Modbus Service
 */
public interface ModbusService {
    boolean writeModbus(Integer masterBeltId, int slaveId, int writeOffset, int writeValue) throws Exception;

    boolean writeModbus(ModbusMasterModel modbusMasterModel, int slaveId, int writeOffset, int writeValue) throws Exception;

    boolean writeModbus(ModbusClientConfig clientConfig, int slaveId, int writeOffset, int writeValue) throws ErrorResponseException, ModbusTransportException;

    Double readModbus(ModbusClientConfig clientConfig, int slaveId, int writeOffset) throws ErrorResponseException, ModbusTransportException;

    Double readModbus(Integer masterBeltId, int offset) throws Exception;

    List<Double> readSerialMoudlebus(ModbusMasterModel modbusMasterModel, int slaveId, int start, int len) throws Exception;
}
