package com.bdtd.modules.conf_distribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.conf_distribution.entity.ControlSource;
import com.bdtd.util.web.Msg;

import java.util.List;

/**
 * <p>
 * 控制源管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IAccessLinkTroubleService extends IService<ControlSource> {

    IPage<ControlSource> controlSourceList(Integer pageNum, Integer pageSize, String ip, Integer port, String userName);

    Msg controlSourceInsert(List<ControlSource> controlSource);

    Msg controlSourceUpdate(ControlSource controlSource);

    Msg controlSourceDelete(List<Integer> ids);

}