package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.base.entity.BaseEntity;
import com.bdtd.modules.conf_distribution.service.BaseService;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.util.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 控制命令下发基类
 *
 * <AUTHOR>
 */
public class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements BaseService<T>
{

    @Transactional(
            rollbackFor = { Exception.class }
    )
    public boolean deleteLogic(@NotEmpty List<Long> ids) {
        // BladeUser user = AuthUtil.getUser();
        List<T> list = new ArrayList();

        ids.forEach((id) -> {
            T entity = BeanUtil.newInstance(this.currentModelClass());
            // if (user != null) {
            //     entity.setUpdateUser(user.getUserId());
            // }
            entity.setUpdateUser(BackendUtil.getInstance().getCurrentUserId());
            entity.setUpdateTime(new Date());
            entity.setId(id);

            list.add(entity);
        });

        return super.updateBatchById(list) && super.removeByIds(ids);
    }

    @Override
    public boolean changeStatus(List<Long> ids, Integer status) {
        return false;
    }
}
