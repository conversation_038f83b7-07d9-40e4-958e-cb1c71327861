package com.bdtd.modules.conf_distribution.service.impl;


import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.entity.ModbusMasterModel;
import com.bdtd.modules.conf_distribution.service.ModbusService;
import com.bdtd.modules.conf_distribution.util.ModbusUtil;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.code.DataType;
import com.serotonin.modbus4j.exception.ErrorResponseException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.serotonin.modbus4j.locator.BaseLocator;
import com.serotonin.modbus4j.msg.ReadHoldingRegistersRequest;
import com.serotonin.modbus4j.msg.ReadHoldingRegistersResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ModbusServiceImpl
 */
@Service
@Slf4j
public class ModbusServiceImpl implements ModbusService
{
    @Resource(name = "modbusClientMap")
    private ConcurrentHashMap<Long, ModbusClientConfig> modbusMasterStaticMap;

    @Override
    public boolean writeModbus(Integer masterBeltId, int slaveId, int writeOffset, int writeValue) throws Exception {
        /*
        // 创建请求对象
        WriteRegisterRequest request = new WriteRegisterRequest(slaveId, writeOffset, writeValue);
        // 发送请求并获取响应对象
        ModbusMasterModel modbusMasterModel = modbusMasterStaticMap.get(masterBeltId);
        if (modbusMasterModel != null) {
            ModbusMaster modbusMaster = modbusMasterModel.getModbusMaster();
            WriteRegisterResponse response = (WriteRegisterResponse) modbusMaster.send(request);
            return !response.isException();
        }
        else {
            return false;
        } */

        ModbusMasterModel modbusMasterModel = modbusMasterStaticMap.get(masterBeltId).getModbusMasterModel();
        BaseLocator<Number> locator = BaseLocator.holdingRegister(slaveId, writeOffset, DataType.TWO_BYTE_INT_UNSIGNED);
        ModbusMaster modbusMaster = modbusMasterModel.getModbusMaster();
        modbusMaster.setValue(locator, writeValue);
        return true;
    }

    @Override
    public boolean writeModbus(ModbusMasterModel modbusMasterModel, int slaveId, int writeOffset, int writeValue)
        throws Exception {
        BaseLocator<Number> locator = BaseLocator.holdingRegister(slaveId, writeOffset, DataType.TWO_BYTE_INT_UNSIGNED);
        ModbusMaster modbusMaster = modbusMasterModel.getModbusMaster();
        modbusMaster.setValue(locator, writeValue);
        return true;
    }

    @Override
    public boolean writeModbus(ModbusClientConfig clientConfig, int slaveId, int writeOffset, int writeValue)
        throws ErrorResponseException, ModbusTransportException {
        ModbusMasterModel modbusMasterModel = clientConfig.getModbusMasterModel();
        if (modbusMasterModel == null || modbusMasterModel.getModbusMaster() == null) {
            clientConfig.setModbusMasterModel(new ModbusMasterModel(
                clientConfig.getIp(),
                clientConfig.getPort(),
                ModbusUtil.getModbusMaster(
                    clientConfig.getIp(),
                    clientConfig.getPort()
                )
            ));
            return false;
        }

        BaseLocator<Number> locator = BaseLocator.holdingRegister(slaveId, writeOffset, DataType.TWO_BYTE_INT_UNSIGNED);
        ModbusMaster modbusMaster = modbusMasterModel.getModbusMaster();
        modbusMaster.setValue(locator, writeValue);
        return true;
    }

    @Override
    public Double readModbus(ModbusClientConfig clientConfig, int slaveId, int writeOffset)
        throws ErrorResponseException, ModbusTransportException {
        Double value = null;
        ModbusMasterModel modbusMasterModel = clientConfig.getModbusMasterModel();
        if (modbusMasterModel == null || modbusMasterModel.getModbusMaster() == null) {
            clientConfig.setModbusMasterModel(new ModbusMasterModel(
                clientConfig.getIp(),
                clientConfig.getPort(),
                ModbusUtil.getModbusMaster(
                    clientConfig.getIp(),
                    clientConfig.getPort()
                )
            ));
            return null;
        }

        ModbusMaster master = modbusMasterModel.getModbusMaster();
        BaseLocator<Number> loc = BaseLocator.holdingRegister(1, writeOffset, DataType.TWO_BYTE_INT_UNSIGNED);
        value = master.getValue(loc).doubleValue();
        return value;
    }

    @Override
    public Double readModbus(Integer masterBeltId, int offset) throws Exception {

        Double value = null;
        ModbusMasterModel modbusMasterModel = modbusMasterStaticMap.get(masterBeltId).getModbusMasterModel();
        if (modbusMasterModel == null || modbusMasterModel.getModbusMaster() == null) {
            log.error(String.format("mastetBelt：[%s] connect modbus is fail,modbusMaster  is null！", masterBeltId));
            return value;
        }
        ModbusMaster master = modbusMasterModel.getModbusMaster();
        BaseLocator<Number> loc = BaseLocator.holdingRegister(1, offset, DataType.TWO_BYTE_INT_UNSIGNED);
        value = master.getValue(loc).doubleValue();
        return value;
    }

    @Override
    public List<Double> readSerialMoudlebus(ModbusMasterModel modbusMasterModel, int slaveId, int start, int len)
        throws Exception {
        List<Double> listDouble = new ArrayList<>();
        ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(slaveId, start, len);
        ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) modbusMasterModel.getModbusMaster().send(
            request);
        if (!response.isException()) {
            // System.out.println(Arrays.toString(response.getShortData()));
            short[] list = response.getShortData();
            for (short l : list
            ) {
                listDouble.add(Double.valueOf(l));
            }
        }
        // else {
        //     System.out.println("异常消息:" + response.getExceptionMessage());
        // }
        return listDouble;
    }
}
