/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO;

/**
 * 组态下发日志 服务类
 */
public interface ConfDistributionLogService extends BaseService<ConfDistributionLog>
{

    /**
     * 自定义分页
     *
     * @param page
     * @param confDistributionLog
     * @return
     */
    IPage<ConfDistributionLogVO> selectConfDistributionLogPage(IPage<ConfDistributionLogVO> page, ConfDistributionLogVO confDistributionLog);

    /**
     * 方法描述: 下发日志 分页查询
     *
     * @param page
     * @param vo
     * @throws
     * @Return {@link IPage< ConfDistributionLog>}
     * <AUTHOR>
    IPage<ConfDistributionLogVO> myPage(IPage<ConfDistributionLogVO> page, ConfDistributionLogVO vo);
}
