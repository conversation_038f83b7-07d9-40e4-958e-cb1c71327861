package com.bdtd.modules.conf_distribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.conf_distribution.dto.ConfMeasurePointDTO;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;

/**
 * 测点
 *
 * <AUTHOR>
 */
public interface ConfMeasurePointService extends IService<ConfMeasurePoint>
{

    /**
     * 方法描述: 新增或修改
     *
     * @param dto 测点实体
     * @return {@link boolean}
     */
    boolean submit(ConfMeasurePointDTO dto) throws Exception;

    /**
     * 根据测点ID查询
     *
     * @param pointId 测点ID
     * @return /
     */
    ConfMeasurePoint findByOutPointId(String pointId);

    /**
     * 分页查询白名单列表
     *
     * @param page                分页参数
     * @param confMeasurePointDTO 查询参数
     * @return /
     */
    IPage<ConfMeasurePoint> selectWhitePointPages(IPage<ConfMeasurePoint> page, ConfMeasurePointDTO confMeasurePointDTO);

}
