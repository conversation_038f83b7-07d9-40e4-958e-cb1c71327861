package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.conf_distribution.entity.ControlSource;
import com.bdtd.modules.conf_distribution.mapper.AccessLinkTroubleMapper;
import com.bdtd.modules.conf_distribution.service.IAccessLinkTroubleService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 控制源管理  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
public class AccessLinkTroubleImpl extends ServiceImpl<AccessLinkTroubleMapper, ControlSource> implements IAccessLinkTroubleService {

    @Resource
    private AccessLinkTroubleMapper accessLinkTroubleMapper;

    @Autowired
    private IAccessLinkTroubleService controlSourceService;

    @Override
    public IPage<ControlSource> controlSourceList(Integer pageNum, Integer pageSize, String ip, Integer port, String userName) {
        Page<ControlSource> page = new Page<>();
        if (pageNum != null && pageSize != null) {
            page.setCurrent(pageNum);
            page.setSize(pageSize);
        }
        String portForLike = null;
        if(port != null){
            portForLike = String.valueOf(port);
        }

        IPage<ControlSource> iPage = accessLinkTroubleMapper.selectPage(page, ip, portForLike, userName);
        return iPage;
    }

    @Override
    public Msg controlSourceInsert(List<ControlSource> controlSource) {
        List<ControlSource> saveList = new ArrayList<>();

        for (ControlSource control : controlSource) {

            QueryWrapper<ControlSource> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq("ip",control.getIp());
            queryWrapper.eq("port",control.getPort());

            if(accessLinkTroubleMapper.selectCount(queryWrapper) > 0){
                return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", "ip和端口不能重复");
            }
            saveList.add(control);
        }
        if(controlSourceService.saveOrUpdateBatch(saveList)){
            return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", "添加成功");
        }else{
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", "添加失败");
        }
    }

    @Override
    public Msg controlSourceUpdate(ControlSource controlSource) {

        if(controlSourceService.updateById(controlSource)){
            return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", "修改成功");
        }else{
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", "修改失败");
        }
    }

    @Override
    public Msg controlSourceDelete(List<Integer> ids) {

        if(controlSourceService.removeByIds(ids)){
            return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", "删除成功");
        }else{
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", "删除失败");
        }
    }

}
