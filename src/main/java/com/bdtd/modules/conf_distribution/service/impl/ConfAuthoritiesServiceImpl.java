/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bdtd.modules.automation.entity.DataAutomationDefinition;
import com.bdtd.modules.automation.service.IDataAutomationDefinitionService;
import com.bdtd.modules.base.entity.User;
import com.bdtd.modules.base.entity.UserInfo;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.*;
import com.bdtd.modules.conf_distribution.entity.ConfAuthorities;
import com.bdtd.modules.conf_distribution.entity.ConfBlackList;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.mapper.ConfAuthoritiesMapper;
import com.bdtd.modules.conf_distribution.mapper.ConfDistributionLogMapper;
import com.bdtd.modules.conf_distribution.service.ConfAuthoritiesService;
import com.bdtd.modules.conf_distribution.service.ConfBlackListService;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.util.DigestUtil;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.SignUtil;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 测点权限
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConfAuthoritiesServiceImpl extends BaseServiceImpl<ConfAuthoritiesMapper, ConfAuthorities> implements ConfAuthoritiesService {
    @Resource
    IDataAutomationDefinitionService dataAutomationDefinitionService;
    @Resource
    ConfMeasurePointService confMeasurePointService;
    @Resource
    ConfBlackListService confBlackListService;
    @Resource
    private ConfDistributionLogMapper confDistributionLogMapper;

    @Resource
    ExecutorService executorPool;

    /** 控制命令单点下发地址 */
    @Value("${point.single.url}")
    private String pointSingleUrl;

    /** 控制命令多点下发地址 */
    @Value("${point.multi.url}")
    private String pointMultiUrl;

    @Value("${point.allowed.tokens}")
    private String[] pointAllowedTokens;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitConfAuthorities(ConfAuthoritiesSaveDto saveDTO) {
        LambdaQueryWrapper<ConfAuthorities> confAuthoritiesLambdaQueryWrapper = new LambdaQueryWrapper<>();
        confAuthoritiesLambdaQueryWrapper.eq(ConfAuthorities::getPointId, saveDTO.getPointId());

        // 移除既存点位、用户权限
        this.remove(confAuthoritiesLambdaQueryWrapper);

        ConfAuthorities data = new ConfAuthorities();
        data.setPointId(saveDTO.getPointId());
        data.setUserIds(FuncUtil.join(saveDTO.getUserIds()));
        data.setIsDeleted(0);

        // 保存点位、用户权限关系
        return this.saveOrUpdate(data);
    }

    @Override
    public List<ConfAuthorities> findByPointIdAndUserId(Long pointId, Long userId) {
        LambdaQueryWrapper<ConfAuthorities> confAuthoritiesQueryWrapper = new LambdaQueryWrapper<>();
        confAuthoritiesQueryWrapper.like(null != userId, ConfAuthorities::getUserIds, userId);
        confAuthoritiesQueryWrapper.eq(null != pointId, ConfAuthorities::getPointId, pointId);

        return this.list(confAuthoritiesQueryWrapper);
    }

    @Override
    public String invoke(ConfDistDto singleDto, Map<String, String> headersMap, Long clickStartTimestamp) {
        // 业务处理开始时间
        Long businessStartTimestamp = System.currentTimeMillis();

        // 鉴权信息
        ConfDistAuthDto authInfo = ConfDistAuthDto
                .builder()
                .userName(singleDto.getUserName())
                .password(singleDto.getPassword())
                .pointSecret(singleDto.getPointSecret())
                .apiToken(singleDto.getApiToken())
                .pointIdList(Collections.singletonList(singleDto.getPointId()))
                .build();

        // 下发操作用户ID
        String errMsg = null;
        User operator = null;

        try {
            operator = validateDistInfo(authInfo);
        }
        catch (BadRequestException bre) {
            errMsg = bre.getMessage();
        }
        catch (Exception ex) {
            log.warn("request distribution info validation error: " + ex.getMessage(), ex);
            errMsg = "下发信息验证错误";
        }

        if (StringUtil.isNotEmpty(errMsg)) {
            return errMsg;
        }

        if (operator == null) {
            return "未提供有效的操作用户鉴权信息";
        }

        // 单点下发
        return processSingleDistribution(singleDto, operator, clickStartTimestamp, businessStartTimestamp);
    }

    @Override
    public String multiInvoke(ConfDistBulkDto bulkDto, Map<String, String> headersMap, Long clickStartTimestamp) {
        // 业务处理开始时间
        Long businessStartTimestamp = System.currentTimeMillis();

        // 鉴权信息
        ConfDistAuthDto authInfo = ConfDistAuthDto
                .builder()
                .userName(bulkDto.getUserName())
                .password(bulkDto.getPassword())
                .apiToken(bulkDto.getApiToken())
                .pointIdList(bulkDto.getDistValues().stream().map(ConfDistItemDto::getPointId).collect(Collectors.toList()))
                .build();

        // 下发操作用户ID
        String errMsg = null;
        User operator = null;

        try {
            operator = validateDistInfo(authInfo);
        }
        catch (BadRequestException bre) {
            errMsg = bre.getMessage();
        }
        catch (Exception ex) {
            log.warn("request distribution info validation error: " + ex.getMessage(), ex);
            errMsg = "下发信息验证错误";
        }

        if (StringUtil.isNotEmpty(errMsg)) {
            return errMsg;
        }

        if (operator == null) {
            return "未提供有效的操作用户鉴权信息";
        }

        // 多点下发
        return processMultipleDistribution(bulkDto, operator, clickStartTimestamp, businessStartTimestamp);
    }

    /**
     * 下发验证信息
     *
     * @param authInfo 下发验证信息
     * @return /
     */
    private User validateDistInfo(ConfDistAuthDto authInfo) throws BadRequestException {
        // 当前日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String todayDate = sdf.format(new Date());

        List<String> errMsgList = new ArrayList<>();

        // 验证测点信息
        List<ConfMeasurePoint> measurePointList = new ArrayList<>();
        if (authInfo.getPointIdList() != null && !authInfo.getPointIdList().isEmpty()) {
            for (String pointId : authInfo.getPointIdList()) {
                // 点位信息检查
                DataAutomationDefinition point = dataAutomationDefinitionService.getDetail(pointId, null);
                if (FuncUtil.isEmpty(point)) {
                    errMsgList.add("测点 " + pointId + " 不存在或者已被删除");
                    continue;
                }

                // 控制点位检查
                ConfMeasurePoint measurePoint = confMeasurePointService.findByOutPointId(pointId);
                if (FuncUtil.isEmpty(measurePoint)) {
                    errMsgList.add("测点 " + pointId + " 未配置或者不是控制点位");
                    continue;
                }
                else {
                    measurePointList.add(measurePoint);
                }

                // 测点黑名单检查
                LambdaQueryWrapper<ConfBlackList> blackListQueryWrapper = new LambdaQueryWrapper<>();
                blackListQueryWrapper.eq(ConfBlackList::getPointId, measurePoint.getId());

                List<ConfBlackList> confBlackLists = confBlackListService.list(blackListQueryWrapper);
                if (!FuncUtil.isEmpty(confBlackLists)) {
                    errMsgList.add("测点 " + pointId + " 不允许操作");
                }
            }
        }

        // 控制权限检查
        for (ConfMeasurePoint measurePoint : measurePointList) {
            LambdaQueryWrapper<ConfAuthorities> confAuthQueryWrapper = new LambdaQueryWrapper<>();
            confAuthQueryWrapper.like(null != authInfo.getUserName(), ConfAuthorities::getUserIds, authInfo.getUserName());
            confAuthQueryWrapper.eq(ConfAuthorities::getPointId, measurePoint.getId());

            List<ConfAuthorities> confAuthorityList = this.list(confAuthQueryWrapper);
            if (FuncUtil.isEmpty(confAuthorityList)) {
                errMsgList.add("用户没有点位 " + measurePoint.getOutPointId() + " 的控制权限");
            }
        }

        if (!errMsgList.isEmpty()) {
            throw new BadRequestException(String.join("\r\n", errMsgList));
        }

        // 下发操作用户ID
        User operator = null;
        // 后管用户信息
        UserInfo userInfo = null;

        // 通过点位控制密钥鉴权, 操作用户鉴权信息通过 header 中的 token 获取
        if (!FuncUtil.isEmpty(authInfo.getPointSecret())) {
            // 取得当前用户ID
            try {
                Long userId = BackendUtil.getInstance().getCurrentUserId();
                operator = BackendUtil.getInstance().getUserById(userId);
            }
            catch (Exception e) {
                log.warn("get operator info from header failed:" + e.getMessage(), e);
            }

            if (operator == null) {
                throw new BadRequestException("未提供操作用户鉴权信息");
            }

            // TODO: 点位控制密钥验证
            // ---
            if (authInfo.getPointIdList() != null && !authInfo.getPointIdList().isEmpty()) {
                String pointId = authInfo.getPointIdList().get(0);
                ConfMeasurePoint outPoint = confMeasurePointService.findByOutPointId(pointId);
                if (null != outPoint) {
                    if (!authInfo.getPointSecret().equals(outPoint.getPointSecret())) {
                        throw new BadRequestException("测点 " + pointId + " 点位控制密钥验证有误");
                    }
                }
                else {
                    throw new BadRequestException("测点 " + pointId + " 未配置或者不是控制点位");
                }

            }


        }
        // 通过接口令牌鉴权，操作用户鉴权信息通过用户名和密码获取
        else if (!FuncUtil.isEmpty(authInfo.getApiToken())) {
            // 未提供操作用户鉴权信息
            if (FuncUtil.isEmpty(authInfo.getUserName()) || FuncUtil.isEmpty(authInfo.getPassword())) {
                throw new BadRequestException("未提供操作用户鉴权信息");
            }

            // 后管获取用户鉴权信息
            userInfo = BackendUtil.getInstance()
                                  .getUserInfo("000000", authInfo.getUserName(), DigestUtil.hex(authInfo.getPassword()));
            if (userInfo == null || userInfo.getUser() == null) {
                log.warn(
                        String.format(
                                "backend user validation failed, userName: %s, password: %s, hexed password: %s",
                                authInfo.getUserName(),
                                authInfo.getPassword(),
                                DigestUtil.hex(authInfo.getPassword())
                        ));
                throw new BadRequestException("操作用户 " + authInfo.getUserName() + " 不存在或者密码不正确");
            }

            operator = userInfo.getUser();

            // 接口令牌无效
            boolean isValidToken = Arrays.stream(pointAllowedTokens)
                                         .map(t -> SignUtil.md5(t.toLowerCase().trim() + todayDate))
                                         .anyMatch(p -> p.equals(authInfo.getApiToken()));
            if (!isValidToken) {
                throw new BadRequestException("下发令牌无效");
            }
        }
        // 通过后管用户鉴权，操作用户鉴权信息通过用户名和密码获取
        else {
            // 未提供操作用户鉴权信息
            if (FuncUtil.isEmpty(authInfo.getUserName()) || FuncUtil.isEmpty(authInfo.getPassword())) {
                throw new BadRequestException("未提供用户鉴权信息");
            }

            userInfo = BackendUtil.getInstance()
                                  .getUserInfo("000000", authInfo.getUserName(), DigestUtil.hex(authInfo.getPassword()));
            if (userInfo == null || userInfo.getUser() == null) {
                log.warn(
                        String.format(
                                "backend user validation failed, userName: %s, password: %s, hexed password: %s",
                                authInfo.getUserName(),
                                authInfo.getPassword(),
                                DigestUtil.hex(authInfo.getPassword())
                        ));
                throw new BadRequestException("操作用户鉴权失败");
            }

            operator = userInfo.getUser();
        }

        return operator;
    }

    /**
     * 处理单点下发
     *
     * @param distDto                下发信息
     * @param user                   操作用户
     * @param clickStartTimestamp    下发时间点
     * @param businessStartTimestamp 业务处理时间点
     * @return 成功返回null, 失败返回错误信息
     */
    private String processSingleDistribution(ConfDistDto distDto, User user, Long clickStartTimestamp, Long businessStartTimestamp) {
        // 下发值信息
        ConfDistValueDto valueDto = new ConfDistValueDto();
        valueDto.setPointId(distDto.getPointId());
        valueDto.setValue(distDto.getValue());

        // 延时重置下发
        if (FuncUtil.isNotEmpty(distDto.getDelayState()) && distDto.getDelayState() == 1) {
            // 控制下发
            singleDistribution(user, valueDto, clickStartTimestamp, businessStartTimestamp);

            // 重置值信息
            ConfDistValueDto resetDto = new ConfDistValueDto();
            resetDto.setPointId(distDto.getPointId());
            resetDto.setValue(distDto.getDelayValue());

            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(distDto.getDelayTime());

                    // return (new SingleDistributionTask(pointSingleUrl, userId, resetDto, bulkDistDto.getDelayTime())).call();
                    singleDistribution(user, resetDto, clickStartTimestamp, businessStartTimestamp);
                }
                catch (Exception iex) {
                    log.warn("delay distribution error: " + iex.getMessage(), iex);
                }
            }, executorPool);

            return null;
        }
        else if (FuncUtil.isNotEmpty(distDto.getPulseState()) && distDto.getPulseState() == 1) {
            // 是脉冲调用
            Boolean firstIsSuccess = singleDistribution(user, valueDto, clickStartTimestamp, businessStartTimestamp);

            try {
                // 调用时间间隔
                // TODO: 性能优化点，备忘
                Thread.sleep(distDto.getElapsedTime());
            }
            catch (Exception e) {
                // 线程意外唤醒
                log.error("distribution error occurs when thread sleeping: " + e.getMessage(), e);
            }

            // 重置值信息
            ConfDistValueDto resetDto = new ConfDistValueDto();
            resetDto.setPointId(distDto.getPointId());
            resetDto.setValue(distDto.getResetValue());

            // 复位
            Boolean resetSuccess = singleDistribution(user, resetDto, clickStartTimestamp, businessStartTimestamp);

            if (firstIsSuccess && resetSuccess) {
                return null;
            }
            else {
                return "控制命令下发失败";
            }
        }
        else {
            // 一般控制命令下发
            Boolean isSuccess = singleDistribution(user, valueDto, clickStartTimestamp, businessStartTimestamp);
            if (isSuccess) {
                return null;
            }
            else {
                return "控制命令下发失败";
            }
        }
    }

    /**
     * 处理多点下发
     *
     * @param bulkDistDto            下发信息列表
     * @param user                   操作用户
     * @param clickStartTimestamp    下发时间点
     * @param businessStartTimestamp 业务处理时间点
     * @return 成功返回null, 失败返回错误信息
     */
    private String processMultipleDistribution(ConfDistBulkDto bulkDistDto, User user, Long clickStartTimestamp, Long businessStartTimestamp) {
        // 下发控制值列表
        List<ConfDistValueDto> distList = bulkDistDto
                .getDistValues()
                .stream()
                .filter(c -> c.getValue() != null)
                .map(c -> new ConfDistValueDto(c.getPointId(), c.getValue()))
                .collect(Collectors.toList());

        // 延时重置下发
        if (FuncUtil.isNotEmpty(bulkDistDto.getDelayState()) && bulkDistDto.getDelayState() == 1) {
            // 控制下发
            bulkDistribution(user, distList, clickStartTimestamp, businessStartTimestamp);

            // 重置值列表
            List<ConfDistValueDto> resetList = bulkDistDto
                    .getDistValues()
                    .stream()
                    .filter(c -> c.getDelayValue() != null)
                    .map(c -> new ConfDistValueDto(c.getPointId(), c.getDelayValue()))
                    .collect(Collectors.toList());

            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(bulkDistDto.getDelayTime());

                    // return (new BulkDistributionTask(pointMultiUrl, userId, resetList, bulkDistDto.getDelayTime())).call();
                    bulkDistribution(user, resetList, clickStartTimestamp, businessStartTimestamp);
                }
                catch (Exception iex) {
                    log.warn("delay distribution error: " + iex.getMessage(), iex);
                }
            }, executorPool);

            return null;
        }
        else if (FuncUtil.isNotEmpty(bulkDistDto.getPulseState()) && bulkDistDto.getPulseState() == 1) {
            // 是脉冲调用
            Boolean firstIsSuccess = bulkDistribution(user, distList, clickStartTimestamp, businessStartTimestamp);

            try {
                // 调用时间间隔
                // TODO: 性能优化点，备忘
                Thread.sleep(bulkDistDto.getElapsedTime());
            }
            catch (Exception e) {
                // 线程意外唤醒
                log.error("distribution error occurs when thread sleeping: " + e.getMessage(), e);
            }

            // 重置值列表
            List<ConfDistValueDto> resetList = bulkDistDto
                    .getDistValues()
                    .stream()
                    .filter(c -> c.getResetValue() != null)
                    .map(c -> new ConfDistValueDto(c.getPointId(), c.getResetValue()))
                    .collect(Collectors.toList());

            // 复位
            Boolean resetSuccess = bulkDistribution(user, resetList, clickStartTimestamp, businessStartTimestamp);

            if (firstIsSuccess && resetSuccess) {
                return null;
            }
            else {
                return "控制命令下发失败";
            }
        }
        else {
            // 一般控制命令下发
            Boolean isSuccess = bulkDistribution(user, distList, clickStartTimestamp, businessStartTimestamp);
            if (isSuccess) {
                return null;
            }
            else {
                return "控制命令下发失败";
            }
        }

    }

    /**
     * 单点位下发
     *
     * @param user                   用户
     * @param distValue              下发值信息
     * @param clickStartTimestamp    下发触发时间
     * @param businessStartTimestamp 业务处理开始时间
     * @return /
     */
    private Boolean singleDistribution(User user, ConfDistValueDto distValue, Long clickStartTimestamp, Long businessStartTimestamp) {
        long invokeStartTimestamp = 0;
        String result = "下发准备失败";

        // 建立HttpPost对象
        HttpPost httppost = new HttpPost(pointSingleUrl);
        // 创建HttpClient对象
        CloseableHttpClient client = HttpClients.createDefault();

        try {
            // 请求参数
            List<BasicNameValuePair> paramsList = new ArrayList<>();
            paramsList.add(
                    new BasicNameValuePair(
                            "point_id",
                            distValue.getPointId().contains(".") ? distValue.getPointId().substring(7) : distValue.getPointId()
                    )
            );
            paramsList.add(new BasicNameValuePair("value", distValue.getValue()));

            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramsList, "UTF-8");
            httppost.setEntity(entity);

            // 设置请求和传输时长
            RequestConfig.Builder builder = RequestConfig.custom();
            builder.setSocketTimeout(5000);
            builder.setConnectTimeout(5000);

            RequestConfig config = builder.build();
            httppost.setConfig(config);

            invokeStartTimestamp = System.currentTimeMillis();

            // 发送 Post
            CloseableHttpResponse response = client.execute(httppost);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                result = EntityUtils.toString(httpEntity, "UTF-8");
            }

            result = "下发成功";
            return true;
        }
        catch (Exception e) {
            result = "下发失败: " + e.getMessage();
            log.error("singleIssue error: " + e.getMessage(), e);
            return false;
        }
        finally {
            try {
                saveLog(result, user, distValue, clickStartTimestamp, businessStartTimestamp, invokeStartTimestamp);
            }
            catch (Exception e) {
                log.error("save single distribution log error: " + e.getMessage(), e);
            }

            try {
                client.close();
                httppost.releaseConnection();
            }
            catch (Exception e) {
                log.error("close single distribution client error: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 多单点位下发
     *
     * @param user                   用户
     * @param confDistDtoList        下发值信息列表
     * @param clickStartTimestamp    下发触发时间
     * @param businessStartTimestamp 业务处理开始时间
     * @return /
     */
    private Boolean bulkDistribution(User user, List<ConfDistValueDto> confDistDtoList, Long clickStartTimestamp, Long businessStartTimestamp) {
        // 下发值列表检查
        if (confDistDtoList == null || confDistDtoList.size() == 0) {
            return false;
        }

        long invokeStartTimestamp = 0;
        String result = "下发准备失败";

        String url = pointMultiUrl;

        // 建立HttpPost对象
        HttpPost httppost = new HttpPost(url);
        // 创建HttpClient对象
        CloseableHttpClient client = HttpClients.createDefault();

        try {
            // 添加参数
            JSONArray jsonArray = new JSONArray();
            for (ConfDistValueDto confDistDto : confDistDtoList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("point_id", confDistDto.getPointId().contains(".") ? confDistDto.getPointId().substring(7) : confDistDto.getPointId());
                jsonObject.put("value", confDistDto.getValue());

                jsonArray.add(jsonObject);
            }

            StringEntity entity = new StringEntity(String.valueOf(jsonArray), "utf-8");
            httppost.setEntity(entity);

            // 设置请求和传输时长
            RequestConfig.Builder builder = RequestConfig.custom();
            builder.setSocketTimeout(5000);
            builder.setConnectTimeout(5000);
            RequestConfig config = builder.build();
            httppost.setConfig(config);

            httppost.setHeader("Content-type", "application/json");

            invokeStartTimestamp = System.currentTimeMillis();

            // 发送 Post
            CloseableHttpResponse response = client.execute(httppost);
            HttpEntity httpEntity = response.getEntity();
            if (httpEntity != null) {
                result = EntityUtils.toString(httpEntity, "UTF-8");
            }

            result = "下发成功";
            return true;
        }
        catch (Exception e) {
            result = "下发失败: " + e.getMessage();
            log.error("bulkDistribution error: " + e.getMessage(), e);
            return false;
        }
        finally {
            try {
                for (ConfDistValueDto confDistDto : confDistDtoList) {
                    saveLog(result, user, confDistDto, clickStartTimestamp, businessStartTimestamp, invokeStartTimestamp);
                }
            }
            catch (Exception e) {
                log.error("save bulk distribution log error: " + e.getMessage(), e);
            }

            try {
                client.close();
                httppost.releaseConnection();
            }
            catch (Exception e) {
                log.error("close bulk distribution client error: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 记录控制下发日志
     *
     * @param result                 下发结果
     * @param usr                    操作用户
     * @param distValueDto           下发值信息
     * @param clickStartTimestamp    点击开始时间
     * @param businessStartTimestamp 业务开始时间
     * @param invokeStartTimestamp   触发开始时间
     */
    private void saveLog(
            String result,
            User usr,
            ConfDistValueDto distValueDto,
            Long clickStartTimestamp,
            Long businessStartTimestamp,
            Long invokeStartTimestamp
    )
    {
        // 获取时间（调用完成的时间）
        long endTimeStamp = System.currentTimeMillis();

        // 记录日志到日志表
        ConfDistributionLog logEntity = new ConfDistributionLog();

        // 点位ID
        logEntity.setPointId(distValueDto.getPointId());
        // 下发值
        logEntity.setValue(distValueDto.getValue());
        // 是否调用成功
        logEntity.setActionResult(FuncUtil.isNotEmpty(result) ? 1 : 0);

        // 点击时间戳
        logEntity.setStartTimeStamp(new Date(clickStartTimestamp));
        // 结束的完成时间
        logEntity.setEndTimeStamp(new Date(endTimeStamp));
        // 总耗时
        logEntity.setElapsedTime(endTimeStamp - clickStartTimestamp);
        // 请求点击-请求到达延迟
        logEntity.setClickReachElapsed(businessStartTimestamp - clickStartTimestamp);
        // 业务处理延时
        Long businessElapsed = null;
        if (logEntity.getElapsedTime() != null) {
            businessElapsed = logEntity.getElapsedTime();
            if (logEntity.getClickReachElapsed() != null) {
                businessElapsed = logEntity.getElapsedTime() - logEntity.getClickReachElapsed();
            }
            if (logEntity.getInvokeIssueElapsed() != null) {
                businessElapsed = logEntity.getElapsedTime() - logEntity.getInvokeIssueElapsed();
            }
        }
        logEntity.setBusinessElapsed(businessElapsed);
        // 调用下发-下发执行完毕延迟
        logEntity.setInvokeIssueElapsed(invokeStartTimestamp == null ? null : endTimeStamp - invokeStartTimestamp);

        // 添加 logEntity 的用户相关信息
        if (usr != null) {
            logEntity.setCreateUser(usr.getId());
            logEntity.setUpdateUser(usr.getId());

            logEntity.setTenantId(usr.getTenantId());
            logEntity.setIsDeleted(0);
            logEntity.setStatus(1);
            logEntity.setCreateDept(-1L);
            logEntity.setUserName(usr.getAccount());
            logEntity.setOperator(usr.getName());

            Date now = new Date();
            logEntity.setCreateTime(now);
            logEntity.setUpdateTime(now);
        }

        confDistributionLogMapper.insert(logEntity);
    }

}
