/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.vo.ModbusClientConfigVO;
import com.bdtd.util.web.Msg;

/**
 * 服务类
 */
public interface IModbusClientConfigService extends IService<ModbusClientConfig>
{

    /**
     * 自定义分页
     *
     * @param page
     * @param modbusClientConfig
     * @return
     */
    IPage<ModbusClientConfigVO> selectModbusClientConfigPage(
            IPage<ModbusClientConfigVO> page,
            ModbusClientConfigVO modbusClientConfig
    );

    /**
     * 新增并加入内存
     *
     * @param modbusClientConfig
     * @return
     */
    Boolean add(ModbusClientConfig modbusClientConfig);

    /**
     * 只更新内存
     *
     * @param modbusClientConfig
     * @return
     */
    Msg myUpdateById(ModbusClientConfig modbusClientConfig);

    /**
     * 分页查询（查询内存的ConcurrentHashMap）
     *
     * @param page
     * @param modbusClientConfig
     * @return
     */
    IPage<ModbusClientConfig> myPage(IPage<ModbusClientConfig> page, ModbusClientConfig modbusClientConfig);

    /**
     * 详情获取
     *
     * @param modbusClientConfig
     * @return
     */
    ModbusClientConfig getMyOne(ModbusClientConfig modbusClientConfig);

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    Msg myRemove(String ids);

    /**
     * 持久化（将内存中数据，写入到数据库）
     */
    void writeDb();

    /**
     * 方法描述: 写心跳信息，刷新PLC和变频器状态
     *
     * @throws
     */
    void flushHeartbeatAndVfdStatus();
}
