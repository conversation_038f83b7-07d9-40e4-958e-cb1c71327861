package com.bdtd.modules.conf_distribution.service;

import com.bdtd.modules.conf_distribution.vo.ConfDistributionVo;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyTestVO;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyVO;
import com.bdtd.util.web.Msg;

import java.util.Map;

/**
 *  组态下发  Service
 */
public interface ConfDistributionService {
    /**
     * 方法描述:  根据传参，调用对应API；  并记录日志
     *
     *
     * @param paramVo

     * @param headersMap 请求头信息
     * @Return {@link Msg< Map< String, Object>>}
     * @throws
     *
     */
    Msg invokeAndLog(ConfDistributionVo paramVo, Map<String, String> headersMap);

    /**
     * 调速控制
     * @param address
     * @param value
     * @return
     */
    Boolean controlFrequencyOld(Integer address, Integer value);

    /**
     * 调速控制

     * @return
     */
    Msg controlFrequency(ControlFrequencyVO paramVo);

    /**
     * 组态下发 测试接口
     * @param paramVo
     * @return
     */
    Msg controlFrequencyTest(ControlFrequencyTestVO paramVo);
}
