package com.bdtd.modules.conf_distribution.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bdtd.modules.conf_distribution.entity.*;
import com.bdtd.modules.conf_distribution.feign.ConfDistributionFeignClient;
import com.bdtd.modules.conf_distribution.service.*;
import com.bdtd.modules.conf_distribution.util.ModbusUtil;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionVo;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyTestVO;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyVO;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import feign.Feign;
import feign.Request;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 组态下发  ServiceImpl
 */
@Slf4j
@Service
public class ConfDistributionServiceImpl implements ConfDistributionService
{

    @Resource
    private ConfDistributionLogService confDistributionLogService;

    @Resource
    private ModbusService modbusService;

    @Resource
    private IModbusClientConfigService modbusClientConfigService;

    @Resource
    private IModbusClientAddressService modbusClientAddressService;

    @Resource(name = "modbusClientMap")
    private ConcurrentHashMap<Long, ModbusClientConfig> modbusMasterStaticMap;

    @Resource
    private ICommandIssuedSchemeService commandIssuedSchemeService;

    /**
     * 方法描述:  根据传参，调用对应API；  并记录日志
     *
     * @param paramVo
     * @param headersMap 请求头信息
     * @throws
     * @Return {@link Msg< Map< String, Object>>}
     */
    @Override
    public Msg invokeAndLog(ConfDistributionVo paramVo, Map<String, String> headersMap) {
        long businessStartTimeStamp = System.currentTimeMillis() * 1000;
        if (paramVo.getTimestamp() == null) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "没有传入开始时间", null);
        }

        long clickStartTimeStamp = paramVo.getTimestamp();

        // 去除
        headersMap.remove("Content-Length");
        headersMap.remove("Content-Type");

        // 转Map（随后再封装）
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", paramVo.getId());
        paramMap.put("value", paramVo.getValue());
        paramMap.put("timestamp", paramVo.getTimestamp());
        paramMap.put("frequency", paramVo.getFrequency());

        // 发送请求
        ConfDistributionFeignClient feignClient = Feign
                .builder()
                .options(new Request.Options(60000, 600000))
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .requestInterceptor(template -> {
                    // 添加请求头（将请求头全部转过来）
                    for (Map.Entry<String, String> kv : headersMap.entrySet()) {
                        template.header(kv.getKey(), kv.getValue());
                    }
                })
                .target(ConfDistributionFeignClient.class, "http://*************:29000/api/metadata/tagpoint/set_tag_vlaue");

        long invokeStartTimeStamp = System.currentTimeMillis() * 1000;

        JSONObject result = null;
        try {
            // // 判断是get请求，还是post请求
            // if ("GET".equalsIgnoreCase(paramVo.getRequestMethod())) {
            //     result = feignClient.getSomething(new URI(paramVo.getUrl()), paramMap);
            // }
            // else {
            //     result = feignClient.postSomething(new URI(paramVo.getUrl()), paramMap);
            // }

            // 暂定post请求
            result = feignClient.postSomething(new URI("http://*************:29000/api/metadata/tagpoint/set_tag_vlaue"), paramMap);
        }
        catch (URISyntaxException e) {
            e.printStackTrace();
        }

        // 获取时间（调用完成的时间）
        long endTimeStamp = System.currentTimeMillis() * 1000;

        //记录日志到日志表
        ConfDistributionLog logEntity = new ConfDistributionLog();
        // logEntity.setAddress(paramVo.getPoint());
        // logEntity.setAction(paramVo.getAction());
        // 是否调用成功
        logEntity.setActionResult(FuncUtil.isNotEmpty(result) ? 1 : 0);
        // 总耗时
        logEntity.setElapsedTime(endTimeStamp - clickStartTimeStamp);
        logEntity.setEndTimeStamp(new Date(endTimeStamp));
        // 点击时间戳
        logEntity.setStartTimeStamp(new Date(clickStartTimeStamp));
        // 请求点击-请求到达延迟
        logEntity.setClickReachElapsed(businessStartTimeStamp - clickStartTimeStamp);
        // 调用下发-下发执行完毕延迟
        logEntity.setInvokeIssueElapsed(endTimeStamp - invokeStartTimeStamp);
        // 业务处理延时
        logEntity.setBusinessElapsed(logEntity.getElapsedTime() - logEntity.getClickReachElapsed() - logEntity.getInvokeIssueElapsed());
        logEntity.setPointId(paramVo.getId());
        logEntity.setValue(String.valueOf(paramVo.getValue()));
        logEntity.setFrequency(paramVo.getFrequency());

        confDistributionLogService.save(logEntity);
        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "调用成功", result);
    }

    /**
     * 调速控制
     *
     * @param address
     * @param value
     * @return
     */
    @Override
    public Boolean controlFrequencyOld(Integer address, Integer value) {
        boolean result = Boolean.FALSE;
        try {
            result = modbusService.writeModbus(address, 1, address, value);
        }
        catch (Exception e) {
            log.error("PLC数据写入错误::address---->" + address + "::value---->" + value, e);
        }
        return result;
    }

    @Override
    public Msg controlFrequency(ControlFrequencyVO paramVo) {
        if (paramVo.getStartTimeStamp() == null) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "请传入开始点击的时间", null);
        }
        Long startTimeStamp = paramVo.getStartTimeStamp().getTime();
        if (paramVo.getAddressCode() == null) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "请传入addressCode", null);
        }
        List<ModbusClientAddress> list = modbusClientAddressService.list(Wrappers.<ModbusClientAddress>query().lambda()
                                                                                 .eq(ModbusClientAddress::getCode, paramVo.getAddressCode())
                                                                                 .eq(ModbusClientAddress::getSchemeCode, paramVo.getSchemeCode()));
        if (CollectionUtils.isEmpty(list)) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "未找到对应的点位配置", null);
        }
        ModbusClientAddress addressEntity = list.get(0);
        if (Objects.isNull(addressEntity)) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "未找到对应的点位配置", null);
        }
        ModbusClientConfig clientEntity = modbusMasterStaticMap.get(addressEntity.getClientId());
        //如果PLC或者变频器状态异常，则直接记录错误日志
        if (clientEntity.getPlcCommuStatus() == null || clientEntity.getPlcCommuStatus() == 0 || clientEntity.getVfdCommuStatus() == null || clientEntity.getVfdCommuStatus() == 0) {
            Long endTimeStamp = System.currentTimeMillis();
            writeLog(paramVo, startTimeStamp, endTimeStamp, clientEntity, addressEntity, 0);
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "客户端状态异常", null);
        }
        if (Objects.isNull(clientEntity)) {
            return ReturnMsg.resultMsg(StatusEnum.ERROR.value, "未找到对应的client配置", null);
        }
        boolean result = Boolean.FALSE;
        try {
            result = modbusService.writeModbus(clientEntity, addressEntity.getStationNum(), addressEntity.getAddress(), paramVo.getValue());
        }
        catch (Exception e) {
            log.error("PLC数据写入错误::address---->" + addressEntity.getAddress() + "::value---->" + paramVo.getValue(), e);
        }

        //获取时间（调用完成的时间）
        Long endTimeStamp = System.currentTimeMillis();

        //记录日志到日志表
        if (result) {
            writeLog(paramVo, startTimeStamp, endTimeStamp, clientEntity, addressEntity, 1);
        }
        else {
            writeLog(paramVo, startTimeStamp, endTimeStamp, clientEntity, addressEntity, 2);
        }
        return ReturnMsg.status(result);
    }

    /**
     * 组态下发 测试接口
     *
     * @param paramVo
     * @return
     */
    @Override
    public Msg controlFrequencyTest(ControlFrequencyTestVO paramVo) {
        ModbusMasterModel modbusMasterModel = new ModbusMasterModel(paramVo.getIp(), paramVo.getPort(), ModbusUtil.getModbusMaster(paramVo.getIp(), paramVo.getPort()));
        boolean result = false;
        //站号默认为1
        if (FuncUtil.isEmpty(paramVo.getStationNum())) {
            paramVo.setStationNum(1);
        }
        try {
            result = modbusService.writeModbus(modbusMasterModel, paramVo.getStationNum(), paramVo.getAddress(), paramVo.getValue());
        }
        catch (Exception e) {
            log.error("PLC数据写入错误::address---->" + paramVo.getAddress() + "::value---->" + paramVo.getValue(), e);
        }
        return ReturnMsg.status(result);
    }

    /**
     * 写入日志
     *
     * @param paramVo        参数
     * @param startTimeStamp 开始时间戳
     * @param endTimeStamp   结束时间戳
     * @param clientEntity   客户端
     * @param addressEntity  位置（点位）
     * @param actionResult   是否成功（操作结果）
     */
    private void writeLog(ControlFrequencyVO paramVo, Long startTimeStamp, Long endTimeStamp, ModbusClientConfig clientEntity, ModbusClientAddress addressEntity, Integer actionResult) {
        ConfDistributionLog logEntity = new ConfDistributionLog();
        logEntity.setStartTimeStamp(paramVo.getStartTimeStamp());
        logEntity.setEndTimeStamp(new Date(endTimeStamp));
        logEntity.setElapsedTime(endTimeStamp - startTimeStamp);
        logEntity.setActionResult(actionResult);
        logEntity.setIp(clientEntity.getIp());
        logEntity.setPort(clientEntity.getPort());
        logEntity.setAddress(addressEntity.getAddress());
        logEntity.setStationNum(addressEntity.getStationNum());
        logEntity.setSchemeId(clientEntity.getSchemeId());
        logEntity.setSchemeCode(clientEntity.getSchemeCode());
        logEntity.setClientId(clientEntity.getId());
        logEntity.setClientName(clientEntity.getName());

        // 查询方案名称
        if (clientEntity.getSchemeId() != null) {
            CommandIssuedScheme schemeEntity = commandIssuedSchemeService.getById(clientEntity.getSchemeId());
            if (schemeEntity != null) {
                logEntity.setSchemeName(schemeEntity.getName());
            }
        }
        confDistributionLogService.save(logEntity);
    }

}
