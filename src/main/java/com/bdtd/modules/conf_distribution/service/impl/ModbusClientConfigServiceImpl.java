/*
 *  Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.entity.ModbusMasterModel;
import com.bdtd.modules.conf_distribution.mapper.ModbusClientConfigMapper;
import com.bdtd.modules.conf_distribution.service.IModbusClientConfigService;
import com.bdtd.modules.conf_distribution.service.ModbusService;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.modules.conf_distribution.util.ModbusUtil;
import com.bdtd.modules.conf_distribution.vo.ModbusClientConfigVO;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.StringUtil;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModbusClientConfigServiceImpl extends BaseServiceImpl<ModbusClientConfigMapper, ModbusClientConfig> implements IModbusClientConfigService
{
    /**
     * modbus客户端静态Map
     */
    @Resource(name = "modbusClientMap")
    private ConcurrentHashMap<Long, ModbusClientConfig> modbusClientMap;

    @Resource
    private ModbusService modbusService;

    @Override
    public IPage<ModbusClientConfigVO> selectModbusClientConfigPage(IPage<ModbusClientConfigVO> page, ModbusClientConfigVO modbusClientConfig) {
        return page.setRecords(baseMapper.selectModbusClientConfigPage(page, modbusClientConfig));
    }

    /**
     * 新增并加入内存
     *
     * @param modbusClientConfig ModbusClient配置
     * @return /
     */
    @Override
    public Boolean add(ModbusClientConfig modbusClientConfig) {
        boolean flag = save(modbusClientConfig);
        if (!flag) {
            return false;
        }

        ModbusMasterModel modbusMasterModel = new ModbusMasterModel(
                modbusClientConfig.getIp(),
                modbusClientConfig.getPort(),
                ModbusUtil.getModbusMaster(modbusClientConfig.getIp(), modbusClientConfig.getPort())
        );

        modbusClientConfig.setModbusMasterModel(modbusMasterModel);
        modbusClientConfig.setPlcStatusUpdateTime(new Date());
        modbusClientConfig.setVfdStatusUpdateTime(new Date());

        modbusClientMap.put(modbusClientConfig.getId(), modbusClientConfig);

        return true;
    }

    /**
     * 只更新内存
     *
     * @param modbusClientConfig ModbusClient配置
     * @return /
     */
    @Override
    public Msg myUpdateById(ModbusClientConfig modbusClientConfig) {
        if (modbusClientConfig.getId() == null) {
            return ReturnMsg.resultMsg(StatusEnum.FAIL.value, "请传入id", null);
        }

        ModbusClientConfig client = modbusClientMap.get(modbusClientConfig.getId());
        if (client == null) {
            return ReturnMsg.resultMsg(StatusEnum.FAIL.value, "该客户端不存在，或者初始化失败", null);
        }

        if (modbusClientConfig.getSchemeId() != null) {
            client.setSchemeId(modbusClientConfig.getSchemeId());
        }
        if (modbusClientConfig.getSchemeCode() != null) {
            client.setSchemeCode(modbusClientConfig.getSchemeCode());
        }
        if (modbusClientConfig.getIp() != null) {
            client.setIp(modbusClientConfig.getIp());
        }
        if (modbusClientConfig.getPort() != null) {
            client.setPort(modbusClientConfig.getPort());
        }
        if (modbusClientConfig.getHeartbeatAddress() != null) {
            client.setHeartbeatAddress(modbusClientConfig.getHeartbeatAddress());
        }
        if (modbusClientConfig.getPlcCommuStatus() != null) {
            client.setPlcCommuStatus(modbusClientConfig.getPlcCommuStatus());
        }
        if (modbusClientConfig.getVfdCommuAddress() != null) {
            client.setVfdCommuAddress(modbusClientConfig.getVfdCommuAddress());
        }
        if (modbusClientConfig.getVfdCommuStatus() != null) {
            client.setVfdCommuStatus(modbusClientConfig.getVfdCommuStatus());
        }
        if (modbusClientConfig.getName() != null) {
            client.setName(modbusClientConfig.getName());
        }
        if (modbusClientConfig.getStatus() != null) {
            client.setStatus(modbusClientConfig.getStatus());
        }

        client.setUpdateTime(new Date());
        client.setUpdateUser(BackendUtil.getInstance().getCurrentUserId());
        client.setModbusMasterModel(new ModbusMasterModel(client.getIp(), client.getPort(), ModbusUtil.getModbusMaster(client.getIp(), client.getPort())));

        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "修改成功", null);
    }

    /**
     * 分页查询（查询内存的ConcurrentHashMap）
     *
     * @param page               分页参数
     * @param modbusClientConfig ModbusClient配置
     * @return /
     */
    @Override
    public IPage<ModbusClientConfig> myPage(IPage<ModbusClientConfig> page, ModbusClientConfig modbusClientConfig) {
        List<ModbusClientConfig> list = new ArrayList<>();
        for (Map.Entry<Long, ModbusClientConfig> kv : modbusClientMap.entrySet()) {
            ModbusClientConfig client = kv.getValue();

            // 名称模糊检索（存在名称的检索条件，则把不符合条件的筛掉）
            if (StringUtil.isNotEmpty(modbusClientConfig.getName())) {
                if (StringUtil.isEmpty(client.getName())) {
                    continue;
                }
                if (!client.getName().contains(modbusClientConfig.getName())) {
                    continue;
                }
            }

            // PLC心跳状态检索（把不相等的筛选掉）
            if (modbusClientConfig.getPlcCommuStatus() != null) {
                if (client.getPlcCommuStatus() == null) {
                    continue;
                }
                if (!client.getPlcCommuStatus().equals(modbusClientConfig.getPlcCommuStatus())) {
                    continue;
                }
            }

            // 变频器状态检索（把不相等的筛选掉）
            if (modbusClientConfig.getVfdCommuStatus() != null) {
                if (client.getVfdCommuStatus() == null) {
                    continue;
                }
                if (!client.getVfdCommuStatus().equals(modbusClientConfig.getVfdCommuStatus())) {
                    continue;
                }
            }
            //将结果加入到list集合
            list.add(client);
        }

        // 排序
        Collections.sort(list, new Comparator<ModbusClientConfig>()
        {
            @Override
            public int compare(ModbusClientConfig o1, ModbusClientConfig o2) {
                if (StringUtil.isEmpty(o1.getName())) {
                    // o1、o2名称均为空，o1在前
                    if (StringUtil.isEmpty(o2.getName())) {
                        return -1;
                    }
                    else {
                        // o1为空，o2不为空， o1在后
                        return 1;
                    }
                }

                // o2为空，o1不为空， o1在前
                if (StringUtil.isEmpty(o2.getName()) && StringUtil.isNotEmpty(o1.getName())) {
                    return -1;
                }

                // 按名称排序
                return o1.getName().compareTo(o2.getName());
            }
        });

        // 分页显示数据
        long start = (page.getCurrent() - 1) * page.getSize();
        long end = page.getCurrent() * page.getSize();
        if (start > list.size()) {
            page.setRecords(new ArrayList<>());
        }
        else if (end > list.size()) {
            page.setRecords(list.subList((int) start, list.size()));
        }
        else {
            page.setRecords(list.subList((int) start, (int) end));
        }
        page.setTotal(list.size());

        return page;
    }

    /**
     * 详情获取
     *
     * @param modbusClientConfig ModbusClient配置
     * @return /
     */
    @Override
    public ModbusClientConfig getMyOne(ModbusClientConfig modbusClientConfig) {
        return modbusClientMap.get(modbusClientConfig.getId());
    }

    /**
     * 删除
     *
     * @param ids ID列表
     * @return /
     */
    @Override
    public Msg myRemove(String ids) {
        boolean flag = deleteLogic(FuncUtil.toLongList(ids));

        if (flag) {
            //清除内存数据
            FuncUtil.toLongList(ids).forEach(id -> {
                if (modbusClientMap.containsKey(id)) {
                    modbusClientMap.remove(id);
                }
            });
        }

        return ReturnMsg.status(flag);
    }


    /**
     * 持久化（将内存中数据，写入到数据库）
     */
    @Override
    public void writeDb() {
        List<Long> idList = new ArrayList<>();
        for (Map.Entry<Long, ModbusClientConfig> kv : modbusClientMap.entrySet()) {
            idList.add(kv.getKey());
        }
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        // 查询数据库
        List<ModbusClientConfig> dbList = list(Wrappers.<ModbusClientConfig>query().lambda().in(ModbusClientConfig::getId, idList));

        // 构造dbMap
        HashMap<Long, ModbusClientConfig> dbMap = new HashMap<>();
        dbList.forEach(client -> dbMap.put(client.getId(), client));

        // 筛选需要持久化的数据
        List<ModbusClientConfig> writeDbList = new ArrayList<>();
        for (Map.Entry<Long, ModbusClientConfig> kv : modbusClientMap.entrySet()) {
            ModbusClientConfig memoryClient = kv.getValue();
            //修改日期不相等，则说明该条记录在这一分钟内，有修改过（则需要持久化到db）
            if (!memoryClient.getUpdateTime().equals(dbMap.get(memoryClient.getId()).getUpdateTime())) {
                writeDbList.add(memoryClient);
            }
        }

        // 持久化写入数据库
        if (CollectionUtils.isEmpty(writeDbList)) {
            return;
        }

        log.info("客户端信息 持久化 ......");
        updateBatchById(writeDbList);
    }

    /**
     * 方法描述: 写心跳信息，刷新PLC和变频器状态
     */
    @Override
    public void flushHeartbeatAndVfdStatus() {
        for (Map.Entry<Long, ModbusClientConfig> kv : modbusClientMap.entrySet()) {
            ModbusClientConfig client = kv.getValue();

            // 写心跳信息
            boolean heartResult = false;
            try {
                heartResult = modbusService.writeModbus(client, client.getHeartbeatStationNum(), client.getHeartbeatAddress(), 1);
            }
            catch (Exception e) {
                log.error("心跳写入失败，client ---> " + client.getId(), e);
            }

            if (heartResult) {
                client.setPlcCommuStatus(1);
                client.setPlcStatusUpdateTime(new Date());
            }

            // 读取变频器状态
            Double vfdResult = null;
            try {
                vfdResult = modbusService.readModbus(client, client.getVfdCommuStationNum(), client.getVfdCommuAddress());
            }
            catch (Exception e) {
                log.error("读取变频器状态失败，client ---> " + client.getId(), e);
            }
            if (vfdResult != null) {
                client.setVfdCommuStatus(vfdResult.intValue());
                client.setVfdStatusUpdateTime(new Date());
            }
        }
    }

}
