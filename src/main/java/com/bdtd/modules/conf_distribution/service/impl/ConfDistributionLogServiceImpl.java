/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import com.bdtd.modules.conf_distribution.mapper.ConfDistributionLogMapper;
import com.bdtd.modules.conf_distribution.service.ConfDistributionLogService;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO;
import org.springframework.stereotype.Service;

/**
 * 组态下发日志 服务实现类
 */
@Service
public class ConfDistributionLogServiceImpl extends BaseServiceImpl<ConfDistributionLogMapper, ConfDistributionLog> implements ConfDistributionLogService
{

    @Override
    public IPage<ConfDistributionLogVO> selectConfDistributionLogPage(IPage<ConfDistributionLogVO> page, ConfDistributionLogVO confDistributionLog) {
        return page.setRecords(baseMapper.selectConfDistributionLogPage(page, confDistributionLog));
    }

    /**
     * 方法描述: 下发日志 分页查询
     *
     * @param page
     * @param vo
     * @throws
     * @Return {@link IPage< ConfDistributionLog>}
     * @date
     */
    @Override
    public IPage<ConfDistributionLogVO> myPage(IPage<ConfDistributionLogVO> page, ConfDistributionLogVO vo) {
        return this.baseMapper.selectPageConditional(page, vo);
    }

}
