/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.conf_distribution.dto.ConfMeasurePointDTO;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.mapper.ConfMeasurePointMapper;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StringUtil;
import com.bdtd.util.bean.BeanUtil;
import com.bdtd.util.exception.BadRequestException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 测点
 */
@Service
public class ConfMeasurePointServiceImpl extends ServiceImpl<ConfMeasurePointMapper, ConfMeasurePoint> implements ConfMeasurePointService
{

    @Override
    public boolean submit(ConfMeasurePointDTO dto) throws Exception {
        if (StringUtil.isEmpty(dto.getOutPointId())) {
            throw new BadRequestException("测点已存在");
        }
        if (StringUtil.isEmpty(dto.getOutPointId())) {
            throw new BadRequestException("测点已存在");
        }

        LambdaQueryWrapper<ConfMeasurePoint> queryWrapper = new LambdaQueryWrapper<>();

        if (dto.getId() != null) {
            queryWrapper.ne(ConfMeasurePoint::getId, dto.getId());
        }

        queryWrapper.and(
                qw -> qw.eq(ConfMeasurePoint::getName, dto.getName())
                        .or()
                        .eq(ConfMeasurePoint::getOutPointId, dto.getOutPointId())
        );

        List<ConfMeasurePoint> existedList = list(queryWrapper);
        if (!existedList.isEmpty()) {
            throw new BadRequestException("测点名称或者测点ID已经存在");
        }

        if (dto.getIsDeleted() == null) {
            dto.setIsDeleted(0);
        }

        ConfMeasurePoint data = BeanUtil.copy(dto, ConfMeasurePoint.class);

        return this.saveOrUpdate(data);
    }

    @Override
    public ConfMeasurePoint findByOutPointId(String pointId) {
        LambdaQueryWrapper<ConfMeasurePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfMeasurePoint::getOutPointId, pointId);
        List<ConfMeasurePoint> data = this.list(queryWrapper);
        return FuncUtil.isEmpty(data) ? null : data.get(0);
    }

    @Override
    public IPage<ConfMeasurePoint> selectWhitePointPages(IPage<ConfMeasurePoint> page, ConfMeasurePointDTO confMeasurePointDTO) {
        List<ConfMeasurePoint> resultList = baseMapper.selectWhitePointPages(page, confMeasurePointDTO);
        return page.setRecords(resultList);
    }

}
