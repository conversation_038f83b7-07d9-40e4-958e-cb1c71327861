/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.conf_distribution.entity.ConfBlackList;
import com.bdtd.modules.conf_distribution.mapper.ConfBlackListMapper;
import com.bdtd.modules.conf_distribution.service.ConfBlackListService;
import org.springframework.stereotype.Service;

/**
 * 测点黑名单
 *
 *
 *
 *
 *
 * @date
 */
@Service
public class ConfBlackListServiceImpl extends ServiceImpl<ConfBlackListMapper, ConfBlackList> implements ConfBlackListService {


}
