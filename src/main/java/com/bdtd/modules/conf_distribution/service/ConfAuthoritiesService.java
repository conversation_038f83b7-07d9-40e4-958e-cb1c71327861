package com.bdtd.modules.conf_distribution.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.ConfAuthoritiesSaveDto;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.ConfDistBulkDto;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.ConfDistDto;
import com.bdtd.modules.conf_distribution.entity.ConfAuthorities;

import java.util.List;
import java.util.Map;

/**
 * 测点权限
 *
 * <AUTHOR>
 */
public interface ConfAuthoritiesService extends IService<ConfAuthorities>
{

    /**
     * 方法描述: 新增或修改
     *
     * @param saveDTO 保存实体
     * @return {@link boolean}
     */
    boolean submitConfAuthorities(ConfAuthoritiesSaveDto saveDTO);

    /**
     * 方法描述:根据测点ID和用户ID查询列表
     *
     * @param diagramId 测点ID
     * @param userId    用户ID
     * @return {@link List<ConfAuthorities>}
     */
    List<ConfAuthorities> findByPointIdAndUserId(Long diagramId, Long userId);

    /**
     * 方法描述: 根据用户名密码测点ID进行单点授权下发
     *
     * @param singleDto           实体列表
     * @param headersMap          header信息Map
     * @param clickStartTimestamp 下发出发时间
     * @return {@link String} 下发成功返回null, 下发失败返回错误文本
     */
    String invoke(ConfDistDto singleDto, Map<String, String> headersMap, Long clickStartTimestamp);

    /**
     * 方法描述: 根据用户名密码测点ID进行多点授权下发
     *
     * @param bulkDto             多下发实体
     * @param headersMap          header信息Map
     * @param clickStartTimestamp 下发出发时间
     * @return {@link String} 下发成功返回null, 下发失败返回错误文本
     */
    String multiInvoke(ConfDistBulkDto bulkDto, Map<String, String> headersMap, Long clickStartTimestamp);

}
