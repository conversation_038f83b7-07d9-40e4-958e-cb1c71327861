package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点权限
 */
@Data
public class ConfAuthoritiesDto
{
    /** ID */
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 测点ID */
    @ApiModelProperty(value = "测点ID")
    private Long pointId;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

}
