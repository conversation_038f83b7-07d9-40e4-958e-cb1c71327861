package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 点位下发鉴权实体
 *
 * <AUTHOR>
 */
@Data
@Builder
public class ConfDistAuthDto
{

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

    /** 点位控制密钥 */
    @ApiModelProperty(value = "点位控制密钥")
    private String pointSecret;

    /** 接口令牌 */
    @ApiModelProperty(value = "接口令牌")
    private String apiToken;

    /** 控制点位列表 */
    @ApiModelProperty(value = "接口令牌")
    private List<String> pointIdList;

}
