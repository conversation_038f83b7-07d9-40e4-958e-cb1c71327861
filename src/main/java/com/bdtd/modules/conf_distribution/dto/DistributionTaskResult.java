package com.bdtd.modules.conf_distribution.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 控制命令下发结果实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionTaskResult
{
    /** 是否成功 */
    private Boolean success;

    /** 错误消息 */
    private String message;

    /** 下发开始时间 */
    private Long startTime;

    /** 下发结束时间 */
    private Long finishTime;
}
