package com.bdtd.modules.conf_distribution.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点DTO
 *
 *
 *
 * <AUTHOR>
@Data
public class ConfMeasurePointDTO
{
    private Long id;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "测点名称")
    private String name;

    /**
     * 外部测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private String outPointId;


    /**
     * 测点类型(开关量，模拟量)
     */
    @ApiModelProperty(value = "测点类型(0:开关量，1:模拟量)")
    private Integer type;

    /**
     * 控制类型
     */
    @ApiModelProperty(value = "控制类型(待定)")
    private Integer controlType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;

    /**
     * 转发地址
     */
    @ApiModelProperty(value = "转发地址")
    private String url;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    /** 点位控制密钥 */
    @ApiModelProperty(value = "点位控制密钥")
    private String pointSecret;
}
