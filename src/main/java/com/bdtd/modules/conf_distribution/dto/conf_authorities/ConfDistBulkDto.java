package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 点位控制多下发实体
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfDistBulkDto
{

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

    /** 接口令牌 */
    @ApiModelProperty(value = "接口令牌")
    private String apiToken;

    /** 是否是脉冲调用 */
    @ApiModelProperty(value = "是否是脉冲调用(1-是，0-不是)")
    private Integer pulseState;

    /** 脉冲时间间隔 */
    @ApiModelProperty(value = "总耗时")
    private Long elapsedTime;

    /** 是否启用延时重置下发 */
    @ApiModelProperty(value = "是否启用延时重置下发(1-是，0-不是)")
    private Integer delayState;

    /** 延时时间(毫秒) */
    @ApiModelProperty(value = "延时时间(毫秒)")
    private Integer delayTime;

    /** 下发点位列表, 脉冲和延时的情况，value为空初始不下发，resetValue、delayValue为空不重复下发 */
    @ApiModelProperty(value = "下发点位列表")
    List<ConfDistItemDto> distValues;

}
