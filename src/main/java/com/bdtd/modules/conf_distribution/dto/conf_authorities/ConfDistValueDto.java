package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 点位控制下发实体
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfDistValueDto
{

    /** 外部测点ID */
    @ApiModelProperty(value = "测点ID")
    private String pointId;

    /** 值(控制值) */
    @ApiModelProperty(value = "值(控制值)")
    private String value;

}
