package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点控制下发实体
 *
 * <AUTHOR>
 */
@Data
public class ConfDistItemDto
{
    /** 外部测点ID */
    @ApiModelProperty(value = "测点ID")
    private String pointId;

    /** 值(控制值) */
    @ApiModelProperty(value = "控制值")
    private String value;

    /** 复位值(复位值) */
    @ApiModelProperty(value = "复位值，脉冲模式下发时提供")
    private String resetValue;

    /** 延时重置值(控制值) */
    @ApiModelProperty(value = "延时重置值，延时下发模式时提供")
    private String delayValue;

}
