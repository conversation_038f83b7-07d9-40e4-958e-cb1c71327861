package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点控制下发实体
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfDistDto
{
    /** 外部测点ID */
    @ApiModelProperty(value = "测点ID")
    private String pointId;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

    /** 点位控制密钥 */
    @ApiModelProperty(value = "点位控制密钥")
    private String pointSecret;

    /** 接口令牌 */
    @ApiModelProperty(value = "接口令牌")
    private String apiToken;

    /** 是否验证用户名密码 */
    @ApiModelProperty(value = "是否验证用户名密码(1-验证，0-不验证)")
    private Integer authState;

    /** 值(控制值) */
    @ApiModelProperty(value = "值(控制值)")
    private String value;

    /** 是否是脉冲调用 */
    @ApiModelProperty(value = "是否是脉冲调用(1-是，0-不是)")
    private Integer pulseState;

    /** 脉冲时间间隔 */
    @ApiModelProperty(value = "总耗时")
    private Long elapsedTime;

    /** 复位值(复位值) */
    @ApiModelProperty(value = "复位值(复位值)")
    private String resetValue;

    /** 是否启用延时重置下发 */
    @ApiModelProperty(value = "是否启用延时重置下发(1-是，0-不是)")
    private Integer delayState;

    /** 延时时间(毫秒) */
    @ApiModelProperty(value = "延时时间(毫秒)")
    private Integer delayTime;

    /** 延时重置值(控制值) */
    @ApiModelProperty(value = "延时重置值(控制值)")
    private String delayValue;

}
