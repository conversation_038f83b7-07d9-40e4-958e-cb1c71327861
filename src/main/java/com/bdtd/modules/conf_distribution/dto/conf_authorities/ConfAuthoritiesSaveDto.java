package com.bdtd.modules.conf_distribution.dto.conf_authorities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 测点权限保存用实体
 *
 * <AUTHOR>
 */
@Data
public class ConfAuthoritiesSaveDto
{

    /** 点位ID */
    @ApiModelProperty(value = "点位ID")
    private Long pointId;

    /** 人员ID */
    @ApiModelProperty(value = "人员ID")
    private List<Long> userIds;

}
