package com.bdtd.modules.conf_distribution.util;

import com.bdtd.modules.conf_distribution.dto.DistributionTaskResult;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.ConfDistValueDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * 单点下发任务
 *
 * <AUTHOR>
 */
@Slf4j
public class SingleDistributionTask implements Callable<DistributionTaskResult> {
    String url;

    Long userId;

    ConfDistValueDto distValue;

    Integer waitTime;

    public SingleDistributionTask(String distUrl, Long userId, ConfDistValueDto distValue, Integer waitTime) {
        this.url = distUrl;
        this.userId = userId;
        this.distValue = distValue;
        this.waitTime = waitTime;
    }

    @Override
    public DistributionTaskResult call() throws Exception {
        Thread.sleep(waitTime);

        DistributionTaskResult taskResult = new DistributionTaskResult();

        // 建立HttpPost对象
        HttpPost httppost = new HttpPost(url);
        // 创建HttpClient对象
        CloseableHttpClient client = HttpClients.createDefault();

        try {
            // 请求参数
            List<BasicNameValuePair> paramsList = new ArrayList<>();
            paramsList.add(
                    new BasicNameValuePair(
                            "point_id",
                            distValue.getPointId().contains(".") ? distValue.getPointId().substring(7) : distValue.getPointId()
                    )
            );
            paramsList.add(new BasicNameValuePair("value", distValue.getValue()));

            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramsList, "UTF-8");
            httppost.setEntity(entity);

            // 设置请求和传输时长
            RequestConfig.Builder builder = RequestConfig.custom();
            builder.setSocketTimeout(5000);
            builder.setConnectTimeout(5000);

            RequestConfig config = builder.build();
            httppost.setConfig(config);

            taskResult.setStartTime(System.currentTimeMillis());

            // 发送 Post
            client.execute(httppost);

            taskResult.setSuccess(true);
            taskResult.setMessage("下发成功");
        }
        catch (Exception e) {
            log.error("singleIssue error: " + e.getMessage(), e);

            taskResult.setSuccess(false);
            taskResult.setMessage("下发失败: " + e.getMessage());
        }
        finally {
            taskResult.setFinishTime(System.currentTimeMillis());

            try {
                client.close();
                httppost.releaseConnection();
            }
            catch (Exception e) {
                log.error("close single distribution client error: " + e.getMessage(), e);
            }
        }

        return taskResult;
    }
}
