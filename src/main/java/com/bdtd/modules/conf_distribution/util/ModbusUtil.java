package com.bdtd.modules.conf_distribution.util;

import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.ip.IpParameters;
import lombok.extern.slf4j.Slf4j;

/**
 * modbus工具类
 */
@Slf4j
public class ModbusUtil
{
    public static ModbusMaster getModbusMaster(String ip, int port) {
        IpParameters params = new IpParameters();
        // params.setHost("localhost");
        params.setHost(ip);
        params.setPort(port);
        params.setEncapsulated(false);

        ModbusMaster master = new ModbusFactory().createTcpMaster(params, true);
        try {
            // 设置超时时间
            master.setTimeout(10000);
            // 设置重连次数
            master.setRetries(3);
            // 初始化
            master.init();
        }
        catch (ModbusInitException e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return master;
    }

    public static String getAllStrLock(int last) {
        String str = "";
        for (int i = last; i > 0; i--) {
            str += "0";
        }
        return str.trim();
    }
}
