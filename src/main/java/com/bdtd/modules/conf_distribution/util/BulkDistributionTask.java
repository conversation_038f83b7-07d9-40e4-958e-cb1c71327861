package com.bdtd.modules.conf_distribution.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.conf_distribution.dto.DistributionTaskResult;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.ConfDistValueDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.util.List;
import java.util.concurrent.Callable;

/**
 * 多点下发任务
 *
 * <AUTHOR>
 */
@Slf4j
public class BulkDistributionTask implements Callable<DistributionTaskResult>
{
    String url;

    Long userId;

    List<ConfDistValueDto> confDistDtoList;

    Integer waitTime;

    public BulkDistributionTask(String distUrl, Long userId, List<ConfDistValueDto> confDistDtoList, Integer waitTime) {
        this.url = distUrl;
        this.userId = userId;
        this.confDistDtoList = confDistDtoList;
        this.waitTime = waitTime;
    }

    @Override
    public DistributionTaskResult call() throws Exception {
        Thread.sleep(waitTime);

        DistributionTaskResult taskResult = new DistributionTaskResult();

        // 下发值列表检查
        if (confDistDtoList == null || confDistDtoList.size() == 0) {
            taskResult.setSuccess(false);
            taskResult.setMessage("未指定下发内容");

            return taskResult;
        }

        // 建立HttpPost对象
        HttpPost httppost = new HttpPost(url);
        // 创建HttpClient对象
        CloseableHttpClient client = HttpClients.createDefault();

        try {
            // 添加参数
            JSONArray jsonArray = new JSONArray();
            for (ConfDistValueDto confDistDto : confDistDtoList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("point_id", confDistDto.getPointId().contains(".") ? confDistDto.getPointId().substring(7) : confDistDto.getPointId());
                jsonObject.put("value", confDistDto.getValue());

                jsonArray.add(jsonObject);
            }

            StringEntity entity = new StringEntity(String.valueOf(jsonArray), "utf-8");
            httppost.setEntity(entity);

            // 设置请求和传输时长
            RequestConfig.Builder builder = RequestConfig.custom();
            builder.setSocketTimeout(5000);
            builder.setConnectTimeout(5000);
            RequestConfig config = builder.build();
            httppost.setConfig(config);

            httppost.setHeader("Content-type", "application/json");

            taskResult.setStartTime(System.currentTimeMillis());

            // 发送 Post
            client.execute(httppost);

            taskResult.setSuccess(true);
            taskResult.setMessage("下发成功");
        }
        catch (Exception e) {
            log.error("bulkDistribution error: " + e.getMessage(), e);

            taskResult.setSuccess(false);
            taskResult.setMessage("下发失败: " + e.getMessage());
        }
        finally {
            taskResult.setFinishTime(System.currentTimeMillis());

            try {
                client.close();
                httppost.releaseConnection();
            }
            catch (Exception e) {
                log.error("close bulk distribution client error: " + e.getMessage(), e);
            }
        }

        return taskResult;
    }
}
