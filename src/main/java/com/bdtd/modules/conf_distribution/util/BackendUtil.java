package com.bdtd.modules.conf_distribution.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.base.entity.User;
import com.bdtd.modules.base.entity.UserInfo;
import com.bdtd.modules.conf_distribution.config.BackendConfig;
import com.bdtd.util.HttpReqUtil;
import com.bdtd.util.HttpUtil;
import com.bdtd.util.StringUtil;
import com.bdtd.util.spring.SpringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BackendUtil
{
    // mod-s 弃用 backend.host, backend.port 配置
    // // host:port+contextPath+uri
    // private static String URL_format = "%1$s:%2$s%3$s%4$s";
    private static String URL_FORMAT = "%1$s%2$s%3$s";
    // mod-e 弃用 backend.host, backend.port 配置

    private static volatile BackendUtil instance;
    private BackendConfig config;

    public final static String CURRENT_USER = "currentUserUrl";
    public final static String UER_INFO = "userInfoUrl";
    public final static String USER_LIST = "userListUrl";
    public final static String USER_BY_ID = "userByIdUrl";

    private BackendUtil() { }

    public static BackendUtil getInstance() {
        if (instance == null) {
            synchronized (BackendUtil.class) {
                if (instance == null) {
                    instance = new BackendUtil();
                    instance.config = SpringUtils.getBean(BackendConfig.class);
                }
            }
        }
        return instance;
    }

    public Long getCurrentUserId() {
        HttpServletRequest request = HttpReqUtil.getLocalRequest();
        String url = getFullUrl(request, CURRENT_USER);
        Map<String, String> headMap = getAuthHeaderMap(request);
        HttpUtil hu = HttpUtil.getInstance();
        HttpGet hg = hu.getHttpGet(url);
        hu.setHeader(hg, headMap);
        String resultString = executeAndResponse(hg);
        return Long.valueOf(resultString);
    }

    private Map<String, String> getAuthHeaderMap(HttpServletRequest request) {
        Map<String, String> headMap = HttpReqUtil.getAllHeaders(request);
        headMap.put("Tenant-Id", "000000");
        return headMap;
    }

    private Map<String, String> getAuthHeaderMapUserById(HttpServletRequest request) {
        Map<String, String> headMap = HttpReqUtil.getAllHeaders(request);

        headMap.remove("Content-Length");
        headMap.put("Tenant-Id", "000000");
        return headMap;
    }

    private Map<String, String> getAuthHeaderMapForUserInfo(HttpServletRequest request) {
        Map<String, String> headMap = HttpReqUtil.getAllHeaders(request);
        headMap.remove("Content-Length");
        headMap.put("Tenant-Id", "000000");
        headMap.put("Content-Type", "application/x-www-form-urlencoded");
        return headMap;
    }

    private Map<String, String> getRoleHeaderMap(HttpServletRequest request, String userId, String authorization, String bladeAuth) {
        Map<String, String> headMap = HttpReqUtil.getAllHeaders(request);
        headMap.put("Tenant-Id", "000000");
        headMap.put("Authorization", authorization);
        headMap.put("Blade-Auth", bladeAuth);
        headMap.put("id", userId);
        return headMap;
    }

    public User getUserById(Long userId) {
        HttpServletRequest request = HttpReqUtil.getLocalRequest();
        String url = getFullUrl(request, USER_BY_ID) + "/" + userId;
        Map<String, String> headMap = getAuthHeaderMapUserById(request);
        HttpUtil hu = HttpUtil.getInstance();
        HttpGet hg = hu.getHttpGet(url);
        hu.setHeader(hg, headMap);
        JSONObject resultString = executeAndResponseForAuthorityAuthentication(hg);
        return JSONObject.parseObject(JSONObject.toJSONString(resultString.getJSONObject("data")), User.class);

    }

    public UserInfo getUserInfo(String tenantId, String account, String password) {
        HttpPost hp = prepareHttpPostForUserInfo(UER_INFO);
        List<BasicNameValuePair> requestParams = new ArrayList<>();
        requestParams.add(new BasicNameValuePair("tenantId", tenantId));
        requestParams.add(new BasicNameValuePair("account", account));
        requestParams.add(new BasicNameValuePair("password", password));
        try {
            hp.setEntity(new UrlEncodedFormEntity(requestParams, "UTF-8"));
        }
        catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        JSONObject resultString = executeAndResponseForAuthorityAuthentication(hp);
        return JSONObject.parseObject(JSONObject.toJSONString(resultString.getJSONObject("data")), UserInfo.class);
    }

    public List<User> getUserListByUserId(List<Long> userIds) {
        HttpPost hp = prepareHttpPost(USER_LIST);
        hp.setHeader(HTTP.CONTENT_TYPE, "application/json");
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            hp.setEntity(new StringEntity(objectMapper.writeValueAsString(userIds),
                    ContentType.create("text/json", "UTF-8")));
        }
        catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        JSONObject resultString = executeAndResponseForAuthorityAuthentication(hp);
        return JSONArray.parseArray(JSONObject.toJSONString(resultString.getJSONArray("data")), User.class);
    }

    public List<User> getRoleListByUserId(String userIds, String authorization, String bladeAuth) {
        HttpServletRequest request = HttpReqUtil.getLocalRequest();
        String url = getFullUrl(request, USER_LIST);
        Map<String, String> headMap = getRoleHeaderMap(request, userIds, authorization, bladeAuth);
        HttpUtil hu = HttpUtil.getInstance();
        HttpGet hg = hu.getHttpGet(url);
        hu.setHeader(hg, headMap);
        JSONObject resultString = executeAndResponseJson(hg);
        if (!resultString.containsKey("code") || !"200".equals(resultString.getString("code"))) {
            return null;
        }
        else {
            String userJson = resultString.getJSONArray("data").toJSONString();
            return JSONArray.parseArray(userJson, User.class);
        }
    }

    public HttpPost prepareHttpPost(String flag) {
        HttpServletRequest request = HttpReqUtil.getLocalRequest();
        String url = getFullUrl(request, flag);
        Map<String, String> headMap = getAuthHeaderMapForUserInfo(request);
        HttpUtil hu = HttpUtil.getInstance();
        HttpPost hp = hu.getHttpPost(url);
        hu.setHeader(hp, headMap);
        return hp;
    }

    public HttpPost prepareHttpPostForUserInfo(String flag) {
        HttpServletRequest request = HttpReqUtil.getLocalRequest();
        String url = getFullUrl(request, flag);
        Map<String, String> headMap = getAuthHeaderMapForUserInfo(request);
        HttpUtil hu = HttpUtil.getInstance();
        HttpPost hp = hu.getHttpPost(url);
        hu.setHeader(hp, headMap);
        return hp;
    }

    public String executeAndResponse(HttpUriRequest hr) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        String result = null;
        try {
            client = HttpClientBuilder.create().build();
            response = client.execute(hr);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity);
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            if (response != null) {
                try {
                    response.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public JSONObject executeAndResponseForAuthorityAuthentication(HttpUriRequest hr) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        JSONObject result = null;
        try {
            client = HttpClientBuilder.create().build();
            response = client.execute(hr);
            HttpEntity entity = response.getEntity();
            result = JSONObject.parseObject(EntityUtils.toString(entity));
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            if (response != null) {
                try {
                    response.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public JSONObject executeAndResponseJson(HttpUriRequest hr) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        JSONObject resultJson = null;
        try {
            client = HttpClients.createDefault();
            response = client.execute(hr);
            HttpEntity entity = response.getEntity();
            resultJson = JSONObject.parseObject(EntityUtils.toString(entity));
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            if (response != null) {
                try {
                    response.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return resultJson;
    }

    public String getFullUrl(HttpServletRequest request, String flag) {
        // mod-s 弃用 backend.host, backend.port 配置
        // String host = config.getHost() == null || "".equals(config.getHost()) ? HttpReqUtil.getRequestHost(request) : config.getHost();
        // String contextPath = getValidateUri(config.getContextPath());
        // String port = config.getPort();
        // if(port == null){
        //     throw new RuntimeException("未提供后管平台用户相关接口配置");
        // }
        String baseUrl = StringUtil.isEmpty(config.getBaseUrl())
                ? HttpReqUtil.getRequestHost(request)
                : config.getBaseUrl();
        String contextPath = getValidateUri(config.getContextPath());
        // mod-e 弃用 backend.host, backend.port 配置

        String uri = null;
        switch (flag) {
            case CURRENT_USER:
                uri = config.getCurrentUserUrl();
                break;
            case UER_INFO:
                uri = config.getUserInfoUrl();
                break;
            case USER_LIST:
                uri = config.getUserListUrl();
                break;
            case USER_BY_ID:
                uri = config.getUserByIdUrl();
                break;
            default:
                throw new RuntimeException("未知的后管平台地址标识");
        }

        if (uri == null) {
            throw new RuntimeException("未提供后管平台用户相关接口配置");
        }

        uri = getValidateUri(uri);

        // mod-s 弃用 backend.host, backend.port 配置
        // return String.format(URL_format, host, port, contextPath, uri);
        return String.format(URL_FORMAT, baseUrl, contextPath, uri);
        // mod-e 弃用 backend.host, backend.port 配置
    }

    public String getValidateUri(String uri) {
        if (uri == null || uri.length() == 0 || "/".contentEquals(uri)) {
            return "";
        }

        if (!uri.startsWith("/")) {
            uri = "/" + uri;
        }

        return uri;
    }
}
