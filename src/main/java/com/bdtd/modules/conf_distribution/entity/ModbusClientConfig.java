/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实体类
 */
@Data
@TableName("blade_modbus_client_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ModbusClientConfig对象", description = "ModbusClientConfig对象")
public class ModbusClientConfig extends TenantEntity
{

    private static final long serialVersionUID = 1L;

    /** 方案id */
    @ApiModelProperty(value = "方案id")
    private Long schemeId;

    /** 方案编码 */
    @ApiModelProperty(value = "方案编码")
    private String schemeCode;

    /** ip地址 */
    @ApiModelProperty(value = "ip地址")
    private String ip;

    /** 端口 */
    @ApiModelProperty(value = "端口")
    private Integer port;

    /** PLC中心跳连接地址 */
    @ApiModelProperty(value = "PLC中心跳连接地址")
    private Integer heartbeatAddress;

    /** PLC通讯状态 */
    @ApiModelProperty(value = "PLC通讯状态")
    private Integer plcCommuStatus;

    /** PLC中存储变频器通讯状态的地址 */
    @ApiModelProperty(value = "PLC中存储变频器通讯状态的地址")
    private Integer vfdCommuAddress;

    /** 变频器通讯状态 */
    @ApiModelProperty(value = "变频器通讯状态")
    private Integer vfdCommuStatus;

    /** 客户端名称 */
    @ApiModelProperty(value = "客户端名称")
    private String name;

    /** PLC 通讯状态 更新时间 */
    @ApiModelProperty(value = "PLC 通讯状态 更新时间")
    @TableField(exist = false)
    private Date plcStatusUpdateTime;

    /** 变频器状态 更新时间 */
    @ApiModelProperty(value = "变频器状态 更新时间")
    @TableField(exist = false)
    private Date vfdStatusUpdateTime;

    /** 维护的客户端对象 */
    @ApiModelProperty(value = "维护的客户端对象")
    @TableField(exist = false)
    @JsonIgnore
    private ModbusMasterModel modbusMasterModel;

    /**
     * 写心跳信息 站号
     */
    @ApiModelProperty(value = "写心跳信息 站号")
    private Integer heartbeatStationNum;

    /**
     * 变频器读通讯状态 站号
     */
    @ApiModelProperty(value = "变频器读通讯状态 站号")
    private Integer vfdCommuStationNum;
}
