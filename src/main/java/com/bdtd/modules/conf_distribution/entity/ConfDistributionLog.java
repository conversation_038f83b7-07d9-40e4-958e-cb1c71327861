/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.TenantEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 组态下发日志实体类
 */
@Data
@TableName("blade_conf_distribution_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConfDistributionLog对象", description = "组态下发日志")
public class ConfDistributionLog extends TenantEntity
{

    private static final long serialVersionUID = 1L;

    /** 开始点击的时间（请求点击时间戳） */
    @ApiModelProperty(value = "开始点击的时间（请求点击时间戳）（16位时间戳  微妙数）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeStamp;

    /** 请求点击-请求到达延迟 */
    @ApiModelProperty(value = "请求点击-请求到达延迟（微妙数）")
    private Long clickReachElapsed;

    /** 请求业务处理延迟 */
    @ApiModelProperty(value = "请求业务处理延迟（微妙数）")
    private Long businessElapsed;

    /** 调用下发-下发执行完毕延迟 */
    @ApiModelProperty(value = "调用下发-下发执行完毕延迟（微妙数）")
    private Long invokeIssueElapsed;

    /** 点位id */
    @ApiModelProperty(value = "点位id")
    private String pointId;

    /** 要修改为的值 */
    @ApiModelProperty(value = "要修改为的值")
    private String value;

    /** 运行频率可为空 */
    @ApiModelProperty(value = "运行频率可为空")
    private String frequency;

    /** 调用API结束的完成时间 */
    @ApiModelProperty(value = "调用API结束的完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeStamp;

    /** 总耗时（毫秒数） */
    @ApiModelProperty(value = "总耗时（毫秒数）")
    private Long elapsedTime;

    /** 执行结果（0 失败， 1 成功  ......） */
    @ApiModelProperty(value = "执行结果（0 失败， 1 成功  ......）")
    private Integer actionResult;

    /** ip */
    @ApiModelProperty(value = "ip", hidden = true)
    @JsonIgnore
    private String ip;

    /** 端口 */
    @ApiModelProperty(value = "端口", hidden = true)
    @JsonIgnore
    private Integer port;

    /** 地址 */
    @ApiModelProperty(value = "地址（点位）", hidden = true)
    @JsonIgnore
    private Integer address;

    /** 站号 */
    @ApiModelProperty(value = "站号", hidden = true)
    @JsonIgnore
    private Integer stationNum;

    /** 方案id */
    @ApiModelProperty(value = "方案id", hidden = true)
    @JsonIgnore
    private Long schemeId;

    /** 方案code */
    @ApiModelProperty(value = "方案code", hidden = true)
    @JsonIgnore
    private String schemeCode;

    /** 客户端id */
    @ApiModelProperty(value = "客户端id", hidden = true)
    @JsonIgnore
    private Long clientId;

    /** 客户端名称 */
    @ApiModelProperty(value = "客户端名称", hidden = true)
    @JsonIgnore
    private String clientName;

    /** 方案名称 */
    @ApiModelProperty(value = "方案名称", hidden = true)
    @JsonIgnore
    private String schemeName;

    /** 操作人 */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /** 用户名 */
    @ApiModelProperty(value = "操作人")
    private String operator;

}
