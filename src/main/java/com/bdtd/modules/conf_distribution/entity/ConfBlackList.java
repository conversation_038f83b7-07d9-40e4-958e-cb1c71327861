package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 测点黑名单
 */
@Data
@TableName("blade_conf_black_list")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConfBlackList对象", description = "测点黑名单")
public class ConfBlackList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private Long pointId;
}
