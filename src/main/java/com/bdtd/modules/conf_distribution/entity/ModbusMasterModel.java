package com.bdtd.modules.conf_distribution.entity;

import com.serotonin.modbus4j.ModbusMaster;
import lombok.Data;

import java.util.Objects;

@Data
public class ModbusMasterModel {
    private  String ip;
    private  int port;
    private ModbusMaster modbusMaster;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ModbusMasterModel that = (ModbusMasterModel) o;
        return getPort() == that.getPort() &&
                Objects.equals(getIp(), that.getIp());
    }

    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), getIp(), getPort());
    }

    public ModbusMasterModel() {
    }

    public ModbusMasterModel(String ip, int port, ModbusMaster modbusMaster) {
        this.ip = ip;
        this.port = port;
        this.modbusMaster = modbusMaster;
    }
}
