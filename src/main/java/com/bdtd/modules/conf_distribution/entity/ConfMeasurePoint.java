package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测点
 */
@Data
@TableName("blade_conf_measure_point")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConfAuthorities对象", description = "测点权限")
public class ConfMeasurePoint extends BaseEntity
{

    /**
     * 外部测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private String outPointId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 测点类型(开关量，模拟量)
     */
    @ApiModelProperty(value = "测点类型(开关量，模拟量)")
    private Integer type;

    /**
     * 控制类型
     */
    @ApiModelProperty(value = "控制类型(待定)")
    private Integer controlType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;

    /**
     * 转发地址
     */
    @ApiModelProperty(value = "转发地址")
    private String url;

    /**
     * 链接类型（GET,POST）
     */
    @ApiModelProperty(value = "链接类型（GET,POST）")
    private String methodType;

    /**
     * 参数json
     */
    @ApiModelProperty(value = "参数json")
    private String paramJson;

    /** 点位控制密钥 */
    @ApiModelProperty(value = "点位控制密钥")
    private String pointSecret;
}
