package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 测点权限
 */
@Data
@TableName("blade_conf_authorities")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConfAuthorities对象", description = "测点权限")
public class ConfAuthorities extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private Long pointId;

    /**
     * 用户ID，逗号隔开
     */
    @ApiModelProperty(value = "用户ID，逗号隔开")
    private String userIds;
}
