/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 */
@Data
@TableName("blade_modbus_client_address")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ModbusClientAddress对象", description = "ModbusClientAddress对象")
public class ModbusClientAddress extends TenantEntity
{

    private static final long serialVersionUID = 1L;

    /** 客户端id */
    @ApiModelProperty(value = "客户端id")
    private Long clientId;

    /** 方案code */
    @ApiModelProperty(value = "方案code")
    private String schemeCode;

    /** 原始地址 */
    @ApiModelProperty(value = "原始地址")
    private Integer address;

    /** 站号 */
    @ApiModelProperty(value = "站号")
    private Integer stationNum;

    /** 下发参数 */
    @ApiModelProperty(value = "下发参数")
    private String param;

    /**
     * 点位编码
     */
    @ApiModelProperty(value = "点位编码")
    private String code;

}
