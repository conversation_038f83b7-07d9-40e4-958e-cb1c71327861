/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.modules.base.entity.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 命令下发方案实体类
 */
@Data
@TableName("blade_command_issued_scheme")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CommandIssuedScheme对象", description = "命令下发方案")
public class CommandIssuedScheme extends TenantEntity
{

    private static final long serialVersionUID = 1L;

    /** 方案名称 */
    @ApiModelProperty(value = "方案名称")
    private String name;

    /** 下发方案编码 */
    @ApiModelProperty(value = "下发方案编码")
    private String schemeCode;


}
