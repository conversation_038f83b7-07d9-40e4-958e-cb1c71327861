/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ModbusClientConfig;
import com.bdtd.modules.conf_distribution.service.IModbusClientConfigService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.wrapper.ModbusClientConfigWrapper;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/conf-distribution/modbus-client-config")
@Api(value = "modbus客户端配置", tags = "modbus客户端配置")
public class ModbusClientConfigController
{

    private final IModbusClientConfigService modbusClientConfigService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入modbusClientConfig")
    public ResponseEntity detail(ModbusClientConfig modbusClientConfig) {
        if (modbusClientConfig.getId() == null) {
            return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.ERROR.value, "请传入id", null), HttpStatus.OK);
        }
        ModbusClientConfig detail = modbusClientConfigService.getMyOne(modbusClientConfig);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", ModbusClientConfigWrapper.build().entityVO(detail)), HttpStatus.OK);
    }

    /**
     * 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入modbusClientConfig")
    public ResponseEntity list(ModbusClientConfig modbusClientConfig, Query query) {
        IPage<ModbusClientConfig> pages = modbusClientConfigService.myPage(Condition.getPage(query), modbusClientConfig);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", ModbusClientConfigWrapper.build().pageVO(pages)), HttpStatus.OK);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入modbusClientConfig")
    public ResponseEntity save(@Valid @RequestBody ModbusClientConfig modbusClientConfig) {
        return new ResponseEntity(ReturnMsg.status(modbusClientConfigService.add(modbusClientConfig)), HttpStatus.OK);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入modbusClientConfig")
    public ResponseEntity update(@Valid @RequestBody ModbusClientConfig modbusClientConfig) {
        return new ResponseEntity(modbusClientConfigService.myUpdateById(modbusClientConfig), HttpStatus.OK);
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public ResponseEntity remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return new ResponseEntity(modbusClientConfigService.myRemove(ids), HttpStatus.OK);
    }


}
