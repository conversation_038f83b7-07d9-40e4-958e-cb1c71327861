package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.dto.ConfMeasurePointDTO;
import com.bdtd.modules.conf_distribution.entity.ConfAuthorities;
import com.bdtd.modules.conf_distribution.entity.ConfBlackList;
import com.bdtd.modules.conf_distribution.entity.ConfMeasurePoint;
import com.bdtd.modules.conf_distribution.service.ConfAuthoritiesService;
import com.bdtd.modules.conf_distribution.service.ConfBlackListService;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.vo.ConfMeasurePointVO;
import com.bdtd.modules.conf_distribution.wrapper.ConfMeasurePointWrapper;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.bean.BeanUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 测点
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/conf-measure-point")
@AllArgsConstructor
@Api(value = "测点", tags = "测点", description = "测点")
public class ConfMeasurePointController
{
    private final ConfMeasurePointService confMeasurePointService;

    private final ConfBlackListService confBlackListService;

    private final ConfAuthoritiesService confAuthoritiesService;


    /**
     * 方法描述: 分页
     *
     * @param confMeasurePoint
     * @param query
     * @return
     * <AUTHOR>
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string")
    })
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入confMeasurePoint")
    public ResponseEntity page(@ApiIgnore @RequestParam Map<String, Object> confMeasurePoint, Query query) {
        QueryWrapper<ConfMeasurePoint> queryWrapper = Condition.getQueryWrapper(confMeasurePoint, ConfMeasurePoint.class);
        IPage<ConfMeasurePoint> pages = confMeasurePointService.page(Condition.getPage(query), queryWrapper);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", pages), HttpStatus.OK);
    }

    /**
     * 方法描述: 列表不分页
     *
     * @param confMeasurePoint
     * @return
     * <AUTHOR>
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "(开关量，模拟量)", paramType = "query", dataType = "number")
    })
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "列表", notes = "传入confMeasurePoint")
    public ResponseEntity list(@ApiIgnore @RequestParam Map<String, Object> confMeasurePoint) {
        LambdaQueryWrapper<ConfMeasurePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(confMeasurePoint.containsKey("name"), ConfMeasurePoint::getName, confMeasurePoint.get("name"));
        if (null != confMeasurePoint.get("type")) {
            queryWrapper.eq(confMeasurePoint.containsKey("type"), ConfMeasurePoint::getType, Integer.valueOf(confMeasurePoint.get("type").toString()));
        }
        List<ConfMeasurePoint> list = confMeasurePointService.list(queryWrapper);
        List<ConfBlackList> confBlackLists = confBlackListService.list();
        List<ConfMeasurePoint> blackPoint = new ArrayList<>();
        for (ConfMeasurePoint point : list) {
            for (ConfBlackList black : confBlackLists) {
                if (black.getPointId().equals(point.getId())) {
                    blackPoint.add(point);
                }
            }
        }
        list.removeAll(blackPoint);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", ConfMeasurePointWrapper.build().listVO(list)), HttpStatus.OK);
    }

    /**
     * 方法描述: 列表不分页
     *
     * @param confMeasurePointDTO 查询参数
     * @return /
     */
    @GetMapping("/page2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "(开关量，模拟量)", paramType = "query", dataType = "number")
    })
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页白名单", notes = "传入confMeasurePoint")
    public ResponseEntity page2(ConfMeasurePointDTO confMeasurePointDTO, Query query) {
        IPage<ConfMeasurePointVO> result = null;
        IPage<ConfMeasurePoint> pageResult = null;
        try {
            pageResult = confMeasurePointService.selectWhitePointPages(Condition.getPage(query), confMeasurePointDTO);
            if (pageResult != null) {
                result = ConfMeasurePointWrapper.build().pageVO(pageResult);
            }
        }
        catch (Exception ex) {
            log.error("query error: " + ex.getMessage(), ex);
        }

        return new ResponseEntity(
                ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", result),
                HttpStatus.OK
        );
    }


    /**
     * 方法描述: 详情
     *
     * @param confMeasurePointDTO
     * @return
     * <AUTHOR>
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入confMeasurePointDTO")
    public ResponseEntity detail(ConfMeasurePointDTO confMeasurePointDTO) {
        if (FuncUtil.isNotEmpty(confMeasurePointDTO.getOutPointId())) {
            ConfMeasurePoint confMeasurePoint = confMeasurePointService.findByOutPointId(confMeasurePointDTO.getOutPointId());
            return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", null == confMeasurePoint ? null : ConfMeasurePointWrapper.build().entityVO(confMeasurePoint)), HttpStatus.OK);
        }
        ConfMeasurePoint confMeasurePoint = confMeasurePointService.getOne(Condition.getQueryWrapper(Objects.requireNonNull(BeanUtil.copy(confMeasurePointDTO, ConfMeasurePoint.class))));

        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", ConfMeasurePointWrapper.build().entityVO(confMeasurePoint)), HttpStatus.OK);
    }


    /**
     * 方法描述: 新增或修改
     *
     * @param confMeasurePointDTO
     * @return
     * <AUTHOR>
    @PostMapping("/submit")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "新增或修改", notes = "传入confMeasurePointDTO")
    public ResponseEntity submit(@Valid @RequestBody ConfMeasurePointDTO confMeasurePointDTO) {
        Msg msg = null;
        try {
            msg = ReturnMsg.status(confMeasurePointService.submit(confMeasurePointDTO));
        }
        catch (BadRequestException bre) {
            log.error("新增或修改 ConfMeasurePointDTO 失败", bre);
            msg = ReturnMsg.status(false, bre.getMessage());
        }
        catch (Exception e) {
            log.error("新增或修改 ConfMeasurePointDTO 失败", e);
            msg = ReturnMsg.status(false, "服务器错误");
        }

        return new ResponseEntity(msg, HttpStatus.OK);
    }

    /**
     * 方法描述: 删除
     *
     * @param ids
     * <AUTHOR>
    @PostMapping("/remove")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public ResponseEntity remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        LambdaQueryWrapper<ConfBlackList> blackQueryWrapper = new LambdaQueryWrapper<>();
        blackQueryWrapper.in(ConfBlackList::getPointId, FuncUtil.toLongList(ids));
        confBlackListService.remove(blackQueryWrapper);
        LambdaQueryWrapper<ConfAuthorities> authQueryWrapper = new LambdaQueryWrapper<>();
        authQueryWrapper.in(ConfAuthorities::getPointId, FuncUtil.toLongList(ids));
        confAuthoritiesService.remove(authQueryWrapper);
        return new ResponseEntity(ReturnMsg.status(confMeasurePointService.removeByIds(FuncUtil.toLongList(ids))), HttpStatus.OK);
    }
}
