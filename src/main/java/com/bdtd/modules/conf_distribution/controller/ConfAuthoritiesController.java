package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.dto.conf_authorities.*;
import com.bdtd.modules.conf_distribution.entity.ConfAuthorities;
import com.bdtd.modules.conf_distribution.service.ConfAuthoritiesService;
import com.bdtd.modules.conf_distribution.service.ConfMeasurePointService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.vo.ConfAuthoritiesVO;
import com.bdtd.modules.conf_distribution.wrapper.ConfAuthoritiesWrapper;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.HttpReqUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.StringUtil;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 测点权限
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/conf-authorities")
@AllArgsConstructor
@Api(value = "测点权限", tags = "测点权限", description = "测点权限")
public class ConfAuthoritiesController
{
    private final ConfAuthoritiesService confAuthoritiesService;

    @Autowired
    private ConfMeasurePointService confMeasurePointService;

    /**
     * 方法描述: 根据用户名密码测点ID进行单点授权下发
     *
     * @param dto 下发实体
     * @return {@link ResponseEntity}
     */
    @PostMapping("/auth")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据用户名密码测点ID进行单点授权下发", notes = "传入用户名，密码，测点ID")
    public ResponseEntity<Msg> auth(@RequestBody List<ConfDistDto> dto, HttpServletRequest request) {
        Long requestStartTimestamp = System.currentTimeMillis();

        if (dto == null || dto.size() == 0) {
            return new ResponseEntity<>(ReturnMsg.fail("请传入测点ID"), HttpStatus.OK);
        }
        else if (dto.size() > 1) {
            return new ResponseEntity<>(ReturnMsg.fail("该接口不支持多测点下发"), HttpStatus.OK);
        }

        // 获取请求头
        Map<String, String> headersMap = HttpReqUtil.getAllHeaders(request);
        // 执行下发
        String result = confAuthoritiesService.invoke(dto.get(0), headersMap, requestStartTimestamp);

        return new ResponseEntity<>(
                StringUtil.isEmpty(result)
                        ? ReturnMsg.success(null, "下发成功")
                        : ReturnMsg.fail(result),
                HttpStatus.OK
        );
    }

    /**
     * 方法描述: 根据用户名密码测点ID进行多点授权下发
     *
     * @param dto 下发实体
     * @return {@link ResponseEntity}
     */
    @PostMapping("/multi-auth")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据用户名密码测点ID进行多点授权下发", notes = "传入用户名，密码，测点ID")
    public ResponseEntity<Msg> multiAuth(@RequestBody ConfDistBulkDto dto, HttpServletRequest request) {
        Long requestStartTimestamp = System.currentTimeMillis();

        // 检查传入测点
        if (dto == null || dto.getDistValues() == null || dto.getDistValues().size() == 0) {
            return new ResponseEntity<>(ReturnMsg.fail("请传入测点ID"), HttpStatus.OK);
        }

        // 获取请求头
        Map<String, String> headersMap = HttpReqUtil.getAllHeaders(request);
        // 执行下发
        String result = confAuthoritiesService.multiInvoke(dto, headersMap, requestStartTimestamp);

        return new ResponseEntity<>(
                StringUtil.isEmpty(result)
                        ? ReturnMsg.success(null, "下发成功")
                        : ReturnMsg.fail(result),
                HttpStatus.OK
        );
    }

    /**
     * 方法描述: 测点权限列表
     *
     * @param dto 查询实体
     * @return {@link ResponseEntity<List<ConfAuthoritiesVO>>}
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "测点权限列表", notes = "传入pointId")
    public ResponseEntity<Msg> list(ConfAuthoritiesDto dto) {
        List<ConfAuthorities> confAuthorities = confAuthoritiesService.findByPointIdAndUserId(dto.getPointId(), dto.getUserId());
        return new ResponseEntity<>(
                ReturnMsg.success(ConfAuthoritiesWrapper.build().listVO(confAuthorities), "操作成功"),
                HttpStatus.OK
        );
    }

    /**
     * 方法描述: 测点权限分页
     *
     * @param dto 查询实体
     * @return {@link ResponseEntity<IPage<ConfAuthoritiesVO>>}
     * @throws
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "测点权限分页", notes = "传入pointId")
    public ResponseEntity<Msg> page(ConfAuthoritiesDto dto, Query query) {
        LambdaQueryWrapper<ConfAuthorities> confAuthoritiesQueryWrapper = new LambdaQueryWrapper<>();
        confAuthoritiesQueryWrapper.like(null != dto.getUserId(), ConfAuthorities::getUserIds, dto.getUserId());
        confAuthoritiesQueryWrapper.eq(null != dto.getPointId(), ConfAuthorities::getPointId, dto.getPointId());
        IPage<ConfAuthorities> pages = confAuthoritiesService.page(Condition.getPage(query), confAuthoritiesQueryWrapper);
        return new ResponseEntity<>(
                ReturnMsg.success(ConfAuthoritiesWrapper.build().pageVO(pages), "操作成功"),
                HttpStatus.OK
        );
    }


    /**
     * 方法描述: 新增或修改
     *
     * @param saveDTO 保存实体
     * @return {@link ResponseEntity}
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "新增或修改", notes = "传入saveDTO")
    public ResponseEntity<Msg> submit(@Valid @RequestBody ConfAuthoritiesSaveDto saveDTO) {
        if (FuncUtil.isEmpty(saveDTO.getPointId())) {
            return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.ERROR.value, "请传入测点ID", null), HttpStatus.OK);
        }
        return new ResponseEntity<>(
                ReturnMsg.status(confAuthoritiesService.submitConfAuthorities(saveDTO)),
                HttpStatus.OK
        );
    }

    /**
     * 方法描述: 删除测点权限关系
     *
     * @param delDTO 删除实体
     * @return {@link ResponseEntity}
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "删除测点权限关系", notes = "传入delDTO")
    public ResponseEntity<Msg> remove(@RequestBody ConfAuthoritiesDelDto delDTO) {
        LambdaQueryWrapper<ConfAuthorities> confAuthoritiesLambdaQueryWrapper = new LambdaQueryWrapper<>();
        confAuthoritiesLambdaQueryWrapper.eq(ConfAuthorities::getPointId, delDTO.getPointId());
        return new ResponseEntity<>(
                ReturnMsg.status(confAuthoritiesService.remove(confAuthoritiesLambdaQueryWrapper)),
                HttpStatus.OK
        );

    }
}
