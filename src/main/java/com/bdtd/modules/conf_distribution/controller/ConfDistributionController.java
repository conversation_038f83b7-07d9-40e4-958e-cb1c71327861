package com.bdtd.modules.conf_distribution.controller;

import com.bdtd.modules.conf_distribution.service.ConfDistributionService;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionVo;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyTestVO;
import com.bdtd.modules.conf_distribution.vo.ControlFrequencyVO;
import com.bdtd.util.HttpReqUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 组态下发  Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/conf-distribution/invo")
@AllArgsConstructor
@Api(value = "组态下发", tags = "组态下发", description = "组态下发")
public class ConfDistributionController
{

    private final ConfDistributionService confDistributionService;

    @PostMapping("/invoke")
    @ApiOperation(value = "组态下发", notes = "组态下发")
    public ResponseEntity<Msg> invoke(@RequestBody ConfDistributionVo paramVo, HttpServletRequest request) {
        //获取请求头
        Map<String, String> headersMap = HttpReqUtil.getAllHeaders(request);
        Msg msg = confDistributionService.invokeAndLog(paramVo, headersMap);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }


    /**
     * 方法描述: 测试用接口
     *
     * @param paramMap 参数
     * @param request  请求信息
     * @throws
     * @Return {@link ResponseEntity}
     * <AUTHOR> @date
     */
    @GetMapping("/test-get")
    public ResponseEntity<Msg> test(@RequestParam Map<String, Object> paramMap, HttpServletRequest request) {
        //获取请求头
        Map<String, String> headersMap = HttpReqUtil.getAllHeaders(request);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", headersMap), HttpStatus.OK);
    }

    /**
     * 方法描述: 测试用接口
     *
     * @param paramMap 参数
     * @param request  请求信息
     * @throws
     * @Return {@link ResponseEntity}
     * <AUTHOR> @date
     */
    @PostMapping("/test-post")
    public ResponseEntity<Msg> testPost(@RequestBody Map<String, Object> paramMap, HttpServletRequest request) {
        //获取请求头
        Map<String, String> headersMap = HttpReqUtil.getAllHeaders(request);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", headersMap), HttpStatus.OK);
    }

    /**
     * 方法描述: 调速控制 - 旧
     *
     * @param address
     * @param value
     * @throws
     * @Return {@link ResponseEntity}
     * <AUTHOR>
    @GetMapping("/control-frequency-old/{id}/{value}")
    @ApiOperation(value = "调速控制 - 旧", notes = "调速控制 - 旧")
    public ResponseEntity<Msg> controlFrequency(@PathVariable(name = "id") Integer address, @PathVariable(name = "value") Integer value) {

        return new ResponseEntity<>(ReturnMsg.status(confDistributionService.controlFrequencyOld(address, value)), HttpStatus.OK);
    }

    /**
     * 方法描述: 调速控制
     *
     * @throws
     * <AUTHOR>
    @GetMapping("/control-frequency")
    @ApiOperation(value = "调速控制", notes = "调速控制")
    public ResponseEntity<Msg> controlFrequency(ControlFrequencyVO paramVo) {
        return new ResponseEntity<>(confDistributionService.controlFrequency(paramVo), HttpStatus.OK);
    }

    /**
     * 组态下发  测试接口
     *
     * @param paramVo
     * @return /
     */
    @GetMapping("/control-frequency-test")
    @ApiOperation(value = "组态下发 测试接口", notes = "组态下发 测试接口")
    public ResponseEntity<Msg> controlFrequencyTest(ControlFrequencyTestVO paramVo) {
        return new ResponseEntity<>(confDistributionService.controlFrequencyTest(paramVo), HttpStatus.OK);
    }

}
