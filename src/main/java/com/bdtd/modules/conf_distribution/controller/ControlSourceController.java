package com.bdtd.modules.conf_distribution.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ControlSource;
import com.bdtd.modules.conf_distribution.service.IAccessLinkTroubleService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "控制源")
@RestController
@RequestMapping(value = "/controlSource")
public class ControlSourceController {

    @Autowired
    private IAccessLinkTroubleService controlSourceService;

    @PostMapping("/controlSourceList")
    @ApiOperation(value = "查询控制源数据", notes = "查询控制源数据")
    public ResponseEntity controlSourceList(@RequestParam(required = false, value = "pageNum") Integer pageNum, @RequestParam(required = false, value = "pageSize") Integer pageSize, @RequestParam(required = false, value = "ip") String ip, @RequestParam(required = false, value = "port") Integer port,@RequestParam(required = false, value = "userName") String userName) {

        IPage<ControlSource> iPage = controlSourceService.controlSourceList(pageNum, pageSize, ip, port, userName);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", iPage), HttpStatus.OK);

    }

    @PostMapping("/controlSourceInsert")
    @ApiOperation(value = "添加控制源数据", notes = "添加控制源数据")
    public ResponseEntity insert(@RequestBody List<ControlSource> controlSource) {

        Msg msg = controlSourceService.controlSourceInsert(controlSource);
        return new ResponseEntity<>(msg, HttpStatus.OK);

    }

    @PostMapping("/controlSourceUpdate")
    @ApiOperation(value = "修改控制源数据", notes = "修改控制源数据")
    public ResponseEntity update(@RequestBody ControlSource controlSource) {

        Msg msg = controlSourceService.controlSourceUpdate(controlSource);
        return new ResponseEntity<>(msg, HttpStatus.OK);

    }

    @PostMapping("/controlSourceDelete")
    @ApiOperation(value = "修改控制源数据", notes = "修改控制源数据")
    public ResponseEntity delete(@RequestBody List<Integer> ids) {

        Msg msg = controlSourceService.controlSourceDelete(ids);
        return new ResponseEntity<>(msg, HttpStatus.OK);

    }
}
