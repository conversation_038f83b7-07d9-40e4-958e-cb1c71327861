/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import com.bdtd.modules.conf_distribution.service.ConfDistributionLogService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.vo.ConfDistributionLogVO;
import com.bdtd.modules.conf_distribution.wrapper.ConfDistributionLogWrapper;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 组态下发日志 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/conf-distribution/log")
@Api(value = "组态下发日志", tags = "组态下发日志接口")
public class ConfDistributionLogController
{

    private final ConfDistributionLogService confDistributionLogService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入confDistributionLog")
    public ResponseEntity detail(ConfDistributionLog confDistributionLog) {
        ConfDistributionLog detail = confDistributionLogService.getOne(Condition.getQueryWrapper(confDistributionLog));

        return new ResponseEntity(
            ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", ConfDistributionLogWrapper.build().entityVO(detail)), HttpStatus.OK);
    }

    /**
     * 分页 组态下发日志
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入confDistributionLog")
    public ResponseEntity list(ConfDistributionLogVO vo, Query query) {
        IPage<ConfDistributionLogVO> pages = confDistributionLogService.myPage(Condition.getPage(query), vo);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", pages), HttpStatus.OK);
    }


    /**
     * 自定义分页 组态下发日志
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入confDistributionLog")
    public ResponseEntity page(ConfDistributionLogVO confDistributionLog, Query query) {
        IPage<ConfDistributionLogVO> pages = confDistributionLogService.selectConfDistributionLogPage(Condition.getPage(query), confDistributionLog);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", pages), HttpStatus.OK);
    }

    /**
     * 新增 组态下发日志
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入confDistributionLog")
    public ResponseEntity save(@Valid @RequestBody ConfDistributionLog confDistributionLog) {
        return new ResponseEntity(ReturnMsg.status(confDistributionLogService.save(confDistributionLog)), HttpStatus.OK);
    }

    /**
     * 修改 组态下发日志
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入confDistributionLog")
    public ResponseEntity update(@Valid @RequestBody ConfDistributionLog confDistributionLog) {
        return new ResponseEntity(ReturnMsg.status(confDistributionLogService.updateById(confDistributionLog)), HttpStatus.OK);
    }

    /**
     * 新增或修改 组态下发日志
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入confDistributionLog")
    public ResponseEntity submit(@Valid @RequestBody ConfDistributionLog confDistributionLog) {
        return new ResponseEntity(ReturnMsg.status(confDistributionLogService.saveOrUpdate(confDistributionLog)), HttpStatus.OK);
    }


    /**
     * 删除 组态下发日志
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public ResponseEntity remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return new ResponseEntity(ReturnMsg.status(confDistributionLogService.deleteLogic(FuncUtil.toLongList(ids))), HttpStatus.OK);
    }


}
