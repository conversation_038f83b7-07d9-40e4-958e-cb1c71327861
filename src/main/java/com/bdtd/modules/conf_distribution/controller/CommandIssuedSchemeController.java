/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.entity.CommandIssuedScheme;
import com.bdtd.modules.conf_distribution.service.ICommandIssuedSchemeService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.vo.CommandIssuedSchemeVO;
import com.bdtd.modules.conf_distribution.wrapper.CommandIssuedSchemeWrapper;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 命令下发方案 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/conf-distribution/command-issued-scheme")
@Api(value = "命令下发方案", tags = "命令下发方案接口")
public class CommandIssuedSchemeController
{

    private final ICommandIssuedSchemeService commandIssuedSchemeService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入commandIssuedScheme")
    public ResponseEntity detail(CommandIssuedScheme commandIssuedScheme) {
        CommandIssuedScheme detail = commandIssuedSchemeService.getOne(Condition.getQueryWrapper(commandIssuedScheme));
        Msg msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", CommandIssuedSchemeWrapper.build().entityVO(detail));
        return new ResponseEntity<>(msg, HttpStatus.OK);

    }

    /**
     * 分页 命令下发方案
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入commandIssuedScheme")
    public ResponseEntity list(CommandIssuedScheme commandIssuedScheme, Query query) {
        IPage<CommandIssuedScheme> pages = commandIssuedSchemeService.page(Condition.getPage(query), Condition.getQueryWrapper(commandIssuedScheme));
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", CommandIssuedSchemeWrapper.build().pageVO(pages)), HttpStatus.OK);
    }


    /**
     * 自定义分页 命令下发方案
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入commandIssuedScheme")
    public ResponseEntity page(CommandIssuedSchemeVO commandIssuedScheme, Query query) {
        IPage<CommandIssuedSchemeVO> pages = commandIssuedSchemeService.selectCommandIssuedSchemePage(Condition.getPage(query), commandIssuedScheme);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功", pages), HttpStatus.OK);
    }

    /**
     * 新增 命令下发方案
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入commandIssuedScheme")
    public ResponseEntity save(@Valid @RequestBody CommandIssuedScheme commandIssuedScheme) {
        return new ResponseEntity(ReturnMsg.status(commandIssuedSchemeService.save(commandIssuedScheme)), HttpStatus.OK);
    }

    /**
     * 修改 命令下发方案
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入commandIssuedScheme")
    public ResponseEntity update(@Valid @RequestBody CommandIssuedScheme commandIssuedScheme) {
        return new ResponseEntity(ReturnMsg.status(commandIssuedSchemeService.updateById(commandIssuedScheme)), HttpStatus.OK);
    }

    /**
     * 新增或修改 命令下发方案
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入commandIssuedScheme")
    public ResponseEntity submit(@Valid @RequestBody CommandIssuedScheme commandIssuedScheme) {
        return new ResponseEntity(ReturnMsg.status(commandIssuedSchemeService.saveOrUpdate(commandIssuedScheme)), HttpStatus.OK);
    }


    /**
     * 删除 命令下发方案
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public ResponseEntity remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return new ResponseEntity(ReturnMsg.status(commandIssuedSchemeService.deleteLogic(FuncUtil.toLongList(ids))), HttpStatus.OK);
    }


}
