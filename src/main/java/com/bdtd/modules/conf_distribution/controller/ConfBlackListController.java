package com.bdtd.modules.conf_distribution.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.conf_distribution.dto.ConfBlackListDTO;
import com.bdtd.modules.conf_distribution.entity.ConfBlackList;
import com.bdtd.modules.conf_distribution.service.ConfBlackListService;
import com.bdtd.modules.conf_distribution.support.Condition;
import com.bdtd.modules.conf_distribution.support.Query;
import com.bdtd.modules.conf_distribution.vo.ConfBlackListVO;
import com.bdtd.modules.conf_distribution.wrapper.ConfBlackListWrapper;
import com.bdtd.util.FuncUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.bean.BeanUtil;
import com.bdtd.util.web.ReturnMsg;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;
import java.util.Objects;

/**
 * 测点黑名单
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/conf-black-list")
@AllArgsConstructor
@Api(value = "测点黑名单", tags = "测点黑名单", description = "测点黑名单")
public class ConfBlackListController {
    private final ConfBlackListService confBlackListService;


    /**
     * 方法描述: 分页
     *
     * @param confBlackList
     * @param query
     * @return {@link ResponseEntity< IPage< ConfBlackListVO>>}
     * <AUTHOR> @date
     */
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pointId", value = "测点ID", paramType = "query", dataType = "long")
    })
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入confMeasurePoint")
    public ResponseEntity list(@ApiIgnore @RequestParam Map<String, Object> confBlackList, Query query) {
        QueryWrapper<ConfBlackList> queryWrapper = Condition.getQueryWrapper(confBlackList, ConfBlackList.class);
        IPage<ConfBlackList> pages = confBlackListService.page(Condition.getPage(query), queryWrapper);
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功",ConfBlackListWrapper.build().pageVO(pages)),HttpStatus.OK);
    }


    /**
     * 方法描述: 详情
     *
     * @param confBlackListDTO
     * @return {@link ResponseEntity< ConfBlackListVO>}
     * <AUTHOR> @date
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "confBlackListDTO")
    public ResponseEntity detail(ConfBlackListDTO confBlackListDTO) {
        if(FuncUtil.isEmpty(confBlackListDTO.getId())){
            return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "黑名单ID为空",null),HttpStatus.OK);
        }
        ConfBlackList confBlackList = confBlackListService.getOne(Condition.getQueryWrapper(Objects.requireNonNull(BeanUtil.copy(confBlackListDTO, ConfBlackList.class))));
        return new ResponseEntity(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "操作成功",ConfBlackListWrapper.build().entityVO(confBlackList)),HttpStatus.OK);
    }


    /**
     * 方法描述: 新增或修改
     *
     * @param confBlackListDTO
     * @return {@link ResponseEntity}
     * <AUTHOR> @date
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "新增或修改", notes = "传入confBlackListDTO")
    public ResponseEntity submit(@Valid @RequestBody ConfBlackListDTO confBlackListDTO) {
        ConfBlackList data = BeanUtil.copy(confBlackListDTO,ConfBlackList.class);
        data.setIsDeleted(0);
        return new ResponseEntity(ReturnMsg.status(confBlackListService.saveOrUpdate(data)),HttpStatus.OK);
    }

    /**
     * 方法描述: 删除
     *
     * @param ids
     * @return {@link ResponseEntity}
     * <AUTHOR> @date
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public ResponseEntity remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {

        return  new ResponseEntity(ReturnMsg.status(confBlackListService.removeByIds(FuncUtil.toLongList(ids))), HttpStatus.OK);
    }
}
