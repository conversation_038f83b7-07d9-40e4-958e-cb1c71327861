package com.bdtd.modules.conf_distribution.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 *
 * @date
 * @description:
 */
@Data
@ApiModel(value = "用于组态下发 接收参数", description = "用于组态下发 接收参数")
public class ConfDistributionVo {

    /**
     * 32位uuid
     */
    @ApiModelProperty(value = "32位uuid")
    private String id;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private Object value;

    /**
     * 开始点击的时间戳
     */
    @ApiModelProperty(value = "开始点击的时间戳 （long类型）")
    private Long timestamp;

    /**
     * 运行频率可为空
     */
    @ApiModelProperty(value = "运行频率可为空")
    private String frequency;

}
