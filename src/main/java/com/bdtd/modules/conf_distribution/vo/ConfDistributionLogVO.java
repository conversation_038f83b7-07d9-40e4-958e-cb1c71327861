/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.bdtd.modules.conf_distribution.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bdtd.modules.conf_distribution.entity.ConfDistributionLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组态下发日志视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConfDistributionLogVO对象", description = "组态下发日志")
public class ConfDistributionLogVO extends ConfDistributionLog
{
    private static final long serialVersionUID = 1L;

    /** 下发人姓名 */
    @ApiModelProperty(value = "下发人姓名", hidden = true)
    private String createUserName;

    /** 测点名称 */
    @ApiModelProperty(value = "测点名称", hidden = true)
    private String pointName;

    /** 测点类型 */
    @ApiModelProperty(value = "测点类型", hidden = true)
    private Integer pointType;

    /** 耗时最小值 */
    @ApiModelProperty(value = "测点类型", hidden = true)
    @TableField(exist = false)
    private Long elapsedTimeMin;

    /** 耗时最大值 */
    @ApiModelProperty(value = "测点类型", hidden = true)
    @TableField(exist = false)
    private Long elapsedTimeMax;

    /** 测点描述 */
    @ApiModelProperty(value = "测点描述", hidden = true)
    @TableField(exist = false)
    private String describe;

}
