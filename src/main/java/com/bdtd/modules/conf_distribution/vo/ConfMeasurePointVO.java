package com.bdtd.modules.conf_distribution.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点
 */
@Data
public class ConfMeasurePointVO {
    private Long id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 外部测点ID
     */
    @ApiModelProperty(value = "外部测点ID")
    private String outPointId;

    /**
     * 测点类型(开关量，模拟量)
     */
    @ApiModelProperty(value = "测点类型(开关量，模拟量)")
    private Integer type;

    /**
     * 控制类型
     */
    @ApiModelProperty(value = "控制类型(待定)")
    private Integer controlType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;

    /**
     * 转发地址
     */
    @ApiModelProperty(value = "转发地址")
    private String url;
}
