package com.bdtd.modules.conf_distribution.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ControlFrequencyTestVO {
    /**
     * ip
     */
    @ApiModelProperty(value = "ip")
    private String ip;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private Integer port;

    /**
     * 站号
     */
    @ApiModelProperty(value = "站号（slaveId）")
    private Integer stationNum;

    /**
     * 点位
     */
    @ApiModelProperty(value = "点位")
    private Integer address;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private Integer value;

}
