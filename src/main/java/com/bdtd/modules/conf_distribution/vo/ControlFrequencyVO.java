package com.bdtd.modules.conf_distribution.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel(value = "用于组态下发 接收参数", description = "用于组态下发 接收参数")
public class ControlFrequencyVO {
//    /**
//     * 客户端id
//     */
//    @ApiModelProperty(value = "客户端id （可传空）")
//    private Long clientId;

    /**
     * 方案code
     */
    @ApiModelProperty(value = "方案code")
    private String schemeCode;

    /**
     * 点位code
     */
    @ApiModelProperty(value = "点位code")
    private String addressCode;

//    /**
//     * 点位id
//     */
//    @ApiModelProperty(value = "点位id address Id（不可空）")
//    private Long addressId;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private Integer value;

    /**
     * 开始点击的时间戳
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始点击的时间戳（记录开始点击的时间戳，是为了统计下发耗时）")
    private Date startTimeStamp;
}
