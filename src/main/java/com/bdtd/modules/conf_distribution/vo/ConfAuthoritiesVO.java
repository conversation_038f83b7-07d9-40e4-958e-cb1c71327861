package com.bdtd.modules.conf_distribution.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 组态元素权限
 *
 *
 *
 * <AUTHOR>
 *
 * @date
 */
@Data
@ApiModel(value = "ConfAuthoritiesVO对象", description = "组态元素权限")
public class ConfAuthoritiesVO {
    /**
     * 测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private Long pointId;

    /**
     * 测点name
     */
    @ApiModelProperty(value = "测点name")
    private String name;

    /**
     * 用户列表
     */
    @ApiModelProperty(value = "用户列表")
    private List<UserSimpleVO> users;
}
