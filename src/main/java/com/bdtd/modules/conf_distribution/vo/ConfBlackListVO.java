package com.bdtd.modules.conf_distribution.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 测点黑名单
 *
 *
 *
 *
 *
 *
 * @date
 */
@Data
public class ConfBlackListVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private Long pointId;

    /**
     * 测点ID
     */
    @ApiModelProperty(value = "测点ID")
    private String outPointId;

    /**
     * 测点名称
     */
    @ApiModelProperty(value = "测点名称")
    private String name;
}
