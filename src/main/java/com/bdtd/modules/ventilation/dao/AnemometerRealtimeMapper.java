package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.ventilation.entity.AnemometerRealtime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 精准测风系统实时值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface AnemometerRealtimeMapper extends BaseMapper<AnemometerRealtime>
{

    List<AnemometerRealtime> selectBySystemId(@Param("systemId") String systemId);
}
