package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面喷雾系统定义 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkfaceSprayDefinitionMapper extends BaseMapper<WorkfaceSprayDefinition>
{
    List<WorkfaceSprayDefinition> selectBySystemId(@Param("systemId") String systemId);

    List<Map<String, Object>> selectListWithValues(@Param(Constants.WRAPPER) Wrapper<WorkfaceSprayDefinition> wrapper);

    IPage<Map<String, Object>> selectPageWithValues(Page<WorkfaceSprayDefinition> page, @Param(Constants.WRAPPER)Wrapper<WorkfaceSprayDefinition> wrapper);

}
