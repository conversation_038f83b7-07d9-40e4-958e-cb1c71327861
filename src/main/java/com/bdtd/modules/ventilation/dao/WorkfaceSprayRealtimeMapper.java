package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayRealtime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工作面喷雾系统实时值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkfaceSprayRealtimeMapper extends BaseMapper<WorkfaceSprayRealtime>
{
    List<WorkfaceSprayRealtime> selectBySystemId(@Param("systemId") String systemId);
}
