package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测温分区定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface FiberTemperatureDefinitionMapper extends BaseMapper<FiberTemperatureDefinition> {

    List<FiberTemperatureDefinition> selectBySystemId(@Param("sectionCode") String sectionCode);

    List<Map<String, Object>> selectListWithValues(@Param(Constants.WRAPPER) Wrapper<FiberTemperatureDefinition> wrapper);

    IPage<Map<String, Object>> selectPageWithValues(Page<FiberTemperatureDefinition> page, @Param(Constants.WRAPPER)Wrapper<FiberTemperatureDefinition> wrapper);
}
