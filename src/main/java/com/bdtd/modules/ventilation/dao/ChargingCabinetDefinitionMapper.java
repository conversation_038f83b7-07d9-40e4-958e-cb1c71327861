package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 智能充电柜系统定义 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface ChargingCabinetDefinitionMapper extends BaseMapper<ChargingCabinetDefinition>
{

    List<ChargingCabinetDefinition> selectBySystemId(@Param("systemId") String systemId);

    List<Map<String, Object>> selectListWithValues(@Param(Constants.WRAPPER) Wrapper<ChargingCabinetDefinition> wrapper);

    IPage<Map<String, Object>> selectPageWithValues(Page<ChargingCabinetDefinition> page, @Param(Constants.WRAPPER)Wrapper<ChargingCabinetDefinition> wrapper);

}
