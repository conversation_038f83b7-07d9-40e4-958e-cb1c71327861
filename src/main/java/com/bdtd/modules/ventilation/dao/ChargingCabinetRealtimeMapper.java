package com.bdtd.modules.ventilation.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetRealtime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 智能充电架系统实时值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface ChargingCabinetRealtimeMapper extends BaseMapper<ChargingCabinetRealtime>
{
    List<ChargingCabinetRealtime> selectBySystemId(@Param("systemId") String systemId);
}
