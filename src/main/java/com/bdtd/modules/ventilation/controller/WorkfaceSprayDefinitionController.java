package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayDefinition;
import com.bdtd.modules.ventilation.service.IWorkfaceSprayDefinitionService;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 工作面喷雾定义 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/workfaceSprayDefinition")
@Api(value = "工作面喷雾定义", tags = "工作面喷雾定义接口")
public class WorkfaceSprayDefinitionController
{

    private final IWorkfaceSprayDefinitionService workfaceSprayDefinitionService;

    public WorkfaceSprayDefinitionController(IWorkfaceSprayDefinitionService workfaceSprayDefinitionService) {
        this.workfaceSprayDefinitionService = workfaceSprayDefinitionService;
    }

    /**
     * 工作面喷雾定义列表查询
     *
     * @param entity 工作面喷雾定义
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾定义列表查询", notes = "工作面喷雾定义列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkfaceSprayDefinitionList(WorkfaceSprayDefinition entity) {
        Msg msg;
        try {
            List<WorkfaceSprayDefinition> listResult;

            QueryWrapper<WorkfaceSprayDefinition> queryWrapper = Wrappers.query(entity);

            queryWrapper
                    // 未删除
                    .isNull(entity.getDeletedAtIsNull() == null || entity.getDeletedAtIsNull() == 1, "deleted_at")
                    // 已删除
                    .isNotNull(entity.getDeletedAtIsNull() != null && entity.getDeletedAtIsNull() == 0, "deleted_at");

            listResult = workfaceSprayDefinitionService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面喷雾定义列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面喷雾定义分页查询
     *
     * @param page      分页对象
     * @param entity    工作面喷雾定义
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾定义分页查询", notes = "工作面喷雾定义分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkfaceSprayDefinitionPage(PageQuery page, WorkfaceSprayDefinition entity) {
        Msg msg;

        Page<WorkfaceSprayDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    workfaceSprayDefinitionService.selectPage(p, entity)
            );
        }
        catch (Exception e) {
            log.error("工作面喷雾定义分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面喷雾点位定义带实时值查询
     *
     * @param page   分页对象
     * @param entity 工作面喷雾点位定义
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾点位定义带实时值查询", notes = "工作面喷雾点位定义带实时值查询")
    @GetMapping("/listWithValues")
    public ResponseEntity<Msg> getWorkfaceSprayDefinitionWithValues(WorkfaceSprayDefinition entity) {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    workfaceSprayDefinitionService.selectListWithValues(entity)
            );
        }
        catch (Exception e) {
            log.error("工作面喷雾点位定义带实时值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面喷雾点位定义带实时值分页查询
     *
     * @param page   分页对象
     * @param entity 工作面喷雾点位定义
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾点位定义带实时值分页查询", notes = "工作面喷雾点位定义带实时值分页查询")
    @GetMapping("/pageWithValues")
    public ResponseEntity<Msg> getWorkfaceSprayDefinitionWithValuesPage(PageQuery page, WorkfaceSprayDefinition entity) {
        Msg msg;

        Page<WorkfaceSprayDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    workfaceSprayDefinitionService.selectPageWithValues(p, entity)
            );
        }
        catch (Exception e) {
            log.error("工作面喷雾点位定义带实时值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
