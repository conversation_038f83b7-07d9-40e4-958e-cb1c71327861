package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.ChargingCabinetRealtime;
import com.bdtd.modules.ventilation.service.IChargingCabinetRealtimeService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 智能充电柜监测值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/chargingCabinetRealtime")
@Api(value = "智能充电柜监测值", tags = "智能充电柜监测值接口")
public class ChargingCabinetRealtimeController
{

    private final IChargingCabinetRealtimeService chargingCabinetRealtimeService;

    public ChargingCabinetRealtimeController(IChargingCabinetRealtimeService chargingCabinetRealtimeService) {
        this.chargingCabinetRealtimeService = chargingCabinetRealtimeService;
    }

    /**
     * 智能充电柜监测值列表查询
     *
     * @param entity 智能充电柜监测值
     * @return Result
     */
    @ApiOperation(value = "智能充电柜监测值列表查询", notes = "智能充电柜监测值列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getChargingCabinetRealtimeList(ChargingCabinetRealtime entity) {
        Msg msg;
        try {
            List<ChargingCabinetRealtime> listResult;

            QueryWrapper<ChargingCabinetRealtime> queryWrapper = Wrappers.query(entity);

            queryWrapper
                    // 未删除
                    .isNull(entity.getDeletedAtIsNull() == null || entity.getDeletedAtIsNull() == 1, "deleted_at")
                    // 已删除
                    .isNotNull(entity.getDeletedAtIsNull() != null && entity.getDeletedAtIsNull() == 0, "deleted_at");

            listResult = chargingCabinetRealtimeService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("智能充电柜监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 智能充电柜监测值分页查询
     *
     * @param page   分页对象
     * @param entity 智能充电柜监测值
     * @return Result
     */
    @ApiOperation(value = "智能充电柜监测值分页查询", notes = "智能充电柜监测值分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getChargingCabinetRealtimePage(PageQuery page, ChargingCabinetRealtime entity) {
        Msg msg;

        Page<ChargingCabinetRealtime> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    chargingCabinetRealtimeService.selectPage(p, entity)
            );
        }
        catch (Exception e) {
            log.error("智能充电柜监测值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 智能充电柜监测历史值查询
     *
     * @param pointId    点位ID
     * @param startTime  开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime    结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize 采样率, 格式: 1s 或 1m
     * @return Result
     */
    @ApiOperation(value = "智能充电柜监测历史值查询", notes = "智能充电柜监测历史值查询")
    @GetMapping("/historyValues")
    public ResponseEntity<Msg> getChargingCabinetRealtimeHistoryList(@RequestParam(required = true) String pointId, String startTime, String endTime, String polymerize) {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    chargingCabinetRealtimeService.selectHistoryValues(pointId, startTime, endTime, polymerize)
            );
        }
        catch (BadRequestException bre) {
            log.error("智能充电柜监测历史值查询输入错误, " + bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("智能充电柜监测历史值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
