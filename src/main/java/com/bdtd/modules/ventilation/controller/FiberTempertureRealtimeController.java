package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.FiberTemperatureRealtime;
import com.bdtd.modules.ventilation.service.IFiberTemperatureRealtimeService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 光纤测温监测值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/fiberTemperatureRealtime")
@Api(value = "光纤测温监测值", tags = "光纤测温监测值接口")
public class FiberTempertureRealtimeController {

    private final IFiberTemperatureRealtimeService fiberTemperatureRealtimeService;

    public FiberTempertureRealtimeController(IFiberTemperatureRealtimeService fiberTemperatureRealtimeService) {
        this.fiberTemperatureRealtimeService = fiberTemperatureRealtimeService;
    }

    /**
     * 光纤测温监测值列表查询
     *
     * @param entity 光纤测温监测值
     * @return Result
     */
    @ApiOperation(value = "光纤测温监测值列表查询", notes = "光纤测温监测值列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getFiberTemperatureRealtimeList(FiberTemperatureRealtime entity) {

        Msg msg;

        try {
            List<FiberTemperatureRealtime> listResult;

            QueryWrapper<FiberTemperatureRealtime> queryWrapper = Wrappers.query(entity);

            listResult = fiberTemperatureRealtimeService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        } catch (Exception e) {
            log.error("光纤测温监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 光纤测温监测值分页查询
     *
     * @param page   分页对象
     * @param entity 光纤测温监测值
     * @return Result
     */
    @ApiOperation(value = "光纤测温监测值分页查询", notes = "光纤测温监测值分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getFiberTemperatureRealtimePage(PageQuery page, FiberTemperatureRealtime entity) {

        Msg msg;

        Page<FiberTemperatureRealtime> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    fiberTemperatureRealtimeService.selectPage(p, entity)
            );
        } catch (Exception e) {
            log.error("光纤测温监测值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 光纤测温监测历史值查询
     *
     * @param sectionCode 分区编码
     * @param startTime   开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime     结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize  采样率, 格式: 1s 或 1m
     * @return Result
     */
    @ApiOperation(value = "光纤测温监测历史值查询", notes = "光纤测温监测历史值查询")
    @GetMapping("/historyValues")
    public ResponseEntity<Msg> getFiberTemperatureRealtimeHistoryList(@RequestParam(required = true) String sectionCode, String startTime, String endTime, String polymerize) {

        Msg msg;

        try {
            msg = ReturnMsg.success(
                    fiberTemperatureRealtimeService.selectHistoryValues(sectionCode, startTime, endTime, polymerize)
            );
        } catch (BadRequestException bre) {
            log.error("光纤测温监测历史值查询输入错误, " + bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        } catch (Exception e) {
            log.error("光纤测温监测历史值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
