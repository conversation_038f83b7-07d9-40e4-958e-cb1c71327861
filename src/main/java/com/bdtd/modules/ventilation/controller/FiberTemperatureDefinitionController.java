package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.AnemometerDefinition;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;
import com.bdtd.modules.ventilation.service.IFiberTemperatureDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 测温分区定义表 控制器
 *
 * <AUTHOR>
 * @date 2022-11-28
 * @description 测温分区定义表
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/fiberTemperatureDefinition")
@Api(value = "光纤测温分区定义", tags = "光纤测温分区定义接口")
public class FiberTemperatureDefinitionController {

    private final IFiberTemperatureDefinitionService fiberTemperatureDefinitionService;

    public FiberTemperatureDefinitionController(IFiberTemperatureDefinitionService fiberTemperatureDefinitionService) {
        this.fiberTemperatureDefinitionService = fiberTemperatureDefinitionService;
    }

    /**
     * 光纤测温点位定义列表查询
     *
     * @param entity 光纤测温点位定义
     * @return Result
     */
    @ApiOperation(value = "光纤测温点位定义列表查询", notes = "光纤测温点位定义列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getFiberTemperatureDefinitionList(FiberTemperatureDefinition entity) {

        Msg msg;
        try {
            List<FiberTemperatureDefinition> listResult = fiberTemperatureDefinitionService.list(Wrappers.query(entity));
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", listResult);
        } catch (Exception e) {
            log.error("光纤测温点位定义列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param entity 光纤测温点位定义
     * @return Result
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getFiberTemperatureDefinitionPage(PageQuery page, FiberTemperatureDefinition entity) {

        Msg msg;

        Page<AnemometerDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    fiberTemperatureDefinitionService.selectPage(p, entity)
            );
        } catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 光纤测温点位定义带实时值查询
     *
     * @param entity 光纤测温点位定义
     * @return Result
     */
    @ApiOperation(value = "光纤测温点位定义带实时值查询", notes = "光纤测温点位定义带实时值查询")
    @GetMapping("/listWithValues")
    public ResponseEntity<Msg> getFiberTemperatureDefinitionWithValues(FiberTemperatureDefinition entity) {

        Msg msg;

        try {
            msg = ReturnMsg.success(
                    fiberTemperatureDefinitionService.selectListWithValues(entity)
            );
        } catch (Exception e) {
            log.error("光纤测温点位定义带实时值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 光纤测温点位定义带实时值分页查询
     *
     * @param page   分页对象
     * @param entity 光纤测温点位定义
     * @return Result
     */
    @ApiOperation(value = "光纤测温点位定义带实时值分页查询", notes = "光纤测温点位定义带实时值分页查询")
    @GetMapping("/pageWithValues")
    public ResponseEntity<Msg> getFiberTemperatureDefinitionWithValuesPage(PageQuery page, FiberTemperatureDefinition entity) {

        Msg msg;

        Page<FiberTemperatureDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    fiberTemperatureDefinitionService.selectPageWithValues(p, entity)
            );
        } catch (Exception e) {
            log.error("光纤测温点位定义带实时值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
