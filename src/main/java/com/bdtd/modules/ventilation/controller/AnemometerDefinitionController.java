package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.AnemometerDefinition;
import com.bdtd.modules.ventilation.service.IAnemometerDefinitionService;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 精准测风点位定义 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/anemometerDefinition")
@Api(value = "精准测风点位定义", tags = "精准测风点位定义接口")
public class AnemometerDefinitionController
{

    private final IAnemometerDefinitionService anemometerDefinitionService;

    public AnemometerDefinitionController(IAnemometerDefinitionService anemometerDefinitionService) {
        this.anemometerDefinitionService = anemometerDefinitionService;
    }

    /**
     * 精准测风点位定义列表查询
     *
     * @param entity 精准测风点位定义
     * @return Result
     */
    @ApiOperation(value = "精准测风点位定义列表查询", notes = "精准测风点位定义列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getAnemometerDefinitionList(AnemometerDefinition entity) {
        Msg msg;
        try {
            List<AnemometerDefinition> listResult;

            QueryWrapper<AnemometerDefinition> queryWrapper = Wrappers.query(entity);

            queryWrapper
                    // 未删除
                    .isNull(entity.getDeletedAtIsNull() == null || entity.getDeletedAtIsNull() == 1, "deleted_at")
                    // 已删除
                    .isNotNull(entity.getDeletedAtIsNull() != null && entity.getDeletedAtIsNull() == 0, "deleted_at");

            listResult = anemometerDefinitionService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("精准测风点位定义列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 精准测风点位定义分页查询
     *
     * @param page   分页对象
     * @param entity 精准测风点位定义
     * @return Result
     */
    @ApiOperation(value = "精准测风点位定义分页查询", notes = "精准测风点位定义分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getAnemometerDefinitionPage(PageQuery page, AnemometerDefinition entity) {
        Msg msg;

        Page<AnemometerDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    anemometerDefinitionService.selectPage(p, entity)
            );
        }
        catch (Exception e) {
            log.error("精准测风点位定义分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 精准测风点位定义带实时值查询
     *
     * @param entity 精准测风点位定义
     * @return Result
     */
    @ApiOperation(value = "精准测风点位定义带实时值查询", notes = "精准测风点位定义带实时值查询")
    @GetMapping("/listWithValues")
    public ResponseEntity<Msg> getAnemometerDefinitionWithValues(AnemometerDefinition entity) {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    anemometerDefinitionService.selectListWithValues(entity)
            );
        }
        catch (Exception e) {
            log.error("精准测风点位定义带实时值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 精准测风点位定义带实时值分页查询
     *
     * @param page   分页对象
     * @param entity 精准测风点位定义
     * @return Result
     */
    @ApiOperation(value = "精准测风点位定义带实时值分页查询", notes = "精准测风点位定义带实时值分页查询")
    @GetMapping("/pageWithValues")
    public ResponseEntity<Msg> getAnemometerDefinitionWithValuesPage(PageQuery page, AnemometerDefinition entity) {
        Msg msg;

        Page<AnemometerDefinition> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    anemometerDefinitionService.selectPageWithValues(p, entity)
            );
        }
        catch (Exception e) {
            log.error("精准测风点位定义带实时值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
