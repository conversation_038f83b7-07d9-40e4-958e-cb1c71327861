package com.bdtd.modules.ventilation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayRealtime;
import com.bdtd.modules.ventilation.service.IWorkfaceSprayRealtimeService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.page.PageQuery;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 工作面喷雾监测值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/ventilation/workfaceSprayRealtime")
@Api(value = "工作面喷雾监测值", tags = "工作面喷雾监测值接口")
public class WorkfaceSprayRealtimeController
{

    private final IWorkfaceSprayRealtimeService workfaceSprayRealtimeService;

    public WorkfaceSprayRealtimeController(IWorkfaceSprayRealtimeService workfaceSprayRealtimeService) {
        this.workfaceSprayRealtimeService = workfaceSprayRealtimeService;
    }

    /**
     * 工作面喷雾监测值列表查询
     *
     * @param entity 工作面喷雾监测值
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾监测值列表查询", notes = "工作面喷雾监测值列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkfaceSprayRealtimeList(WorkfaceSprayRealtime entity) {
        Msg msg;
        try {
            List<WorkfaceSprayRealtime> listResult;

            QueryWrapper<WorkfaceSprayRealtime> queryWrapper = Wrappers.query(entity);

            queryWrapper
                    // 未删除
                    .isNull(entity.getDeletedAtIsNull() == null || entity.getDeletedAtIsNull() == 1, "deleted_at")
                    // 已删除
                    .isNotNull(entity.getDeletedAtIsNull() != null && entity.getDeletedAtIsNull() == 0, "deleted_at");

            listResult = workfaceSprayRealtimeService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面喷雾监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面喷雾监测值分页查询
     *
     * @param page      分页对象
     * @param entity    工作面喷雾监测值
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾监测值分页查询", notes = "工作面喷雾监测值分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkfaceSprayRealtimePage(PageQuery page, WorkfaceSprayRealtime entity) {
        Msg msg;

        Page<WorkfaceSprayRealtime> p = new Page<>();
        p.setCurrent(null != page.getPageNum() ? page.getPageNum() : 0);
        p.setSize(null != page.getPageSize() ? page.getPageSize() : 10);

        try {
            msg = ReturnMsg.success(
                    workfaceSprayRealtimeService.selectPage(p, entity)
            );
        }
        catch (Exception e) {
            log.error("工作面喷雾监测值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面喷雾监测历史值查询
     *
     * @param pointId 设备编码
     * @param startTime  开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime    结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize 采样率, 格式: 1s 或 1m
     * @return Result
     */
    @ApiOperation(value = "工作面喷雾监测历史值查询", notes = "工作面喷雾监测历史值查询")
    @GetMapping("/historyValues")
    public ResponseEntity<Msg> getWorkfaceSprayRealtimeHistoryList(@RequestParam(required = true) String pointId, String startTime, String endTime, String polymerize) {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    workfaceSprayRealtimeService.selectHistoryValues(pointId, startTime, endTime, polymerize)
            );
        }
        catch (BadRequestException bre) {
            log.error("工作面喷雾监测历史值查询输入错误, " + bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("工作面喷雾监测历史值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
