package com.bdtd.modules.ventilation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面喷雾系统实施值
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkfaceSprayRealtime implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 设备编码 */
    @ApiModelProperty(value = "设备编码")
    @TableId
    private String pointId;

    /** 工作模式 0000:自动, 0001:闭锁, 0002:手动 */
    @ApiModelProperty(value = "工作模式 0000:自动, 0001:闭锁, 0002:手动")
    private String workMode;

    /** 工作模式描述 */
    @ApiModelProperty(value = "工作模式描述")
    private String workModeText;

    /** 通讯模式, 0000:RS485通讯, 0001:WIFI通讯, 0002:以太网通讯 */
    @ApiModelProperty(value = "通讯模式, 0000:RS485通讯, 0001:WIFI通讯, 0002:以太网通讯")
    private String connectMode;

    /** 通讯模式描述 */
    @ApiModelProperty(value = "通讯模式描述")
    private String connectModeText;

    /** 设备连接状态, 0000:未连接, 0001:已连接 */
    @ApiModelProperty(value = "设备连接状态, 0000:未连接, 0001:已连接")
    private String connectStatus;

    /** 设备连接状态描述 */
    @ApiModelProperty(value = "设备连接状态描述")
    private String connectStatusText;

    /** 喷雾状态, FF00:开启, 0000:关闭 */
    @ApiModelProperty(value = "喷雾状态, FF00:开启, 0000:关闭")
    private String waterStatus;

    /** 喷雾状态描述 */
    @ApiModelProperty(value = "喷雾状态描述")
    private String waterStatusText;

    /** 排污状态, FF00:开启, 0000:关闭 */
    @ApiModelProperty(value = "排污状态, FF00:开启, 0000:关闭")
    private String airStatus;

    /** 排污状态描述 */
    @ApiModelProperty(value = "排污状态描述")
    private String airStatusText;

    /** 温度值 */
    @ApiModelProperty(value = "温度值")
    private String temperature;

    /** 湿度值 */
    @ApiModelProperty(value = "湿度值")
    private String humidity;

    /** 粉尘浓度 */
    @ApiModelProperty(value = "粉尘浓度")
    private String dustConcentration;

    /** 水压值 */
    @ApiModelProperty(value = "水压值")
    private String waterPressure;

    /** 气压值 */
    @ApiModelProperty(value = "气压值")
    private String airPressure;

    /** 支架主机数量 */
    @ApiModelProperty(value = "支架主机数量")
    private String holderCount;

    /** 支架状态-二进制, 0:关闭, 1:开启 */
    @ApiModelProperty(value = "支架状态-二进制, 0:关闭, 1:开启")
    private String holderStatus;

    /** 支架延时 */
    @ApiModelProperty(value = "支架延时")
    private String holderDelayed;

    /** 支架联动数量 */
    @ApiModelProperty(value = "支架联动数量")
    private String holderLinkCount;

    /** 主机所在支架的位置 */
    @ApiModelProperty(value = "主机所在支架的位置")
    private String holderLocation;

    /** 应用场景, 0000:巷道喷雾, 0001:综采喷雾, 0002:放炮喷雾, 0003:风水联动; 0004:水汽分配 */
    @ApiModelProperty(value = "应用场景, 0000:巷道喷雾, 0001:综采喷雾, 0002:放炮喷雾, 0003:风水联动; 0004:水汽分配")
    private String appScene;

    /** 应用场景描述 */
    @ApiModelProperty(value = "应用场景描述")
    private String appSceneText;

    /** 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    /** 是否软删除 */
    @TableField(exist = false)
    private Integer deletedAtIsNull;

}
