package com.bdtd.modules.ventilation.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 精准测风系统定义
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AnemometerDefinition implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 装置编码 */
    @ApiModelProperty(value = "装置编码")
    @TableId
    private String deviceCode;

    /** 装置编码 */
    @ApiModelProperty(value = "装置名称")
    private String deviceName;

    /** 安装位置 */
    @ApiModelProperty(value = "安装位置")
    private String installPos;

    /** 风速单位：m/s(-99.99时，异常) */
    @ApiModelProperty(value = "风速单位：m/s(-99.99时，异常)")
    private String windSpeedUnit;

    /** 温度单位：℃(大于80°时，异常) */
    @ApiModelProperty(value = "温度单位：℃(大于80°时，异常)")
    private String temperatureUnit;

    /** 横截面积单位：平方米 */
    @ApiModelProperty(value = "横截面积单位：平方米")
    private String transverseAreaUnit;

    /** 风量单位：立方米/分钟 */
    @ApiModelProperty(value = "风量单位：立方米/分钟")
    private String windAmountUnit;

    /** 湿度单位：% */
    @ApiModelProperty(value = "湿度单位：%")
    private String humidityUnit;

    /** 气压单位：Pa */
    @ApiModelProperty(value = "气压单位：Pa")
    private String barometricUnit;

    /** 设备描述 */
    @ApiModelProperty(value = "设备描述")
    private String memo;

    /** 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private Timestamp deletedAt;

    /** 是否软删除 */
    @TableField(exist = false)
    private Integer deletedAtIsNull;

}
