package com.bdtd.modules.ventilation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 测温分区温度报警表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="FiberTemperatureAlarmRealtime对象", description="测温分区温度报警表")
public class FiberTemperatureAlarmRealtime implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "矿编码,矿编码")
    private String mineCode;

    @ApiModelProperty(value = "矿名称,矿名称")
    private String mineName;

    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    @ApiModelProperty(value = "集团名称")
    private String groupName;

    @ApiModelProperty(value = "分区编码")
    private String sectionCode;

    @ApiModelProperty(value = "报警点在分区内的位置")
    private String alarmPoint;

    @ApiModelProperty(value = "报警点在通道内的位置")
    private Integer fiberPoint;

    @ApiModelProperty(value = "报警开始时间")
    private Timestamp startTime;

    @ApiModelProperty(value = "报警结束时间")
    private Timestamp endTime;

    @ApiModelProperty(value = "报警期间实时温度")
    private Double alarmTemp;

    @ApiModelProperty(value = "报警期间最大值")
    private Double alarmMax;

    @ApiModelProperty(value = "最大值时间")
    private Timestamp maxTime;

    @ApiModelProperty(value = "报警期间最小值")
    private Double alarmMin;

    @ApiModelProperty(value = "最小值时间")
    private Timestamp minTime;

    @ApiModelProperty(value = "报警期间平均值")
    private Double alarmAvg;

    @ApiModelProperty(value = "报警级别；1 橙色报警，2 红色报警")
    private String alarmType;

    @ApiModelProperty(value = "报警级别解释字段")
    private String alarmTypeText;

    @ApiModelProperty(value = "采样次数(多次报警为有效数据)")
    private Integer sample;

    @ApiModelProperty(value = "数据变化时间")
    private Timestamp dataTime;

    @ApiModelProperty(value = "数据采集时间")
    private Timestamp collectTime;
}
