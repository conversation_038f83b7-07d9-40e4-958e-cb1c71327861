package com.bdtd.modules.ventilation.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面喷雾系统定义
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkfaceSprayDefinition implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 设备编码 */
    @ApiModelProperty(value = "设备编码")
    @TableId
    private String pointId;

    /** 设备ID */
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 设备IP */
    @ApiModelProperty(value = "设备IP")
    private String deviceIp;

    /** 数据类型, int float */
    @ApiModelProperty(value = "数据类型, int float")
    private String dataType;

    /** 设备描述 */
    @ApiModelProperty(value = "设备描述")
    private String memo;

    /** 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private Timestamp deletedAt;

    /** 是否软删除 */
    @TableField(exist = false)
    private Integer deletedAtIsNull;

}
