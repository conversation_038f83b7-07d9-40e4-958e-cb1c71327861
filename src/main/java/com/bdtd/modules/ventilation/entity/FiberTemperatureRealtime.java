package com.bdtd.modules.ventilation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="FiberTemperatureRealtime对象", description="")
public class FiberTemperatureRealtime implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    @ApiModelProperty(value = "矿名称")
    private String mineName;

    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    @ApiModelProperty(value = "集团名称")
    private String groupName;

    @ApiModelProperty(value = "分区编码")
    private String sectionCode;

    @ApiModelProperty(value = "温度字符串")
    private String tempStr;

    @ApiModelProperty(value = "分区最高温度")
    private Double tempMax;

    @ApiModelProperty(value = "分区最低温度")
    private Double tempMin;

    @ApiModelProperty(value = "分区平均温度")
    private Double tempAvg;

    @ApiModelProperty(value = "分区最高温度位置")
    private Double tempMaxPosition;

    @ApiModelProperty(value = "分区最低温度位置")
    private Double tempMinPosition;

    @ApiModelProperty(value = "报警状态;0 正常，1 橙色报警，2 红色报警")
    private String alarmFlag;

    @ApiModelProperty(value = "报警状态解释字段")
    private String alarmFlagText;

    @ApiModelProperty(value = "数据变化时间")
    private Timestamp dataTime;

    @ApiModelProperty(value = "数据采集时间")
    private Timestamp collectTime;
}
