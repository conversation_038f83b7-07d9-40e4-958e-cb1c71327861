package com.bdtd.modules.ventilation.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 智能充电柜定义
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charging_cabinet_definition")
public class ChargingCabinetDefinition implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 充电柜称号.柜门号 */
    @ApiModelProperty(value = "充电柜称号.柜门号")
    @TableId
    private String pointId;

    /** 智能充电柜系统号 */
    @ApiModelProperty(value = "智能充电柜系统号")
    private String standCode;

    /** 柜门号 */
    @ApiModelProperty(value = "柜门号")
    private String doorCode;

    /** 员工号 */
    @ApiModelProperty(value = "员工号")
    private String personCard;

    /** 钥匙卡号 */
    @ApiModelProperty(value = "钥匙卡号")
    private String keyCardCode;

    /** 矿灯配备时间 */
    @ApiModelProperty(value = "矿灯配备时间")
    private Timestamp lampEquipTime;

    /** 矿灯充电次数 */
    @ApiModelProperty(value = "矿灯充电次数")
    private Integer lampChargeTimes;

    /** 定位号 */
    @ApiModelProperty(value = "定位号")
    private String locateCard;

    /** 名字 */
    @ApiModelProperty(value = "名字")
    private String name;

    /** 部门 */
    @ApiModelProperty(value = "部门")
    private String department;

    /** 工种 */
    @ApiModelProperty(value = "工种")
    private String workKind;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /** 个人照片 */
    @ApiModelProperty(value = "个人照片")
    private String profilePhoto;

    /** 背景照片 */
    @ApiModelProperty(value = "背景照片")
    private String bgPhoto;

    /** 设备描述 */
    @ApiModelProperty(value = "设备描述")
    private String memo;

    /** 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private Timestamp deletedAt;

    /** 是否软删除 */
    @TableField(exist = false)
    private Integer deletedAtIsNull;

}
