package com.bdtd.modules.ventilation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 智能充电柜实时
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charging_cabinet_realtime")
public class ChargingCabinetRealtime
{

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 充电柜称号.柜门号 */
    @ApiModelProperty(value = "充电柜称号.柜门号")
    @TableId
    private String pointId;

    /** 充电柜称号 */
    @ApiModelProperty(value = "充电柜称号")
    private String standCode;

    /** 柜门号 */
    @ApiModelProperty(value = "柜门号")
    private String doorCode;

    /** 员工号 */
    @ApiModelProperty(value = "员工号")
    private String personCard;

    /** 门的状态 0：门关闭，1：门打开，2：通信故障 */
    @ApiModelProperty(value = "门的状态 0：门关闭，1：门打开，2：通信故障")
    private String doorState;

    /** 门的状态描述 */
    @ApiModelProperty(value = "门的状态描述")
    private String doorStateText;

    /** 灯的状态 0：下架，1：充电中，2：充满，3：充电故障 */
    @ApiModelProperty(value = "灯的状态 0：下架，1：充电中，2：充满，3：充电故障")
    private String lampState;

    /** 灯的状态描述 */
    @ApiModelProperty(value = "灯的状态描述")
    private String lampStateText;

    /** 矿灯上架时间 */
    @ApiModelProperty(value = "矿灯上架时间")
    private Timestamp lampOnTime;

    /** 矿灯下架时间 */
    @ApiModelProperty(value = "矿灯下架时间")
    private Timestamp lampOffTime;

    /** 状态变化时间   0、1、2、3状态变化的时间 */
    @ApiModelProperty(value = "状态变化时间   0、1、2、3状态变化的时间")
    private Timestamp lampChangeTime;

    /** 自救器状态  0：离架，1：在架 */
    @ApiModelProperty(value = "自救器状态  0：离架，1：在架")
    private String selfRescuerState;

    /** 自救器状态描述 */
    @ApiModelProperty(value = "自救器状态描述")
    private String selfRescuerStateText;

    /** 充电次数 */
    @ApiModelProperty(value = "充电次数")
    private Long chargeCount;

    /** 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    /** 是否软删除 */
    @TableField(exist = false)
    private Integer deletedAtIsNull;

}
