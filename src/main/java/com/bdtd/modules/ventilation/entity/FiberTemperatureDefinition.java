package com.bdtd.modules.ventilation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 测温分区定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="FiberTemperatureDefinition对象", description="测温分区定义表")
public class FiberTemperatureDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "矿编码,矿编码")
    private String mineCode;

    @ApiModelProperty(value = "矿名称,矿名称")
    private String mineName;

    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    @ApiModelProperty(value = "集团名称")
    private String groupName;

    @ApiModelProperty(value = "分区编码")
    private String sectionCode;

    @ApiModelProperty(value = "分区名称")
    private String sectionName;

    @ApiModelProperty(value = "光缆起点")
    private Integer fiberStart;

    @ApiModelProperty(value = "光缆终点")
    private Integer fiberEnd;

    @ApiModelProperty(value = "光缆长度")
    private Integer fiberLength;

    @ApiModelProperty(value = "通道长度变化，配合channel_upflag使用")
    private Integer changeLength;

    @ApiModelProperty(value = "橙色警报线")
    private Double orangeWarnLimit;

    @ApiModelProperty(value = "红色警报线")
    private Double redWarnLimit;

    @ApiModelProperty(value = "分站编码")
    private String substationCode;

    @ApiModelProperty(value = "分站名称")
    private String substationName;

    @ApiModelProperty(value = "分站ip地址")
    private String substationIp;

    @ApiModelProperty(value = "分站安装位置")
    private String substationLocation;

    @ApiModelProperty(value = "分站连接状态，0正常，-1断开")
    private String substationState;

    @ApiModelProperty(value = "通道编码")
    private String channelCode;

    @ApiModelProperty(value = "通道名称")
    private String channelName;

    @ApiModelProperty(value = "通道更新状态；0 长度正常，-1长度减少，1长度增加")
    private String channelUpflag;

    @ApiModelProperty(value = "数据变化时间")
    private Timestamp dataTime;

    @ApiModelProperty(value = "数据采集时间")
    private Timestamp collectTime;
}
