package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.ChargingCabinetDefinitionMapper;
import com.bdtd.modules.ventilation.dao.ChargingCabinetRealtimeMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.ventilation.entity.ChargingCabinetRealtime;
import com.bdtd.modules.ventilation.service.IChargingCabinetRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 智能充电柜系统实时数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Slf4j
@Service
public class ChargingCabinetRealtimeServiceImpl extends ServiceImpl<ChargingCabinetRealtimeMapper, ChargingCabinetRealtime>
    implements IChargingCabinetRealtimeService
{
    private final ChargingCabinetDefinitionMapper chargingCabinetDefinitionMapper;
    private final ChargingCabinetRealtimeMapper chargingCabinetRealtimeMapper;
    private final TaosConnector taosConnector;

    public ChargingCabinetRealtimeServiceImpl(
        ChargingCabinetDefinitionMapper chargingCabinetDefinitionMapper,
        ChargingCabinetRealtimeMapper chargingCabinetRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.chargingCabinetDefinitionMapper = chargingCabinetDefinitionMapper;
        this.chargingCabinetRealtimeMapper = chargingCabinetRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<ChargingCabinetRealtime> selectPage(Page page, ChargingCabinetRealtime queryEntity) {
        QueryWrapper<ChargingCabinetRealtime> query = Wrappers.query(queryEntity);
        return chargingCabinetRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(@RequestParam(required = true) String pointId, String startTime, String endTime, String polymerize)
        throws Exception {

        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            throw new BadRequestException("聚合度无效");
        }

        // 检查点位是否存在
        if (StringUtil.isNotEmpty(pointId)) {
            QueryWrapper<ChargingCabinetDefinition> queryWrap = new QueryWrapper<>();
            queryWrap.eq("point_id", pointId);
            int existedCount = chargingCabinetDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的点位不存在");
            }
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select").append(" ");
        buffer.append("`point_id`, "
                      + "last(`door_state`) as `door_state`, "
                      + "last(`door_state_text`) as `door_state_text`, "
                      + "last(`lamp_state`) as `lamp_state`, "
                      + "last(`lamp_state_text`) as `lamp_state_text`, "
                      + "last(`lamp_on_time`) as `lamp_on_time`, "
                      + "last(`lamp_off_time`) as `lamp_off_time`, "
                      + "last(`lamp_change_time`) as `lamp_change_time`, "
                      + "last(`self_rescuer_state`) as `self_rescuer_state`, "
                      + "last(`self_rescuer_state_text`) as `self_rescuer_state_text` "
        );
        buffer.append("from `charging_cabinet`").append(" ");
        buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");

        if (StringUtil.isNotEmpty(pointId)) {
            // buffer.append("and `point_id` in ('").append(String.join("','", pointIds)).append("') ");
            buffer.append("and `point_id` = '").append(pointId).append("' ");
        }

        buffer.append("interval(").append(polymerize).append(") ");
        buffer.append("group by `point_id` ");
        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            mapList = taosConnector.query(sql);
            // if (mapList != null) {
            //     for (Map<String, Object> map : mapList) {
            //         String pId = (String) map.get("point_id");
            //         retMap.computeIfAbsent(pId, k -> new ArrayList<>());
            //
            //         List<Map<String, Object>> list = retMap.get(pId);
            //         list.add(map);
            //         retMap.put(pId, list);
            //     }
            // }
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: {}", ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        // return retMap.values().stream().collect(Collectors.toList());
        return mapList;
    }

}
