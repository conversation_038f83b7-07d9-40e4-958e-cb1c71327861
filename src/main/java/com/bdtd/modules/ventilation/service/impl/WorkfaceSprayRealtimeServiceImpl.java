package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.WorkfaceSprayDefinitionMapper;
import com.bdtd.modules.ventilation.dao.WorkfaceSprayRealtimeMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayDefinition;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayRealtime;
import com.bdtd.modules.ventilation.service.IWorkfaceSprayRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面喷雾系统实时监测 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
public class WorkfaceSprayRealtimeServiceImpl extends ServiceImpl<WorkfaceSprayRealtimeMapper, WorkfaceSprayRealtime> implements IWorkfaceSprayRealtimeService
{
    private final WorkfaceSprayDefinitionMapper workfaceSprayDefinitionMapper;
    private final WorkfaceSprayRealtimeMapper workfaceSprayRealtimeMapper;
    private final TaosConnector taosConnector;

    public WorkfaceSprayRealtimeServiceImpl(
        WorkfaceSprayDefinitionMapper workfaceSprayDefinitionMapper,
        WorkfaceSprayRealtimeMapper workfaceSprayRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.workfaceSprayDefinitionMapper = workfaceSprayDefinitionMapper;
        this.workfaceSprayRealtimeMapper = workfaceSprayRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<WorkfaceSprayRealtime> selectPage(Page page, WorkfaceSprayRealtime queryEntity) {
        QueryWrapper<WorkfaceSprayRealtime> query = Wrappers.query(queryEntity);
        return workfaceSprayRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(@RequestParam(required = true) String pointId, String startTime, String endTime, String polymerize)
        throws Exception {
        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            throw new BadRequestException("聚合度无效");
        }

        // 检查点位是否存在
        if (StringUtil.isNotEmpty(pointId)) {
            QueryWrapper<WorkfaceSprayDefinition> queryWrap = new QueryWrapper<>();
            queryWrap.eq("point_id", pointId);
            int existedCount = workfaceSprayDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的点位不存在");
            }
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select").append(" ");
        buffer.append("`point_id`, "
                      + "last(`work_mode`) as `work_mode`, "
                      + "last(`work_mode_text`) as `work_mode_text`, "
                      + "last(`connect_mode`) as `connect_mode`, "
                      + "last(`connect_mode_text`) as `connect_mode_text`, "
                      + "last(`connect_status`) as `connect_status`, "
                      + "last(`connect_status_text`) as `connect_status_text`, "
                      + "last(`water_status`) as `water_status`, "
                      + "last(`water_status_text`) as `water_status_text`, "
                      + "last(`air_status`) as `air_status`, "
                      + "last(`air_status_text`) as `air_status_text`, "
                      + "last(`temperature`) as `temperature`, "
                      + "last(`humidity`) as `humidity`, "
                      + "last(`dust_concentration`) as `dust_concentration`, "
                      + "last(`water_pressure`) as `water_pressure`, "
                      + "last(`air_pressure`) as `air_pressure`, "
                      + "last(`holder_count`) as `holder_count`, "
                      + "last(`holder_status`) as `holder_status`, "
                      + "last(`holder_delayed`) as `holder_delayed`, "
                      + "last(`holder_link_count`) as `holder_link_count`, "
                      + "last(`holder_location`) as `holder_location`, "
                      + "last(`app_scene`) as `app_scene`, "
                      + "last(`app_scene_text`) as `app_scene_text` "
        );
        buffer.append("from `workface_spray`").append(" ");
        buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");

        if (StringUtil.isNotEmpty(pointId)) {
            // buffer.append("and `point_id` in ('").append(String.join("','", pointIds)).append("') ");
            buffer.append("and `point_id` = '").append(pointId).append("' ");
        }

        buffer.append("interval(").append(polymerize).append(") ");
        buffer.append("group by `point_id` ");
        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            mapList = taosConnector.query(sql);
            // if (mapList != null) {
            //     for (Map<String, Object> map : mapList) {
            //         String pId = (String) map.get("point_id");
            //         retMap.computeIfAbsent(pId, k -> new ArrayList<>());
            //
            //         List<Map<String, Object>> list = retMap.get(pId);
            //         list.add(map);
            //         retMap.put(pId, list);
            //     }
            // }
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: " + ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        // return retMap.values().stream().collect(Collectors.toList());
        return mapList;
    }

}
