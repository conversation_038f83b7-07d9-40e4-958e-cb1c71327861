package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测温分区定义表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface IFiberTemperatureDefinitionService extends IService<FiberTemperatureDefinition> {

    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<FiberTemperatureDefinition> selectPage(Page page, FiberTemperatureDefinition queryEntity);

    /**
     * 查询列表带实时值
     * @param queryEntity   查询参数
     * @return /
     */
    List<Map<String, Object>> selectListWithValues(FiberTemperatureDefinition queryEntity);

    /**
     * 查询分页带实时值
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    IPage<Map<String, Object>> selectPageWithValues(Page<FiberTemperatureDefinition> page, FiberTemperatureDefinition queryEntity);
}
