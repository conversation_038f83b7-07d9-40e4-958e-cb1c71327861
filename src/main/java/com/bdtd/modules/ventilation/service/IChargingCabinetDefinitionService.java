package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 智能充电柜系统定义数据 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IChargingCabinetDefinitionService extends IService<ChargingCabinetDefinition>
{

    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<ChargingCabinetDefinition> selectPage(Page page, ChargingCabinetDefinition queryEntity);

    /**
     * 查询列表带实时值
     * @param queryEntity   查询参数
     * @return /
     */
    List<Map<String, Object>> selectListWithValues(ChargingCabinetDefinition queryEntity);

    /**
     * 查询分页带实时值
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    IPage<Map<String, Object>> selectPageWithValues(Page<ChargingCabinetDefinition> page, ChargingCabinetDefinition queryEntity);
}
