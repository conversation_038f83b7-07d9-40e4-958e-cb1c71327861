package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.FiberTemperatureAlarmRealtimeMapper;
import com.bdtd.modules.ventilation.dao.FiberTemperatureDefinitionMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.ventilation.entity.FiberTemperatureAlarmRealtime;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;
import com.bdtd.modules.ventilation.service.IFiberTemperatureAlarmRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测温分区温度报警表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Service
public class FiberTemperatureAlarmRealtimeServiceImpl extends ServiceImpl<FiberTemperatureAlarmRealtimeMapper, FiberTemperatureAlarmRealtime>
    implements IFiberTemperatureAlarmRealtimeService
{
    private final FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper;
    private final FiberTemperatureAlarmRealtimeMapper fiberTemperatureAlarmRealtimeMapper;
    private final TaosConnector taosConnector;

    public FiberTemperatureAlarmRealtimeServiceImpl(
        FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper,
        FiberTemperatureAlarmRealtimeMapper fiberTemperatureAlarmRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.fiberTemperatureDefinitionMapper = fiberTemperatureDefinitionMapper;
        this.fiberTemperatureAlarmRealtimeMapper = fiberTemperatureAlarmRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<FiberTemperatureAlarmRealtime> selectPage(Page<FiberTemperatureAlarmRealtime> page, FiberTemperatureAlarmRealtime queryEntity) {

        QueryWrapper<FiberTemperatureAlarmRealtime> query = Wrappers.query(queryEntity);
        return fiberTemperatureAlarmRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(String sectionCode, String alarmPoint, String startTime, String endTime, String polymerize)
        throws Exception {

        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            throw new BadRequestException("聚合度无效");
        }

        if (StringUtil.isNotEmpty(sectionCode)) {
            QueryWrapper<FiberTemperatureDefinition> queryWrap = new QueryWrapper<>();
            queryWrap.eq("section_code", sectionCode);
            int existedCount = fiberTemperatureDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的分区编码不存在");
            }
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select").append(" ");
        buffer.append("`section_code`, `alarm_point`, "
                      + "last(`alarm_temp`) as `alarm_temp`, "
                      + "last(`alarm_max`) as `alarm_max`, "
                      + "last(`alarmTypeText`) as `alarmTypeText`, "
                      + "last(`sample`) as `sample`, "
                      + "last(`start_time`) as `start_time`, "
                      + "last(`end_time`) as `end_time` "
        );
        buffer.append("from `fiber_temperature_alarm`").append(" ");
        // 开始、结束时间已做检查，避免SQL注入风险
        buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");

        if (StringUtil.isNotEmpty(sectionCode)) {
            buffer.append("and `section_code` = '").append(sectionCode).append("' ");
        }
        // if (StringUtil.isNotEmpty(alarmPoint)) {
        //     buffer.append("and `alarm_point` = '").append(alarmPoint).append("' ");
        // }

        buffer.append("interval(").append(polymerize).append(") ");
        buffer.append("group by `section_code`, `alarm_point` ");
        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;

        try {
            mapList = taosConnector.query(sql);
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: " + ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        return mapList;
    }
}
