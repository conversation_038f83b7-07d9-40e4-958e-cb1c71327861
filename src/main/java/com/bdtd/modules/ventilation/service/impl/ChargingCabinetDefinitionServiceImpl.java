package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.ChargingCabinetDefinitionMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.ventilation.service.IChargingCabinetDefinitionService;
import com.bdtd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 智能充电柜系统定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Slf4j
@Service
public class ChargingCabinetDefinitionServiceImpl extends ServiceImpl<ChargingCabinetDefinitionMapper, ChargingCabinetDefinition> implements IChargingCabinetDefinitionService
{
    private final ChargingCabinetDefinitionMapper chargingCabinetDefinitionMapper;

    public ChargingCabinetDefinitionServiceImpl(ChargingCabinetDefinitionMapper chargingCabinetDefinitionMapper) {
        this.chargingCabinetDefinitionMapper = chargingCabinetDefinitionMapper;
    }

    @Override
    public Page<ChargingCabinetDefinition> selectPage(Page page, ChargingCabinetDefinition queryEntity) {
        QueryWrapper<ChargingCabinetDefinition> query = Wrappers.query(queryEntity);

        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("deleted_at");
        }

        return chargingCabinetDefinitionMapper.selectPage(page, query);
    }



    @Override
    public List<Map<String, Object>> selectListWithValues(ChargingCabinetDefinition queryEntity) {
        QueryWrapper<ChargingCabinetDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getPointId()), "ccd.point_id", queryEntity.getPointId());
        // 智能充电柜系统号
        query.like(StringUtil.isNotEmpty(queryEntity.getStandCode()), "ccd.stand_code", queryEntity.getStandCode());
        // 柜门号
        query.like(StringUtil.isNotEmpty(queryEntity.getDoorCode()), "ccd.door_code", queryEntity.getDoorCode());
        // 员工号
        query.like(StringUtil.isNotEmpty(queryEntity.getPersonCard()), "ccd.person_card", queryEntity.getPersonCard());
        // 钥匙卡号
        query.like(StringUtil.isNotEmpty(queryEntity.getKeyCardCode()), "ccd.key_card_code", queryEntity.getKeyCardCode());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("ccd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("ccd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("ccd.deleted_at");
        }

        return chargingCabinetDefinitionMapper.selectListWithValues(query);
    }

    @Override
    public IPage<Map<String, Object>> selectPageWithValues(Page<ChargingCabinetDefinition> page, ChargingCabinetDefinition queryEntity) {
        QueryWrapper<ChargingCabinetDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getPointId()), "ccd.point_id", queryEntity.getPointId());
        // 智能充电柜系统号
        query.eq(StringUtil.isNotEmpty(queryEntity.getStandCode()), "ccd.stand_code", queryEntity.getStandCode());
        // 柜门号
        query.eq(StringUtil.isNotEmpty(queryEntity.getDoorCode()), "ccd.door_code", queryEntity.getDoorCode());
        // 员工号
        query.eq(StringUtil.isNotEmpty(queryEntity.getPersonCard()), "ccd.person_card", queryEntity.getPersonCard());
        // 钥匙卡号
        query.eq(StringUtil.isNotEmpty(queryEntity.getKeyCardCode()), "ccd.key_card_code", queryEntity.getKeyCardCode());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("ccd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("ccd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("ccd.deleted_at");
        }

        return chargingCabinetDefinitionMapper.selectPageWithValues(page, query);
    }

}
