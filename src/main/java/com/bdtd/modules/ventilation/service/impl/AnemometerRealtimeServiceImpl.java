package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.AnemometerDefinitionMapper;
import com.bdtd.modules.ventilation.dao.AnemometerRealtimeMapper;
import com.bdtd.modules.ventilation.entity.AnemometerDefinition;
import com.bdtd.modules.ventilation.entity.AnemometerRealtime;
import com.bdtd.modules.ventilation.service.IAnemometerRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 精准测风系统实时监测 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
public class AnemometerRealtimeServiceImpl extends ServiceImpl<AnemometerRealtimeMapper, AnemometerRealtime> implements IAnemometerRealtimeService
{
    private final AnemometerDefinitionMapper anemometerDefinitionMapper;
    private final AnemometerRealtimeMapper anemometerRealtimeMapper;
    private final TaosConnector taosConnector;

    public AnemometerRealtimeServiceImpl(
        AnemometerDefinitionMapper anemometerDefinitionMapper,
        AnemometerRealtimeMapper anemometerRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.anemometerDefinitionMapper = anemometerDefinitionMapper;
        this.anemometerRealtimeMapper = anemometerRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<AnemometerRealtime> selectPage(Page page, AnemometerRealtime queryEntity) {
        QueryWrapper<AnemometerRealtime> query = Wrappers.query(queryEntity);
        return anemometerRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(@RequestParam(required = true) String deviceCode, String startTime, String endTime, String polymerize)
        throws Exception {
        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            throw new BadRequestException("聚合度无效");
        }

        // 检查设备编码是否存在 
        if (StringUtil.isNotEmpty(deviceCode)) {
            QueryWrapper<AnemometerDefinition> queryWrap = Wrappers.query();
            queryWrap.eq("device_code", deviceCode);
            int existedCount = anemometerDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("设备编码不存在");
            }
        }

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select").append(" ");
        buffer.append("`device_code`, "
                      + "last(`wind_speed`) as `wind_speed`, "
                      + "last(`temperature`) as `temperature`, "
                      + "last(`transverse_area`) as `transverse_area`, "
                      + "last(`wind_amount`) as `wind_amount`, "
                      + "last(`humidity`) as `humidity`, "
                      + "last(`barometric`) as `barometric`, "
                      + "last(`online_status`) as `online_status`, "
                      + "last(`online_status_text`) as `online_status_text` "
        );
        buffer.append("from `anemometer`").append(" ");
        buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");

        if (StringUtil.isNotEmpty(deviceCode)) {
            // buffer.append("and `device_code` in ('").append(String.join("','", deviceCodes)).append("') ");
            buffer.append("and `device_code` = '").append(deviceCode).append("' ");
        }

        buffer.append("interval(").append(polymerize).append(") ");
        buffer.append("group by `device_code` ");
        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            mapList = taosConnector.query(sql);
            // if (mapList != null) {
            //     for (Map<String, Object> map : mapList) {
            //         String devCode = (String) map.get("device_code");
            //         retMap.computeIfAbsent(devCode, k -> new ArrayList<>());
            //
            //         List<Map<String, Object>> list = retMap.get(devCode);
            //         list.add(map);
            //         retMap.put(devCode, list);
            //     }
            // }
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: " + ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        // return retMap.values().stream().collect(Collectors.toList());
        return mapList;
    }

}
