package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.FiberTemperatureRealtime;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface IFiberTemperatureRealtimeService extends IService<FiberTemperatureRealtime> {

    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<FiberTemperatureRealtime> selectPage(Page<FiberTemperatureRealtime> page, FiberTemperatureRealtime queryEntity);

    /**
     * 查询点位历史值列表
     * @param sectionCode    分区编码
     * @param startTime     开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime       结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize    采样率, 格式: 1s 或 1m
     * @return /
     */
    List<Map<String, Object>> selectHistoryValues(String sectionCode, String startTime, String endTime, String polymerize) throws Exception;

}
