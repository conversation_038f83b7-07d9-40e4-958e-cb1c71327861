package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.AnemometerRealtime;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 精准测风系统实时监测 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IAnemometerRealtimeService extends IService<AnemometerRealtime>
{
    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<AnemometerRealtime> selectPage(Page<AnemometerRealtime> page, AnemometerRealtime queryEntity);

    /**
     * 查询点位历史值列表
     * @param deviceCode    设备编码
     * @param startTime     开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime       结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize    采样率, 格式: 1s 或 1m
     * @return /
     */
    List<Map<String, Object>> selectHistoryValues(String deviceCode, String startTime, String endTime, String polymerize) throws Exception;

}
