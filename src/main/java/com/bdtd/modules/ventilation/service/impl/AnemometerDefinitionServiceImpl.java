package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.AnemometerDefinitionMapper;
import com.bdtd.modules.ventilation.entity.AnemometerDefinition;
import com.bdtd.modules.ventilation.service.IAnemometerDefinitionService;
import com.bdtd.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 精准测风系统定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
public class AnemometerDefinitionServiceImpl extends ServiceImpl<AnemometerDefinitionMapper, AnemometerDefinition> implements IAnemometerDefinitionService
{
    private final AnemometerDefinitionMapper anemometerDefinitionMapper;

    public AnemometerDefinitionServiceImpl(AnemometerDefinitionMapper anemometerDefinitionMapper) {
        this.anemometerDefinitionMapper = anemometerDefinitionMapper;
    }

    @Override
    public Page<AnemometerDefinition> selectPage(Page page, AnemometerDefinition queryEntity) {
        QueryWrapper<AnemometerDefinition> query = Wrappers.query(queryEntity);

        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("deleted_at");
        }

        return anemometerDefinitionMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectListWithValues(AnemometerDefinition queryEntity) {
        QueryWrapper<AnemometerDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getDeviceCode()), "ad.device_code", queryEntity.getDeviceCode());
        // 安装地点
        query.like(StringUtil.isNotEmpty(queryEntity.getInstallPos()), "ad.install_pos", queryEntity.getInstallPos());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("ad.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("ad.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("ad.deleted_at");
        }

        return anemometerDefinitionMapper.selectListWithValues(query);
    }

    @Override
    public IPage<Map<String, Object>> selectPageWithValues(Page<AnemometerDefinition> page, AnemometerDefinition queryEntity) {
        QueryWrapper<AnemometerDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getDeviceCode()), "ad.device_code", queryEntity.getDeviceCode());
        // 安装地点
        query.like(StringUtil.isNotEmpty(queryEntity.getInstallPos()), "ad.install_pos", queryEntity.getInstallPos());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("ad.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("ad.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("ad.deleted_at");
        }

        return anemometerDefinitionMapper.selectPageWithValues(page, query);
    }

}
