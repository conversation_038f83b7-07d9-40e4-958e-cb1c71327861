package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.FiberTemperatureDefinitionMapper;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;
import com.bdtd.modules.ventilation.service.IFiberTemperatureDefinitionService;
import com.bdtd.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测温分区定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Service
public class FiberTemperatureDefinitionServiceImpl extends ServiceImpl<FiberTemperatureDefinitionMapper, FiberTemperatureDefinition> implements IFiberTemperatureDefinitionService {

    private final FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper;

    public FiberTemperatureDefinitionServiceImpl(FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper) {
        this.fiberTemperatureDefinitionMapper = fiberTemperatureDefinitionMapper;
    }

    @Override
    public Page<FiberTemperatureDefinition> selectPage(Page page, FiberTemperatureDefinition queryEntity) {

        QueryWrapper<FiberTemperatureDefinition> query = Wrappers.query(queryEntity);

        return fiberTemperatureDefinitionMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectListWithValues(FiberTemperatureDefinition queryEntity) {

        QueryWrapper<FiberTemperatureDefinition> query = new QueryWrapper<>();

        // 分区ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getSectionCode()), "ad.section_code", queryEntity.getSectionCode());

        return fiberTemperatureDefinitionMapper.selectListWithValues(query);
    }

    @Override
    public IPage<Map<String, Object>> selectPageWithValues(Page<FiberTemperatureDefinition> page, FiberTemperatureDefinition queryEntity) {

        QueryWrapper<FiberTemperatureDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getSectionCode()), "ad.section_code", queryEntity.getSectionCode());

        return fiberTemperatureDefinitionMapper.selectPageWithValues(page, query);
    }
}
