package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.ChargingCabinetRealtime;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 智能充电柜系统实时数据 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IChargingCabinetRealtimeService extends IService<ChargingCabinetRealtime>
{
    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<ChargingCabinetRealtime> selectPage(Page<ChargingCabinetRealtime> page, ChargingCabinetRealtime queryEntity);

    /**
     * 查询点位历史值列表
     * @param pointId       点位ID
     * @param startTime     开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime       结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize    采样率, 格式: 1s 或 1m
     * @return /
     */
    List<Map<String, Object>> selectHistoryValues(String pointId, String startTime, String endTime, String polymerize) throws Exception;

}
