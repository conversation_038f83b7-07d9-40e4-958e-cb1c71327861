package com.bdtd.modules.ventilation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.ventilation.entity.AnemometerDefinition;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 精准测风系统定义 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
public interface IAnemometerDefinitionService extends IService<AnemometerDefinition>
{
    /**
     * 分页查询
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    Page<AnemometerDefinition> selectPage(Page page, AnemometerDefinition queryEntity);

    /**
     * 查询列表带实时值
     * @param queryEntity   查询参数
     * @return /
     */
    List<Map<String, Object>> selectListWithValues(AnemometerDefinition queryEntity);

    /**
     * 查询分页带实时值
     * @param page          分页参数
     * @param queryEntity   查询参数
     * @return /
     */
    IPage<Map<String, Object>> selectPageWithValues(Page<AnemometerDefinition> page, AnemometerDefinition queryEntity);

}
