package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.FiberTemperatureDefinitionMapper;
import com.bdtd.modules.ventilation.dao.FiberTemperatureRealtimeMapper;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.ventilation.entity.FiberTemperatureDefinition;
import com.bdtd.modules.ventilation.entity.FiberTemperatureRealtime;
import com.bdtd.modules.ventilation.service.IFiberTemperatureRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Service
public class FiberTemperatureRealtimeServiceImpl extends ServiceImpl<FiberTemperatureRealtimeMapper, FiberTemperatureRealtime>
    implements IFiberTemperatureRealtimeService
{

    private final FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper;
    private final FiberTemperatureRealtimeMapper fiberTemperatureRealtimeMapper;
    private final TaosConnector taosConnector;

    public FiberTemperatureRealtimeServiceImpl(
        FiberTemperatureDefinitionMapper fiberTemperatureDefinitionMapper,
        FiberTemperatureRealtimeMapper fiberTemperatureRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.fiberTemperatureDefinitionMapper = fiberTemperatureDefinitionMapper;
        this.fiberTemperatureRealtimeMapper = fiberTemperatureRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<FiberTemperatureRealtime> selectPage(Page<FiberTemperatureRealtime> page, FiberTemperatureRealtime queryEntity) {

        QueryWrapper<FiberTemperatureRealtime> query = Wrappers.query(queryEntity);
        return fiberTemperatureRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(String sectionCode, String startTime, String endTime, String polymerize) throws Exception {

        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            throw new BadRequestException("聚合度无效");
        }

        // 检查点位是否存在
        if (StringUtil.isNotEmpty(sectionCode)) {
            QueryWrapper<FiberTemperatureDefinition> queryWrap = new QueryWrapper<>();
            queryWrap.eq("section_code", sectionCode);
            int existedCount = fiberTemperatureDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的分区编码不存在");
            }
        }

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select ");
        buffer.append(
            " `section_code`, "
            + " last(`temp_str`) as `temp_str`, "
            + " last(`temp_max`) as `temp_max`, "
            + " last(`temp_max_position`) as `temp_max_position`, "
            + " last(`temp_min`) as `temp_min`, "
            + " last(`temp_min_position`) as `temp_min_position`, "
            + " last(`temp_avg`) as `temp_avg`, "
            + " last(`alarm_flag`) as `alarm_flag`, "
            + " last(`alarm_flag_text`) as `alarm_flag_text` "
        );
        buffer.append("from `fiber_temperature` ");
        // 开始、结束时间已做检查，避免SQL注入风险
        buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");

        if (StringUtil.isNotEmpty(sectionCode)) {
            buffer.append("and `section_code` = '").append(sectionCode).append("' ");
        }

        buffer.append("interval(").append(polymerize).append(") ");
        buffer.append("group by `section_code` ");
        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            mapList = taosConnector.query(sql);
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: " + ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        for (Map<String, Object> map : mapList) {
            // 字符数组转字符串
            String tempStr = new String((byte[]) map.get("temp_str"));
            map.put("temp_str", tempStr);
        }

        return mapList;
    }
}
