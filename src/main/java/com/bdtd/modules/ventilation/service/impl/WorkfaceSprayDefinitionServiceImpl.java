package com.bdtd.modules.ventilation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.dao.WorkfaceSprayDefinitionMapper;
import com.bdtd.modules.ventilation.entity.WorkfaceSprayDefinition;
import com.bdtd.modules.ventilation.service.IWorkfaceSprayDefinitionService;
import com.bdtd.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面喷雾系统定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Service
public class WorkfaceSprayDefinitionServiceImpl extends ServiceImpl<WorkfaceSprayDefinitionMapper, WorkfaceSprayDefinition> implements IWorkfaceSprayDefinitionService
{
    private final WorkfaceSprayDefinitionMapper workfaceSprayDefinitionMapper;

    public WorkfaceSprayDefinitionServiceImpl(WorkfaceSprayDefinitionMapper workfaceSprayDefinitionMapper) {
        this.workfaceSprayDefinitionMapper = workfaceSprayDefinitionMapper;
    }

    @Override
    public Page<WorkfaceSprayDefinition> selectPage(Page page, WorkfaceSprayDefinition queryEntity) {
        QueryWrapper<WorkfaceSprayDefinition> query = Wrappers.query(queryEntity);

        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("deleted_at");
        }

        return workfaceSprayDefinitionMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectListWithValues(WorkfaceSprayDefinition queryEntity) {
        QueryWrapper<WorkfaceSprayDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getPointId()), "wsd.device_id", queryEntity.getPointId());
        // 设备名称
        query.like(StringUtil.isNotEmpty(queryEntity.getDeviceName()), "wsd.device_name", queryEntity.getDeviceName());
        // 设备IP
        query.eq(StringUtil.isNotEmpty(queryEntity.getDeviceIp()), "wsd.device_ip", queryEntity.getDeviceIp());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("wsd.deleted_at");
        }

        return workfaceSprayDefinitionMapper.selectListWithValues(query);
    }

    @Override
    public IPage<Map<String, Object>> selectPageWithValues(Page<WorkfaceSprayDefinition> page, WorkfaceSprayDefinition queryEntity) {
        QueryWrapper<WorkfaceSprayDefinition> query = new QueryWrapper<>();

        // 点位ID
        query.eq(StringUtil.isNotEmpty(queryEntity.getPointId()), "wsd.device_id", queryEntity.getPointId());
        // 设备名称
        query.like(StringUtil.isNotEmpty(queryEntity.getDeviceName()), "wsd.device_name", queryEntity.getDeviceName());
        // 设备IP
        query.eq(StringUtil.isNotEmpty(queryEntity.getDeviceIp()), "wsd.device_ip", queryEntity.getDeviceIp());
        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("wsd.deleted_at");
        }

        return workfaceSprayDefinitionMapper.selectPageWithValues(page, query);
    }

}
