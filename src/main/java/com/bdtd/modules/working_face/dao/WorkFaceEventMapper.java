package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WorkingFaceEvent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public interface WorkFaceEventMapper extends BaseMapper<WorkingFaceEvent> {

    List<WorkingFaceEvent> selectWorkFaceEventList(
            @Param("workFaceId") String workFaceId,
            @Param("workFaceType") String workFaceType,
            @Param("evenType") String evenType,
            @Param("recordType") String recordType,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);

    IPage<WorkingFaceEvent> selectWorkFaceEventPage(Page<WorkingFaceEvent> page, String workFaceId, String workFaceType, String evenType, String recordType, String startTime, String endTime);

    int saveOrUpdateBatch(List<WorkingFaceEvent> list);
}
