package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 煤矿定位厂家统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface WfpMineLocationManufacturerMapper extends BaseMapper<WfpMineLocationManufacturer>
{

    WfpMineLocationManufacturer findById(@Param("id") Long id);

    List<WfpMineLocationManufacturer> findList(@Param(Constants.WRAPPER) QueryWrapper<WfpMineLocationManufacturer> queryWrap);

    Page<WfpMineLocationManufacturer> findPage(
            Page<WfpMineLocationManufacturer> page,
            @Param(Constants.WRAPPER) QueryWrapper<WfpMineLocationManufacturer> queryWrap
    );

}
