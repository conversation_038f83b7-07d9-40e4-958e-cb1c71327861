package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 工作面进尺实时值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceProgressRealtimeMapper extends BaseMapper<WorkingFaceProgressRealtime>
{
    List<WorkingFaceProgressRealtime> selectBySystemId(@Param("systemId") String systemId);

    /**
     * 取得工作面最新的数据时间
     *
     * @param wrapper 查询条件
     * @return 最后同步的时间
     */
    List<WorkingFaceProgressRealtime> getLatestRealtimeList(@Param(Constants.WRAPPER) Wrapper<WorkingFaceProgressRealtime> wrapper);

    /**
     * 取得最后同步的时间
     *
     * @param wrapper 查询条件
     * @return 最后同步的时间
     */
    Timestamp getAllLatestDataTime(@Param(Constants.WRAPPER) Wrapper<WorkingFaceProgressRealtime> wrapper);

}
