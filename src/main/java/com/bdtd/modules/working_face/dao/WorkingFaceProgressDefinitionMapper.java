package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面进尺定义 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceProgressDefinitionMapper extends BaseMapper<WorkingFaceProgressDefinition> {
    List<WorkingFaceProgressDefinition> selectBySystemId(@Param("systemId") String systemId);

    List<Map<String, Object>> selectListWithValues(
        @Param(Constants.WRAPPER) Wrapper<WorkingFaceProgressDefinition> wrapper
    );

    IPage<Map<String, Object>> selectPageWithValues(
        Page<WorkingFaceProgressDefinition> page,
        @Param(Constants.WRAPPER)
        Wrapper<WorkingFaceProgressDefinition> wrapper
    );

    /**
     * 查询数据接入开启的工作面
     *
     * @param wrapper 查询条件
     * @return /
     */
    List<WorkingFaceProgressDefinition> selectListAcqEnabled(@Param(Constants.WRAPPER) Wrapper<WorkingFaceProgressDefinition> wrapper);

    /**
     * 批量更新工作面定义
     *
     * @param insertList 工作面定义列表
     */
    void upsertWfpdBatch(List<WorkingFaceProgressDefinition> insertList);

    /**
     * 批量更新工作面定义和工作模式变更信息
     *
     * @param insertList 工作面定义列表
     */
    void upsertWfpdBatchWithWorkMode(List<WorkingFaceProgressDefinition> insertList);

}
