package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.dto.WorkingFaceLocatorStatusComposite;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 工作面监测信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceLocatorStatusMapper extends BaseMapper<WorkingFaceLocatorStatus>
{

    IPage<WorkingFaceLocatorStatusComposite> selectCompositedPage(@Param("query") WorkingFaceLocatorStatus t, Page<WorkingFaceLocatorStatusComposite> page);

    // IPage<WorkingFaceLocatorStatus> selectPageTest1(@Param("qe") WorkingFaceLocatorStatus t, Page<WorkingFaceLocatorStatus> page);
    // IPage<WorkingFaceLocatorStatus> selectPageTest2(@Param("qw") QueryWrapper<WorkingFaceLocatorStatus> t, Page<WorkingFaceLocatorStatus> page);

}
