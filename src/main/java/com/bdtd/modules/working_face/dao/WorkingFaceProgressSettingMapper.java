package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.working_face.dto.DepartmentDto;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 工作面解析配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceProgressSettingMapper extends BaseMapper<WorkingFaceProgressSetting>
{

    /**
     * 获取二级公司列表
     *
     * @return /
     */
    List<DepartmentDto> selectCompanyList();

    /**
     * 获取煤矿列表
     *
     * @return /
     */
    List<DepartmentDto> selectMineList(@Param("companyCode") String companyCode);

    /**
     * 取得煤矿最新的配置
     *
     * @return /
     */
    WorkingFaceProgressSetting selectLatestSetting(@Param("mineCode") String mineCode);

    /**
     * 根据煤矿编码和配置时间取得煤矿配置
     *
     * @param mineCode   煤矿编码
     * @param updateTime 配置时间
     * @return /
     */
    WorkingFaceProgressSetting selectWithMineCodeAndUpdateTime(
            @Param("mineCode") String mineCode,
            @Param("updateTime") Timestamp updateTime
    );

    /**
     * 获取最新的更新时间
     *
     * @return /
     */
    Long selectLatestUpdateTime();

}
