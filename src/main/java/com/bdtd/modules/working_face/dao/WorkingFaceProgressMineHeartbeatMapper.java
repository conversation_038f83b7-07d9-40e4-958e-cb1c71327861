package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressMineHeartbeat;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 矿端采集链路心跳 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceProgressMineHeartbeatMapper extends BaseMapper<WorkingFaceProgressMineHeartbeat>
{
    /**
     * 取得最后同步的时间
     *
     * @param wrapper 查询条件
     * @return 得最后同步的时间
     */
    Timestamp getLatestSyncTime(@Param(Constants.WRAPPER) Wrapper<WorkingFaceProgressMineHeartbeat> wrapper);

    /**
     * 新增更新心跳记录
     *
     * @param list 心跳记录
     */
    void upsertBatch(@Param("list") List<WorkingFaceProgressMineHeartbeat> list);

    /**
     * 保存检查结果
     *
     * @param list 心跳记录
     */
    void saveCheckResult(@Param("list") List<WorkingFaceProgressMineHeartbeat> list);

}
