package com.bdtd.modules.working_face.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.working_face.entity.WorkingFaceDefinition;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 工作面基本信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface WorkingFaceDefinitionMapper extends BaseMapper<WorkingFaceDefinition>
{

    Timestamp selectLastUpdateTime();

    int upsertBatchWfDef(List<WorkingFaceDefinition> list);

    // region 根据更新时间更新工作面的状态等信息

    /**
     * 批量更新指定时间段内发生更新的工作面
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return /
     */
    int updateModelSystemByUpdatedWf(Timestamp startTime, Timestamp endTime);

    /**
     * 批量更新指定时间段内发生更新的工作面配置
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return /
     */
    int updateModelSystemAliveByUpdatedWf(Timestamp startTime, Timestamp endTime);

    /**
     * 批量更新指定时间段内发生更新的工作面的断线记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return /
     */
    int updateModelSystemAliveStatisticsByUpdatedWf(Timestamp startTime, Timestamp endTime);

    /**
     * 批量更新指定时间段内发生更新的工作面在线状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return /
     */
    int updateDataSystemMonitorByUpdatedWf(Timestamp startTime, Timestamp endTime);

    /**
     * 批量更新指定时间段内发生更新的工作面定义
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return /
     */
    int updateWorkingFaceProgressDefinitionByUpdatedWf(Timestamp startTime, Timestamp endTime);

    // endregion 根据更新时间更新工作面的状态等信息

    // region 根据指定工作面更新工作面状态等

    /**
     * 根据指定工作面ID更新工作面的在线状态，更新为 2 不确定状态
     *
     * @param mineCode 煤矿编码
     * @param wfIds    工作面ID列表
     */
    void updateDataSystemMonitorStatus(
            @Param("mineCode") String mineCode,
            @Param("set") Set<String> wfIds
    );

    /**
     * 根据指定工作面ID更新工作面的断线记录，结束断线记录
     *
     * @param mineCode 煤矿编码
     * @param wfIds    工作面ID列表
     */
    void updateModelSystemAliveStatistics(
            @Param("mineCode") String mineCode,
            @Param("set") Set<String> wfIds
    );

    // endregion 根据指定工作面更新工作面状态等

    // region 根据启用的工作面更新工作面状态等

    /**
     * 根据启用的工作面ID反向更新禁用的工作面的在线状态，更新为 2 不确定状态
     *
     * @param mineCode     煤矿编码
     * @param enabledWfIds 工作面ID列表
     */
    void updateDataSystemMonitorStatusWithEnabledWfd(
            @Param("mineCode") String mineCode,
            @Param("set") Set<String> enabledWfIds
    );

    /**
     * 根据启用的工作面ID反向更新禁用的工作面的断线记录，结束断线记录
     *
     * @param mineCode     煤矿编码
     * @param enabledWfIds 工作面ID列表
     */
    void updateModelSystemAliveStatisticsWithEnabledWfd(
            @Param("mineCode") String mineCode,
            @Param("set") Set<String> enabledWfIds
    );

    // endregion 根据启用的工作面更新工作面状态等

}
