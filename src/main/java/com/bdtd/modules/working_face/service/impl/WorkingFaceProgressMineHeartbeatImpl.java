package com.bdtd.modules.working_face.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.conf.RabbitTopicConfig;
import com.bdtd.modules.alarm.service.IPushMsgService;
import com.bdtd.modules.monitor.entity.AutoAlarmMsgs;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IAutoAlarmMsgsService;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressMineHeartbeatMapper;
import com.bdtd.modules.working_face.dto.MineHeartbeatWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressMineHeartbeat;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressMineHeartbeatService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.middleware.MqSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpRequest;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.io.support.ClassicRequestBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 矿端采集链路心跳 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@Service
@Slf4j
public class WorkingFaceProgressMineHeartbeatImpl extends ServiceImpl<WorkingFaceProgressMineHeartbeatMapper, WorkingFaceProgressMineHeartbeat>
        implements IWorkingFaceProgressMineHeartbeatService
{
    @Value("${scheduled.position-mine-heartbeat-url}")
    private String positionMineHbBizUrl;

    private final WorkingFaceProgressMineHeartbeatMapper workingFaceProgressMineHeartbeatMapper;
    private final IPushMsgService pushMsgService;
    private final IAutoAlarmMsgsService autoAlarmMsgsService;
    private final MqSendUtil mqSendUtil;

    public WorkingFaceProgressMineHeartbeatImpl(
            WorkingFaceProgressMineHeartbeatMapper workingFaceProgressMineHeartbeatMapper,
            IPushMsgService pushMsgService,
            IAutoAlarmMsgsService autoAlarmMsgsService,
            MqSendUtil mqSendUtil
    )
    {
        this.workingFaceProgressMineHeartbeatMapper = workingFaceProgressMineHeartbeatMapper;
        this.pushMsgService = pushMsgService;
        this.autoAlarmMsgsService = autoAlarmMsgsService;
        this.mqSendUtil = mqSendUtil;
    }

    /**
     * 分页查询人员定位解算历史数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public IPage<WorkingFaceProgressMineHeartbeat> queryWorkingFaceProgressMineHeartbeatPage(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException
    {
        // if (StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate())) {
        //     throw new BadRequestException("未提供查询时间范围");
        // }

        // 查询条件
        QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = generateQueryWrapper(queryCriteria);

        try {
            return workingFaceProgressMineHeartbeatMapper.selectPage(page, queryWrap);
        }
        catch (Exception e) {
            log.error("queryWorkingFaceProgressMineHeartbeatPage failed, error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 查询人员定位解算历史数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public List<WorkingFaceProgressMineHeartbeat> queryWorkingFaceProgressMineHeartbeatList(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException
    {
        // if (StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate())) {
        //     throw new BadRequestException("未提供查询时间范围");
        // }

        // 查询条件
        QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = generateQueryWrapper(queryCriteria);
        for (OrderItem order : page.getOrders()) {
            if (order.isAsc()) {
                queryWrap.orderByAsc(order.getColumn());
            }
            else {
                queryWrap.orderByDesc(order.getColumn());
            }
        }

        try {
            return workingFaceProgressMineHeartbeatMapper.selectList(queryWrap);
        }
        catch (Exception e) {
            log.error("queryWorkingFaceProgressMineHeartbeatPage failed, error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 得最后同步的时间
     */
    @Override
    public Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria)
    {
        QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = new QueryWrapper<>();
        queryWrap.eq(StringUtil.isNotEmpty(queryCriteria.getMineCode()), "mine_code", queryCriteria.getMineCode());
        return workingFaceProgressMineHeartbeatMapper.getLatestSyncTime(queryWrap);
    }

    /**
     * 同步矿端采集链路心跳
     *
     * @param syncCriteria 同步参数
     * @param actionStr    操作字符串
     * @return /
     */
    @Override
    public PositionCalcSyncResult syncPositionMineHeartbeat(
            PositionCalcQueryCriteria syncCriteria,
            String actionStr
    )
    {
        PositionCalcSyncResult result = new PositionCalcSyncResult();
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(syncCriteria.getStartDate())) {
            queryMap.put("start_time", syncCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getEndDate())) {
            queryMap.put("end_time", syncCriteria.getEndDate());
        }

        JSONObject respObj = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet = ClassicRequestBuilder.get(positionMineHbBizUrl).build();

            StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
            httpGet.setEntity(entity);

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    respObj = JSON.parseObject(is, JSONObject.class);
                    log.debug(
                            "syncPositionMineHeartbeat return result (startDate: {}, endDate: {}, mineCode: {}): {}", syncCriteria.getStartDate(),
                            syncCriteria.getEndDate(),
                            "null",
                            (respObj == null ? "" : respObj.toJSONString())
                    );
                }
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 接口调用错误",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        // 返回内容无效
        if (respObj == null
            || !respObj.containsKey("content")
            || respObj.getJSONArray("content") == null
        ) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 返回内容无效",
                    (byte) 1,
                    "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
        // 返回内容为空
        if (respObj.getJSONArray("content").isEmpty()) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 无同步数据",
                    (byte) 0,
                    "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        List<WorkingFaceProgressMineHeartbeat> insertList = new ArrayList<>();
        Map<String, WorkingFaceProgressMineHeartbeat> insertMineMap = new HashMap<>();

        try {
            JSONArray historyArr = respObj.getJSONArray("content");
            for (Object obj : historyArr) {
                JSONObject jsonObj = (JSONObject) obj;

                WorkingFaceProgressMineHeartbeat hbObj = new WorkingFaceProgressMineHeartbeat();
                hbObj.setMineCode(jsonObj.getString("mine_code"));
                hbObj.setHbTime(jsonObj.getObject("hb_time", Timestamp.class));
                hbObj.setUpdateTime(jsonObj.getObject("updated_at", Timestamp.class));
                hbObj.setTimeLimit(3600);
                hbObj.setOffline("1");

                // 服务状态（telnet 服务端口，0离线1在线)")
                if (jsonObj.containsKey("service_state")) {
                    String serviceState = jsonObj.getString("service_state");
                    if (StringUtil.isNotBlank(serviceState)) {
                        hbObj.setServiceState(serviceState);
                    }
                    else {
                        // 默认离线
                        hbObj.setServiceState("0");
                    }
                }
                // 服务状态监测时间
                if (jsonObj.containsKey("service_state_monitor_time")) {
                    Timestamp serviceStateChkTime = jsonObj.getObject("service_state_monitor_time", Timestamp.class);
                    if (serviceStateChkTime != null) {
                        hbObj.setServiceStateMonitorTime(serviceStateChkTime);
                    }
                }
                // 连通状态（ping IP，0离线1在线）
                if (jsonObj.containsKey("connect_state")) {
                    String connectState = jsonObj.getString("connect_state");
                    if (StringUtil.isNotBlank(connectState)) {
                        hbObj.setConnectState(connectState);
                    }
                    else {
                        // 默认离线
                        hbObj.setConnectState("0");
                    }
                }
                // 连通状态监测时间
                if (jsonObj.containsKey("connect_state_monitor_time")) {
                    Timestamp connectStateChkTime = jsonObj.getObject("connect_state_monitor_time", Timestamp.class);
                    if (connectStateChkTime != null) {
                        hbObj.setConnectStateMonitorTime(connectStateChkTime);
                    }
                }

                insertList.add(hbObj);
                insertMineMap.put(hbObj.getMineCode(), hbObj);
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 接口返回解析错误",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);
        }

        try {
            // 批量插入或者更新
            if (!insertList.isEmpty()) {
                workingFaceProgressMineHeartbeatMapper.upsertBatch(insertList);
            }

            for (Map.Entry<String, WorkingFaceProgressMineHeartbeat> entry : insertMineMap.entrySet()) {
                WorkingFaceProgressMineHeartbeat heartbeat = entry.getValue();
                serviceCallLogs.add(ServiceCallLog.createNew(
                        String.format(
                                "%s 完成数据同步, hb_time: %s, updated_at: %s",
                                actionStr,
                                heartbeat.getHbTime(),
                                heartbeat.getUpdateTime()
                        ),
                        (byte) 0,
                        null,
                        null,
                        heartbeat.getMineCode()
                ));
            }

            result.setSucceed(true);
            result.setCount(insertList.size());
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 数据落库失败",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
    }

    /**
     * 检查矿端采集链路心跳状态
     */
    @Override
    public void checkPositionMineHeartbeat(String checkField, PositionCalcQueryCriteria syncQueryCriteria)
    {
        // 日志输出时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        long startMs = System.currentTimeMillis();
        log.info("checkPositionMineHeartbeat started at: {}", sdf.format(new Timestamp(startMs)));
        try {
            // 只检查下发了指标的煤矿
            QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = new QueryWrapper<>();
            // queryWrap.isNotNull("indicator_id");
            queryWrap.isNotNull("time_limit");
            List<WorkingFaceProgressMineHeartbeat> heartbeatList = this.list(queryWrap);

            List<WorkingFaceProgressMineHeartbeat> updateList = new ArrayList<>();
            for (WorkingFaceProgressMineHeartbeat hb : heartbeatList) {
                Timestamp beCheckedTime;
                String prevOffline = hb.getOffline();

                // 根据更新时间判断
                if ("update_time".equals(checkField)) {
                    beCheckedTime = hb.getUpdateTime();
                }
                else {
                    // 根据心跳时间判断
                    beCheckedTime = hb.getHbTime();
                }

                // 没有同步到时间, 默认离线
                if (beCheckedTime == null) {
                    hb.setOffline("1");
                    hb.setCheckDesc(String.format("断线监测用字段 %s 为空", checkField));

                    updateList.add(hb);
                    continue;
                }

                // 链路检测字段更新
                if ("1".equals(hb.getServiceState())) {
                    hb.setServiceLastOnlineTime(hb.getServiceStateMonitorTime());
                }
                if ("1".equals(hb.getConnectState())) {
                    hb.setConnectLastOnlineTime(hb.getConnectStateMonitorTime());
                }

                Timestamp expireTime = new Timestamp(System.currentTimeMillis() - hb.getTimeLimit() * 1000);
                if (beCheckedTime.before(expireTime)) {
                    hb.setOffline("1");
                    hb.setCheckDesc(String.format(
                            "断线监测用字段 %s: %s, 断线判断用时间: %s, 断线阈值: %s 秒",
                            checkField,
                            sdf.format(beCheckedTime),
                            sdf.format(expireTime),
                            hb.getTimeLimit()
                    ));
                }
                else {
                    hb.setOffline("0");
                    hb.setCheckDesc("正常");
                }

                // 更新时间
                hb.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                // 离线状态是否变更
                hb.setOfflineChanged(!Objects.equals(prevOffline, hb.getOffline()));

                updateList.add(hb);
            }

            if (!updateList.isEmpty()) {
                workingFaceProgressMineHeartbeatMapper.saveCheckResult(updateList);
                log.info(
                        "Mine heartbeat check success, total update {}, online size: {}, offline size: {}",
                        updateList.size(),
                        updateList.stream().filter(hb -> "0".equals(hb.getOffline())).count(),
                        updateList.stream().filter(hb -> "1".equals(hb.getOffline())).count()
                );

                // 推送已经报警
                // 不再筛选状态发生变化的，改为根据预警报警侧指标报警状态进行筛选
                // pushWarnToPushMsg(
                //         updateList.stream()
                //                   .filter(mhb -> mhb.getOfflineChanged() != null
                //                                  && mhb.getOfflineChanged()
                //                                  && StringUtil.isNotEmpty(mhb.getIndicatorId())
                //                   )
                //                   .collect(Collectors.toList())
                // );
                pushWarnToPushMsg(updateList, checkField, syncQueryCriteria);
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        finally {
            log.info("checkPositionMineHeartbeat ends, cost: {} ms", (System.currentTimeMillis() - startMs));
        }
    }

    /**
     * 代理矿端采集链路心跳数据
     *
     * @param queryCriteria 同步条件
     * @return 同步结果
     */
    @Override
    public Object passPositionMineHeartbeat(PositionCalcQueryCriteria queryCriteria)
    {
        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStartDate())) {
            queryMap.put("start_time", queryCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
            queryMap.put("end_time", queryCriteria.getEndDate());
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet = ClassicRequestBuilder.get(positionMineHbBizUrl).build();

            StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
            httpGet.setEntity(entity);

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    return JSON.parseObject(is, JSONObject.class);
                }
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    /**
     * 同步矿端链路相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置列表
     * @return /
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean submitIndicatorRule(List<MineHeartbeatWarnIndicatorSubmitDto> submitDtoList) throws BadRequestException
    {
        // 检查输入
        for (MineHeartbeatWarnIndicatorSubmitDto submitDto : submitDtoList) {
            if (StringUtil.isEmpty(submitDto.getWarnType())) {
                throw new BadRequestException("报警类型 不能为空");
            }
            if (StringUtil.isEmpty(submitDto.getMineCode())) {
                throw new BadRequestException("煤矿编码 不能为空");
            }
            // if (submitDto.getIndicatorId() == null) {
            //     throw new BadRequestException("指标ID 不能为空");
            // }
            if (submitDto.getIndicatorId() != null && (submitDto.getWarningValue() == null || submitDto.getWarningValue() == 0)) {
                throw new BadRequestException("报警阈值 不能为空或者0");
            }
        }

        // 煤矿ID
        Set<String> mineCodes = submitDtoList
                .stream()
                .map(MineHeartbeatWarnIndicatorSubmitDto::getMineCode)
                .collect(Collectors.toSet());
        if (mineCodes.isEmpty()) {
            throw new BadRequestException("下发指标无效，可能为空");
        }

        QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = new QueryWrapper<>();
        if (mineCodes.size() == 1) {
            queryWrap.eq("mine_code", mineCodes.iterator().next());
        }
        else {
            queryWrap.in("mine_code", new ArrayList<>(mineCodes));
        }

        // 既存记录
        List<WorkingFaceProgressMineHeartbeat> existedDefList = list(queryWrap);
        // 更新保存记录
        List<WorkingFaceProgressMineHeartbeat> saveOrUpdateList = new ArrayList<>();

        // 下发规则
        for (MineHeartbeatWarnIndicatorSubmitDto submitDto : submitDtoList) {
            // if ("wrongChannel".equalsIgnoreCase(submitDto.getWarnType())) {
            //     continue;
            // }
            if (!"mineHeartbeat".equalsIgnoreCase(submitDto.getWarnType())) {
                continue;
            }

            Optional<WorkingFaceProgressMineHeartbeat> mhbOpt = existedDefList
                    .stream()
                    .filter(wf -> wf.getMineCode().equals(submitDto.getMineCode()))
                    .findAny();
            if (!mhbOpt.isPresent()) {
                log.warn(
                        "Submit working face may not exists. mineCode: {}, indicatorId: {}",
                        submitDto.getMineCode(),
                        submitDto.getIndicatorId()
                );
                continue;
            }

            WorkingFaceProgressMineHeartbeat mhb = mhbOpt.get();
            mhb.setIndicatorId(submitDto.getIndicatorId());
            mhb.setTimeLimit(submitDto.getWarningValue());
            mhb.setIndicatorUpdatedAt(new Timestamp(System.currentTimeMillis()));

            saveOrUpdateList.add(mhb);
        }

        try {
            saveOrUpdateBatch(saveOrUpdateList);
            log.info("submitIndicatorRule succeeded: {}", JSON.toJSONString(submitDtoList));

            return true;
        }
        catch (Exception e) {
            log.error("submitIndicatorRule failed: {}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 生成查询条件
     *
     * @param queryCriteria 查询条件
     * @return /
     */
    private QueryWrapper<WorkingFaceProgressMineHeartbeat> generateQueryWrapper(PositionCalcQueryCriteria queryCriteria)
    {
        QueryWrapper<WorkingFaceProgressMineHeartbeat> queryWrap = new QueryWrapper<>();
        queryWrap.eq(StringUtil.isNotEmpty(queryCriteria.getMineCode()), "mine_code", queryCriteria.getMineCode());
        queryWrap.eq(StringUtil.isNotEmpty(queryCriteria.getOffline()), "offline", queryCriteria.getOffline());

        // 时间窗口
        int timeMode = 2;
        if (queryCriteria.getTimeMode() != null && Arrays.asList(0, 1, 2, 3).contains(queryCriteria.getTimeMode())) {
            timeMode = queryCriteria.getTimeMode();
        }

        switch (timeMode) {
            case 0:
                queryWrap.gt(
                        StringUtil.isNotEmpty(queryCriteria.getStartDate()),
                        "update_time",
                        queryCriteria.getStartDate()
                );
                queryWrap.lt(
                        StringUtil.isNotEmpty(queryCriteria.getEndDate()),
                        "update_time",
                        queryCriteria.getEndDate()
                );
                break;
            case 1:
                queryWrap.gt(
                        StringUtil.isNotEmpty(queryCriteria.getStartDate()),
                        "update_time",
                        queryCriteria.getStartDate()
                );
                queryWrap.le(
                        StringUtil.isNotEmpty(queryCriteria.getEndDate()),
                        "update_time",
                        queryCriteria.getEndDate()
                );
                break;
            case 3:
                queryWrap.ge(
                        StringUtil.isNotEmpty(queryCriteria.getStartDate()),
                        "update_time",
                        queryCriteria.getStartDate()
                );
                queryWrap.le(
                        StringUtil.isNotEmpty(queryCriteria.getEndDate()),
                        "update_time",
                        queryCriteria.getEndDate()
                );
                break;
            case 2:
            default:
                queryWrap.ge(
                        StringUtil.isNotEmpty(queryCriteria.getStartDate()),
                        "update_time",
                        queryCriteria.getStartDate()
                );
                queryWrap.lt(
                        StringUtil.isNotEmpty(queryCriteria.getEndDate()),
                        "update_time",
                        queryCriteria.getEndDate()
                );
                break;
        }

        return queryWrap;
    }

    /**
     * 推送矿端链路断线报警到预警报警服务
     *
     * @param updateList        在线检查结果
     * @param checkField        检查用字段
     * @param syncQueryCriteria 检查数据同步参数
     */
    private void pushWarnToPushMsg(List<WorkingFaceProgressMineHeartbeat> updateList, String checkField, PositionCalcQueryCriteria syncQueryCriteria)
    {
        if (updateList.isEmpty()) {
            log.info("### push mineHeartbeatWarn to push-msg, size: 0 ###");
            return;
        }

        Map<String, Integer> indicatorAlarmStatusMap = null;
        Set<String> idicatorIdSet = null;
        try {
            // 检查指标对象
            idicatorIdSet = updateList
                    .stream()
                    .map(WorkingFaceProgressMineHeartbeat::getIndicatorId)
                    .filter(StringUtil::isNotEmpty)
                    .collect(Collectors.toSet());
            // 获取指标状态
            indicatorAlarmStatusMap = pushMsgService.getIndicatorAlarmStatus(idicatorIdSet);
        }
        catch (Exception ex) {
            log.error(
                    "Failed to get alarm status of indicator {}, error: {}",
                    idicatorIdSet,
                    ex.getMessage(),
                    ex
            );
            return;
        }

        if (indicatorAlarmStatusMap == null || indicatorAlarmStatusMap.isEmpty()) {
            log.info("### push mineHeartbeatWarn to push-msg, no indicator assigned ###");
            return;
        }

        // 消息输出时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 当前时间
        String nowStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        Set<String> mineHbStates = new HashSet<>();
        List<JSONObject> pushObjList = new ArrayList<>();
        boolean pushSuccess = false;

        try {
            // 未配置的指标ID列表
            Set<String> unsetIndicatorSet = new HashSet<>();
            // 已关闭的指标ID Map
            Map<String, Integer> closedIndicatorMap = new HashMap<>();

            for (WorkingFaceProgressMineHeartbeat entry : updateList) {
                // 未设置指标ID
                if (entry.getIndicatorId() == null) {
                    log.warn("!!! Indicator is not set of mine {}", entry.getMineCode());
                    continue;
                }
                // 预警报警服务状态默认正常，便于后续处理
                if (!indicatorAlarmStatusMap.containsKey(entry.getIndicatorId())) {
                    // indicatorAlarmStatusMap.put(entry.getIndicatorId(), 1);
                    unsetIndicatorSet.add(entry.getIndicatorId());
                    continue;
                }
                // 指标报警状态
                Integer indicatorStatus = indicatorAlarmStatusMap.get(entry.getIndicatorId());
                // 指标已关闭
                if (!Arrays.asList(0, 1, 9).contains(indicatorStatus) || Objects.equals(9, indicatorStatus)) {
                    closedIndicatorMap.put(entry.getIndicatorId(), indicatorStatus);
                    continue;
                }

                // 本报警: 0正常，1异常
                // 预警报警服务: 0报警中，1报警结束，9指标关闭
                if (!Objects.equals(indicatorStatus, Integer.valueOf(entry.getOffline()))) {
                    // 状态一致，不生成推送
                    continue;
                }

                JSONObject obj = new JSONObject();
                obj.put("mineCode", entry.getMineCode());
                obj.put("warningAlarmInfoUpdatetime", nowStr);
                if ("update_time".equals(checkField)) {
                    obj.put("warningAlarmInfoCreatetime", sdf.format(entry.getUpdateTime()));
                }
                else {
                    obj.put("warningAlarmInfoCreatetime", sdf.format(entry.getHbTime()));
                }
                obj.put("warningAlarmId", entry.getIndicatorId());

                // 根据判断字段不同对告警内容进行必要的区分
                if ("update_time".equals(checkField)) {
                    obj.put("warnContent", String.format(
                            // "矿端采集链路疑似中断，最后处理时间为：%s",
                            "采集链路疑似中断，最后连接时间为：%s",
                            (entry.getUpdateTime() != null ? sdf.format(entry.getUpdateTime()) : "")
                    ));
                }
                else {
                    obj.put("warnContent", String.format(
                            // "矿端采集链路疑似中断，最后心跳时间为：%s",
                            "采集链路疑似中断，最后连接时间为：%s",
                            (entry.getHbTime() != null ? sdf.format(entry.getHbTime()) : "")
                    ));
                }

                obj.put("status", entry.getOffline());
                pushObjList.add(obj);

                // 记录推送的煤矿和状态，打印日志用
                mineHbStates.add(String.format("%s:%s", entry.getMineCode(), entry.getOffline()));
            }

            // 推送到预警报警分析服务
            if (!pushObjList.isEmpty()) {
                mqSendUtil.send(
                        RabbitTopicConfig.SYSTEM_ALIVE_PUSH_EXCHANGE,
                        RabbitTopicConfig.SYSTEM_ALIVE_PUSH_ROUTING_KEY,
                        pushObjList
                );
                pushSuccess = true;

                log.info(
                        "### push mineHeartbeatWarn to push-msg, size: {}, content(mineCode:offline): {} ###",
                        pushObjList.size(),
                        String.join("|", mineHbStates)
                );
            }

            // 记录日志
            if (!unsetIndicatorSet.isEmpty()) {
                log.warn(
                        "!!! Indicator is not returned: {}, DO NOT push mineHeartbeatWarn to push-msg",
                        String.join("|", unsetIndicatorSet)
                );
            }
            if (!closedIndicatorMap.isEmpty()) {
                log.warn(
                        "!!! Indicator is unknown or closed: {}, DO NOT push mineHeartbeatWarn to push-msg",
                        closedIndicatorMap.entrySet()
                                          .stream()
                                          .map(Object::toString)
                                          .collect(Collectors.joining("&"))
                );
            }
        }
        catch (Exception ex) {
            log.warn("push mineHeartbeatWarn to push-msg failed: {}", ex.getMessage(), ex);
        }

        try {
            if (pushSuccess) {
                Map<String, Object> memoMap = new HashMap<>();
                memoMap.put("syncQueryCriteria", syncQueryCriteria);
                memoMap.put("mineHbStates", mineHbStates);

                AutoAlarmMsgs backupMsg = new AutoAlarmMsgs(
                        "mineHeartbeatWarn",
                        JSON.toJSONString(pushObjList),
                        JSON.toJSONString(memoMap)
                );
                autoAlarmMsgsService.save(backupMsg);
            }
        }
        catch (Exception e) {
            log.error("save mineHeartbeatWarn to autoAlarmMsgs failed: {}", e.getMessage(), e);
        }
    }

}
