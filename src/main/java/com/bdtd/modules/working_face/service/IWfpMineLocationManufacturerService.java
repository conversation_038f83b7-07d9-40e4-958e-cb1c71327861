package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer;
import com.bdtd.util.exception.BadRequestException;

import java.util.List;

/**
 * <p>
 * 煤矿定位厂家统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface IWfpMineLocationManufacturerService extends IService<WfpMineLocationManufacturer>
{

    Boolean createOrUpdate(WfpMineLocationManufacturer saveEntity) throws BadRequestException;

    WfpMineLocationManufacturer findById(Long id);

    List<WfpMineLocationManufacturer> findList(WfpMineLocationManufacturer queryEntity);

    Page<WfpMineLocationManufacturer> findPage(Page<WfpMineLocationManufacturer> page, WfpMineLocationManufacturer queryEntity);

}
