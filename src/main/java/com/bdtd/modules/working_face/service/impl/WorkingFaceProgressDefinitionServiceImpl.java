package com.bdtd.modules.working_face.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressDefinitionMapper;
import com.bdtd.modules.working_face.dto.WorkingFaceWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressDefinitionService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作面进尺定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Slf4j
@Service
public class WorkingFaceProgressDefinitionServiceImpl extends ServiceImpl<WorkingFaceProgressDefinitionMapper, WorkingFaceProgressDefinition>
        implements IWorkingFaceProgressDefinitionService
{
    private final WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper;

    public WorkingFaceProgressDefinitionServiceImpl(WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper)
    {
        this.workingFaceProgressDefinitionMapper = workingFaceProgressDefinitionMapper;
    }

    @Override
    public Page<WorkingFaceProgressDefinition> selectPage(
            Page<WorkingFaceProgressDefinition> page,
            WorkingFaceProgressDefinition queryEntity
    )
    {
        QueryWrapper<WorkingFaceProgressDefinition> query = Wrappers.query(queryEntity);

        // 集团编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "group_code", queryEntity.getGroupCode());
        // 煤矿编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "mine_code", queryEntity.getMineCode());
        // 工作面ID
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceId()),
                "working_face_id",
                queryEntity.getWorkingFaceId()
        );
        // 工作面类型
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceType()),
                "working_face_type",
                queryEntity.getWorkingFaceType()
        );
        // 工作面名称
        query.like(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceName()),
                "working_face_name",
                queryEntity.getWorkingFaceName()
        );

        // 是否逻辑
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("deleted_at");
        }

        return workingFaceProgressDefinitionMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectListWithValues(WorkingFaceProgressDefinition queryEntity)
    {
        QueryWrapper<WorkingFaceProgressDefinition> query = new QueryWrapper<>();

        // 集团编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "wsd.group_code", queryEntity.getGroupCode());
        // 煤矿编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "wsd.mine_code", queryEntity.getMineCode());
        // 工作面ID
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceId()),
                "wsd.working_face_id",
                queryEntity.getWorkingFaceId()
        );
        // 工作面类型
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceType()),
                "wsd.working_face_type",
                queryEntity.getWorkingFaceType()
        );
        // 工作面名称
        query.like(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceName()),
                "wsd.working_face_name",
                queryEntity.getWorkingFaceName()
        );

        // 是否逻辑删除
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("wsd.deleted_at");
        }

        // 排序
        query.orderByDesc("wsr.data_time");

        return workingFaceProgressDefinitionMapper.selectListWithValues(query);
    }

    @Override
    public IPage<Map<String, Object>> selectPageWithValues(
            Page<WorkingFaceProgressDefinition> page,
            WorkingFaceProgressDefinition queryEntity
    )
    {
        QueryWrapper<WorkingFaceProgressDefinition> query = new QueryWrapper<>();

        // 集团编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "wsd.group_code", queryEntity.getGroupCode());
        // 煤矿编码
        query.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "wsd.mine_code", queryEntity.getMineCode());
        // 工作面ID
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceId()),
                "wsd.working_face_id",
                queryEntity.getWorkingFaceId()
        );
        // 工作面类型
        query.eq(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceType()),
                "wsd.working_face_type",
                queryEntity.getWorkingFaceType()
        );
        // 工作面名称
        query.like(
                StringUtil.isNotEmpty(queryEntity.getWorkingFaceName()),
                "wsd.working_face_name",
                queryEntity.getWorkingFaceName()
        );

        // 是否逻辑删除
        if (queryEntity.getDeletedAtIsNull() == null) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 1) {
            query.isNull("wsd.deleted_at");
        }
        else if (queryEntity.getDeletedAtIsNull() == 0) {
            query.isNotNull("wsd.deleted_at");
        }

        // 排序
        query.orderByDesc("wsr.data_time");

        return workingFaceProgressDefinitionMapper.selectPageWithValues(page, query);
    }

    /**
     * 查询存在有效工作模式且启用接入的工作面
     *
     * @param workModes 查询的工作模式
     * @return /
     */
    @Override
    public List<WorkingFaceProgressDefinition> selectWorkModelAssignedList(List<String> workModes)
    {
        QueryWrapper<WorkingFaceProgressDefinition> queryWrap = new QueryWrapper<>();
        if (workModes != null && !workModes.isEmpty()) {
            queryWrap.in("wsd.work_mode", workModes);
        }

        List<WorkingFaceProgressDefinition> result = new ArrayList<>();
        try {
            result = workingFaceProgressDefinitionMapper.selectListAcqEnabled(queryWrap);
        }
        catch (Exception ex) {
            log.warn("selectWorkModelAssignedList failed", ex);
        }
        return result;
    }

    /**
     * 查询工作模式发生变更或者处在反装告警状态、且存在有效工作模式的工作面
     *
     * @return /
     */
    @Override
    public List<WorkingFaceProgressDefinition> selectWorkModelChangedList()
    {
        QueryWrapper<WorkingFaceProgressDefinition> queryWrap = new QueryWrapper<>();
        // queryWrap.isNull("first_data_time");
        queryWrap.isNotNull("work_mode");
        // 工作面模式发生变更 或者 处在反装告警状态
        queryWrap.and(wrap ->
                              wrap.eq("work_mode_changed", "1")
                                  .or()
                                  .eq("wrong_channel_state", 1)
        );

        List<WorkingFaceProgressDefinition> result = new ArrayList<>();
        try {
            result = workingFaceProgressDefinitionMapper.selectList(queryWrap);
        }
        catch (Exception ex) {
            log.warn("selectWorkModelChangedList failed", ex);
        }
        return result;
    }

    /**
     * 批量写入或者更新
     *
     * @param insertList 插入列表
     */
    @Override
    public void upsertBatch(List<WorkingFaceProgressDefinition> insertList)
    {
        workingFaceProgressDefinitionMapper.upsertWfpdBatch(insertList);
    }

    /**
     * 同步工作面相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置列表
     * @return /
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean submitIndicatorRule(List<WorkingFaceWarnIndicatorSubmitDto> submitDtoList) throws BadRequestException
    {
        // 检查输入
        for (WorkingFaceWarnIndicatorSubmitDto submitDto : submitDtoList) {
            if (StringUtil.isEmpty(submitDto.getWarnType())) {
                throw new BadRequestException("报警类型 不能为空");
            }
            if (StringUtil.isEmpty(submitDto.getMiningFaceId())) {
                throw new BadRequestException("工作面ID 不能为空");
            }
            // if (submitDto.getIndicatorId() == null) {
            //     throw new BadRequestException("指标ID 不能为空");
            // }
        }

        // 工作面ID
        Set<String> workingFaceIds = submitDtoList
                .stream()
                .map(WorkingFaceWarnIndicatorSubmitDto::getMiningFaceId)
                .collect(Collectors.toSet());
        if (workingFaceIds.isEmpty()) {
            throw new BadRequestException("下发指标无效，可能为空");
        }

        QueryWrapper<WorkingFaceProgressDefinition> queryWrap = new QueryWrapper<>();
        if (workingFaceIds.size() == 1) {
            queryWrap.eq("working_face_id", workingFaceIds.iterator().next());
        }
        else {
            queryWrap.in("working_face_id", new ArrayList<>(workingFaceIds));
        }

        // 既存记录
        List<WorkingFaceProgressDefinition> existedDefList = list(queryWrap);
        // 更新保存记录
        List<WorkingFaceProgressDefinition> saveOrUpdateList = new ArrayList<>();

        // 下发规则
        for (WorkingFaceWarnIndicatorSubmitDto submitDto : submitDtoList) {
            if (!"wrongChannel".equalsIgnoreCase(submitDto.getWarnType())) {
                throw new BadRequestException("下发指标类型无效");
            }

            Optional<WorkingFaceProgressDefinition> wfpdOpt = existedDefList
                    .stream()
                    .filter(wf -> wf.getWorkingFaceId().equals(submitDto.getMiningFaceId()))
                    .findAny();
            if (!wfpdOpt.isPresent()) {
                log.warn(
                        "Submit working face may not exists. mineCode: {}, workingFaceId: {}, indicatorId: {}",
                        submitDto.getMineCode(),
                        submitDto.getMiningFaceId(),
                        submitDto.getIndicatorId()
                );
                continue;
            }

            WorkingFaceProgressDefinition wfpd = wfpdOpt.get();
            wfpd.setChannelIndicatorId(submitDto.getIndicatorId());
            wfpd.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

            saveOrUpdateList.add(wfpd);
        }

        try {
            saveOrUpdateBatch(saveOrUpdateList);
            log.info("submitIndicatorRule succeeded: {}", JSON.toJSONString(submitDtoList));

            return true;
        }
        catch (Exception e) {
            log.error("submitIndicatorRule failed: {}", e.getMessage(), e);
        }
        return false;
    }

}
