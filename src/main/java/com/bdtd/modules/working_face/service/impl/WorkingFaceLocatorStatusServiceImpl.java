package com.bdtd.modules.working_face.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.working_face.dao.WorkingFaceLocatorStatusMapper;
import com.bdtd.modules.working_face.dto.WorkingFaceLocatorStatusComposite;
import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus;
import com.bdtd.modules.working_face.service.IWorkingFaceLocatorStatusService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面进尺实时监测 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Slf4j
@Service
public class WorkingFaceLocatorStatusServiceImpl extends ServiceImpl<WorkingFaceLocatorStatusMapper, WorkingFaceLocatorStatus>
    implements IWorkingFaceLocatorStatusService
{
    private final WorkingFaceLocatorStatusMapper workingFaceLocatorStatusMapper;
    private final TaosConnector taosConnector;

    public WorkingFaceLocatorStatusServiceImpl(WorkingFaceLocatorStatusMapper workingFaceLocatorStatusMapper, TaosConnector taosConnector) {
        this.workingFaceLocatorStatusMapper = workingFaceLocatorStatusMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<WorkingFaceLocatorStatus> selectPage(WorkingFaceLocatorStatus queryEntity, Page page) {
        QueryWrapper<WorkingFaceLocatorStatus> queryWrap = new QueryWrapper<>();
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "wfls.group_code", queryEntity.getGroupCode());
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "wfls.mine_code", queryEntity.getMineCode());
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getWorkingFaceId()), "wfls.working_face_id", queryEntity.getWorkingFaceId());
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getLabelCode()), "wfls.label_code", queryEntity.getLabelCode());
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getPointId()), "wfls.point_id", queryEntity.getPointId());
        queryWrap.like(StringUtil.isNotEmpty(queryEntity.getPointDesc()), "wfls.point_desc", queryEntity.getPointDesc());

        return workingFaceLocatorStatusMapper.selectPage(page, queryWrap);
    }

    @Override
    public IPage<WorkingFaceLocatorStatusComposite> selectCompositedPage(WorkingFaceLocatorStatus queryEntity, Page<WorkingFaceLocatorStatusComposite> page)
        throws BadRequestException {
        if (StringUtil.hasInsecureSqlChar(queryEntity.getGroupCode())) {
            throw new BadRequestException("二级公司编码含有非法字符");
        }
        if (StringUtil.hasInsecureSqlChar(queryEntity.getMineCode())) {
            throw new BadRequestException("煤矿编码含有非法字符");
        }
        if (StringUtil.hasInsecureSqlChar(queryEntity.getWorkingFaceId())) {
            throw new BadRequestException("工作面编码含有非法字符");
        }
        if (queryEntity.getWorkingFaceIdList() != null
            && !queryEntity.getWorkingFaceIdList().isEmpty()
            && queryEntity.getWorkingFaceIdList().stream().anyMatch(StringUtil::hasInsecureSqlChar)
        ) {
            throw new BadRequestException("工作面编码含有非法字符");
        }

        // if (StringUtil.isEmpty(queryEntity.getState())) {
        //     queryEntity.setState("good");
        // }
        // else if (Arrays.stream(new String[] { "good", "bad" }).noneMatch(i -> i.equalsIgnoreCase(queryEntity.getState()))) {
        //     throw new BadRequestException("状态参数无效");
        // }
        if (StringUtil.isNotEmpty(queryEntity.getState())
            && Arrays.stream(new String[] { "good", "bad", "all" }).noneMatch(s -> s.equalsIgnoreCase(queryEntity.getState()))
        ) {
            throw new BadRequestException("状态参数无效");
        }

        // // 添加默认排序
        // if (page.orders().isEmpty()) {
        //     page.addOrder(OrderItem.asc("wfpd.group_code"));
        //     page.addOrder(OrderItem.asc("wfpd.mine_code"));
        // }

        // region 测试代码
        // Page<WorkingFaceLocatorStatus> testPage = new Page<>(page.getCurrent(), page.getSize());
        // testPage.addOrder(OrderItem.asc("group_code"));
        // testPage.addOrder(OrderItem.asc("mine_code"));
        // IPage<WorkingFaceLocatorStatus> page1 = workingFaceLocatorStatusMapper.selectPageTest1(queryEntity, testPage);
        // QueryWrapper<WorkingFaceLocatorStatus> queryWrap = new QueryWrapper<>();
        // queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "group_code", queryEntity.getGroupCode());
        // queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "mine_code", queryEntity.getMineCode());
        // IPage<WorkingFaceLocatorStatus> page2 = workingFaceLocatorStatusMapper.selectPageTest2(queryWrap, testPage);
        // endregion 测试代码
        IPage<WorkingFaceLocatorStatusComposite> pageList = workingFaceLocatorStatusMapper.selectCompositedPage(queryEntity, page);

        // 如果有返回数据需要去查时序库获取最新测距
        if (!pageList.getRecords().isEmpty()) {
            // 查询语句
            StringBuffer buffer = new StringBuffer();
            buffer.append("select work_face_id, card_number, last(distance) as distance ");
            buffer.append("from ")
                  .append(TaosConnector.prependDatabaseName(true, "pos_raw_history"))
                  .append(" ");
            // 获取工作面ID+标签卡用来查询历史库
            for (int i = 0; i < pageList.getRecords().size(); i++) {
                WorkingFaceLocatorStatusComposite workingFaceLocatorStatus = pageList.getRecords().get(i);
                if (i == 0) {
                    buffer.append("where (card_number = '")
                          .append(workingFaceLocatorStatus.getLabelCode())
                          .append("' and work_face_id = '")
                          .append(workingFaceLocatorStatus.getWorkingFaceId()).append("') ");
                }
                else {
                    buffer.append("or (card_number = '")
                          .append(workingFaceLocatorStatus.getLabelCode())
                          .append("' and work_face_id = '")
                          .append(workingFaceLocatorStatus.getWorkingFaceId()).append("') ");
                }
            }
            buffer.append("group by work_face_id,card_number");

            try {
                log.info("selectCompositedPage, query dist data: {}", buffer.toString());
                List<PositionRawHistory> positionRawHistories = taosConnector.queryList(buffer.toString(), PositionRawHistory.class);
                if (positionRawHistories != null && !positionRawHistories.isEmpty()) {
                    for (WorkingFaceLocatorStatusComposite rec : pageList.getRecords()) {
                        for (PositionRawHistory positionRawHistory : positionRawHistories) {
                            if (positionRawHistory.getWorkFaceId().equals(rec.getWorkingFaceId())
                                && positionRawHistory.getCardNumber().equals(rec.getLabelCode())
                            ) {
                                rec.setDist(positionRawHistory.getDistance().toString());
                            }
                        }
                    }
                }
            }
            catch (TaosDBException ex) {
                log.error("query dist data error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            }
            catch (Exception e) {
                log.error("query dist data error: {}", e.getMessage(), e);
            }
        }

        return pageList;
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(
        @RequestParam(required = true) String pointId,
        String startTime,
        String endTime,
        String polymerize,
        Integer timeMode
    ) throws Exception {
        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize, true)) {
            throw new BadRequestException("聚合度无效");
        }

        // 点位ID存在检查 
        if (StringUtil.isNotEmpty(pointId)) {
            QueryWrapper<WorkingFaceLocatorStatus> queryWrap = new QueryWrapper<>();
            queryWrap.eq("point_id", pointId);
            int existedCount = workingFaceLocatorStatusMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的点位不存在");
            }
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select ");

        // 根据聚合度参数调整查询字段
        if (StringUtil.isEmpty(polymerize)) {
            buffer.append(
                "`time`, `point_id` as `pointId`, `value`, `state` "
            );
        }
        else {
            buffer.append(
                "`point_id` as `pointId`, last(`value`) as `value`, last(`state`) as `state` "
            );
        }

        buffer.append("from `wf_lctr_stts` ");

        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        boolean isValidTimeMode = true;
        switch (timeMode) {
            case 0:
                buffer.append("where `time` > '").append(dtf.format(lStartTime)).append("' and `time` < '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 1:
                buffer.append("where `time` > '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 2:
                buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` < '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 3:
                buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");
                break;
            default:
                isValidTimeMode = false;
                break;
        }
        // 工作面ID参数
        if (StringUtil.isNotEmpty(pointId)) {
            if (isValidTimeMode) {
                buffer.append("and `point_id` = '").append(pointId).append("' ");
            }
            else {
                buffer.append("where `point_id` = '").append(pointId).append("' ");
            }
        }

        // 聚合度
        if (StringUtil.isNotEmpty(polymerize)) {
            buffer.append("interval(").append(polymerize).append(") ");
            buffer.append("group by `point_id` ");
        }

        buffer.append("order by `time` ASC ");

        String sql = StringUtil.trimJoinedSql(buffer.toString());

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            mapList = taosConnector.query(sql);
            // if (mapList != null) {
            //     for (Map<String, Object> map : mapList) {
            //         String pId = (String) map.get("point_id");
            //         retMap.computeIfAbsent(pId, k -> new ArrayList<>());
            //
            //         List<Map<String, Object>> list = retMap.get(pId);
            //         list.add(map);
            //         retMap.put(pId, list);
            //     }
            // }
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: {}", ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        // return retMap.values().stream().collect(Collectors.toList());
        return mapList;
    }

}
