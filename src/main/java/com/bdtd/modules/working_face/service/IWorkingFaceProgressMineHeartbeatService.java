package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.dto.MineHeartbeatWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressMineHeartbeat;
import com.bdtd.util.exception.BadRequestException;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 矿端采集链路心跳 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
public interface IWorkingFaceProgressMineHeartbeatService extends IService<WorkingFaceProgressMineHeartbeat>
{

    /**
     * 分页查询矿端采集链路心跳数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    IPage<WorkingFaceProgressMineHeartbeat> queryWorkingFaceProgressMineHeartbeatPage(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 查询矿端采集链路心跳数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    List<WorkingFaceProgressMineHeartbeat> queryWorkingFaceProgressMineHeartbeatList(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 最后同步的时间
     */
    Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria);

    /**
     * 同步矿端采集链路心跳
     *
     * @param syncCriteria 同步参数
     * @param actionStr    操作字符串
     * @return /
     */
    PositionCalcSyncResult syncPositionMineHeartbeat(
            PositionCalcQueryCriteria syncCriteria,
            String actionStr
    );

    /**
     * 检查矿端采集链路心跳状态
     *
     * @param checkField        检查字段
     * @param syncQueryCriteria 同步参数
     */
    void checkPositionMineHeartbeat(String checkField, PositionCalcQueryCriteria syncQueryCriteria);

    /**
     * 代理矿端采集链路心跳数据
     *
     * @param queryCriteria 同步条件
     * @return 同步结果
     */
    Object passPositionMineHeartbeat(PositionCalcQueryCriteria queryCriteria);

    /**
     * 同步矿端链路相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置
     * @return /
     */
    boolean submitIndicatorRule(List<MineHeartbeatWarnIndicatorSubmitDto> submitDtoList) throws BadRequestException;

}
