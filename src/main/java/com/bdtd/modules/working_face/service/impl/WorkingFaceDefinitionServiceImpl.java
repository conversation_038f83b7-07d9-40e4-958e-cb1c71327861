package com.bdtd.modules.working_face.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.working_face.dao.WorkingFaceDefinitionMapper;
import com.bdtd.modules.working_face.entity.WorkingFaceDefinition;
import com.bdtd.modules.working_face.service.IWorkingFaceDefinitionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 工作面基本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
public class WorkingFaceDefinitionServiceImpl extends ServiceImpl<WorkingFaceDefinitionMapper, WorkingFaceDefinition> implements IWorkingFaceDefinitionService
{
    private final WorkingFaceDefinitionMapper workingFaceDefinitionMapper;

    public WorkingFaceDefinitionServiceImpl(WorkingFaceDefinitionMapper workingFaceDefinitionMapper)
    {
        this.workingFaceDefinitionMapper = workingFaceDefinitionMapper;
    }

    @Override
    public Timestamp selectLastUpdateTime()
    {
        return workingFaceDefinitionMapper.selectLastUpdateTime();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int upsertBatchWfDef(List<WorkingFaceDefinition> list)
    {
        for (WorkingFaceDefinition workFace : list) {
            // ID 字段
            // - 采煤工作面（mwf）：0 + 原工作面 ID
            // - 掘进工作面（ewf）：1 + 原工作面 ID
            if (WorkingFaceDefinition.FACE_TYPE_MINING.equalsIgnoreCase(workFace.getFaceType())) {
                workFace.setId(String.format("%s%s", "0", workFace.getWorkFaceId()));
            }
            else {
                workFace.setId(String.format("%s%s", "1", workFace.getWorkFaceId()));
            }
        }

        return workingFaceDefinitionMapper.upsertBatchWfDef(list);
    }

    // region 根据更新时间更新工作面的状态等信息

    @Override
    public int updateModelSystemByUpdatedWf(Timestamp startTime, Timestamp endTime)
    {
        return workingFaceDefinitionMapper.updateModelSystemByUpdatedWf(startTime, endTime);
    }

    @Override
    public int updateModelSystemAliveByUpdatedWf(Timestamp startTime, Timestamp endTime)
    {
        return workingFaceDefinitionMapper.updateModelSystemAliveByUpdatedWf(startTime, endTime);
    }

    @Override
    public int updateDataSystemMonitorByUpdatedWf(Timestamp startTime, Timestamp endTime)
    {
        return workingFaceDefinitionMapper.updateDataSystemMonitorByUpdatedWf(startTime, endTime);
    }

    @Override
    public int updateModelSystemAliveStatisticsByUpdatedWf(Timestamp startTime, Timestamp endTime)
    {
        return workingFaceDefinitionMapper.updateModelSystemAliveStatisticsByUpdatedWf(startTime, endTime);
    }

    @Override
    public int updateWorkingFaceProgressDefinitionByUpdatedWf(Timestamp startTime, Timestamp endTime)
    {
        return workingFaceDefinitionMapper.updateWorkingFaceProgressDefinitionByUpdatedWf(startTime, endTime);
    }

    // endregion 根据更新时间更新工作面的状态等信息

    // region 根据指定工作面更新工作面状态等

    @Override
    public void updateDataSystemMonitorStatus(String mineCode, Set<String> wfIds)
    {
        workingFaceDefinitionMapper.updateDataSystemMonitorStatus(mineCode, wfIds);
    }

    @Override
    public void updateModelSystemAliveStatistics(String mineCode, Set<String> wfIds)
    {
        workingFaceDefinitionMapper.updateModelSystemAliveStatistics(mineCode, wfIds);
    }

    // endregion 根据指定工作面更新工作面状态等

    // region 根据启用的工作面更新工作面状态等

    @Override
    public void updateDataSystemMonitorStatusWithEnabledWfd(String mineCode, Set<String> enabledWfIds)
    {
        workingFaceDefinitionMapper.updateDataSystemMonitorStatusWithEnabledWfd(mineCode, enabledWfIds);
    }

    @Override
    public void updateModelSystemAliveStatisticsWithEnabledWfd(String mineCode, Set<String> enabledWfIds)
    {
        workingFaceDefinitionMapper.updateModelSystemAliveStatisticsWithEnabledWfd(mineCode, enabledWfIds);
    }

    // endregion 根据启用的工作面更新工作面状态等
}
