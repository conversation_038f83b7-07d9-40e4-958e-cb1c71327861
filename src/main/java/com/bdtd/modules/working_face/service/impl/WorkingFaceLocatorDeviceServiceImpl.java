package com.bdtd.modules.working_face.service.impl;

import com.bdtd.modules.working_face.dao.WorkingFaceLocatorDeviceMapper;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorDevice;
import com.bdtd.modules.working_face.service.IWorkingFaceLocatorDeviceService;
import com.github.jeffreyning.mybatisplus.service.MppServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工作面定位测距设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class WorkingFaceLocatorDeviceServiceImpl extends MppServiceImpl<WorkingFaceLocatorDeviceMapper, WorkingFaceLocatorDevice> implements IWorkingFaceLocatorDeviceService {

}
