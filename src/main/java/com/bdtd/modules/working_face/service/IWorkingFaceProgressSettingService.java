package com.bdtd.modules.working_face.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.working_face.dto.DepartmentDto;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 工作面解析配置 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface IWorkingFaceProgressSettingService extends IService<WorkingFaceProgressSetting>
{

    /**
     * 获取二级公司列表
     *
     * @return /
     */
    List<DepartmentDto> getCompanyList();

    /**
     * 获取煤矿列表
     *
     * @param companyCode 二级公司编码
     * @return /
     */
    List<DepartmentDto> getMineList(String companyCode);

    /**
     * 取得最新的配置
     *
     * @param mineCode 煤矿编码
     * @return /
     */
    WorkingFaceProgressSetting getLatestSetting(String mineCode);

    /**
     * 根据煤矿编码和配置时间取得煤矿配置
     *
     * @param mineCode   煤矿编码
     * @param updateTime 配置时间
     * @return /
     */
    WorkingFaceProgressSetting selectWithMineCodeAndUpdateTime(String mineCode, Timestamp updateTime);

    /**
     * 解析煤矿的定位解析配置
     * 解析数据来源
     * 1) 接口下发数据直接解析，/workingFace/workProgress/postSettings
     * 2）收到业务侧通知，主动拉取配置进行解析， WorkingFaceProgressSettingUpdateHandler
     * 3）定时任务同步下发配置，WorkingProgressSettingsSyncTask，/workingFace/workProgress/syncSettings
     *
     * @param actionStr              操作描述
     * @param content                定位解析配置
     * @param groupCode              集团编码
     * @param companyCode            二级公司编码
     * @param mineCode               煤矿编码
     * @param lastUpdateTime         上次同步时间
     * @param fullDataOfMine         加载的是否煤矿全量数据
     * @param containsWorkMode       配置是否包含工作模式
     * @param forceUpdateModelSystem 强制更新接入系统信息
     */
    List<ServiceCallLog> parseProgressSettings(
            String actionStr,
            JSONObject content,
            String groupCode,
            String companyCode,
            String mineCode,
            Long lastUpdateTime,
            Boolean fullDataOfMine,
            Boolean containsWorkMode,
            String forceUpdateModelSystem
    );

    /**
     * 同步煤矿配置
     *
     * @param mineCode         煤矿编码
     * @param fullDataOfMine   加载的是否煤矿全量数据
     * @param containsWorkMode 配置是否包含工作模式
     * @param lastUpdateTime   最后上传时间，如果配置则取得大于这个时间的配置
     * @param actionStr        操作区分
     */
    List<ServiceCallLog> syncProgressSettings(
            String mineCode,
            Boolean fullDataOfMine,
            Boolean containsWorkMode,
            Long lastUpdateTime,
            String actionStr
    );

}
