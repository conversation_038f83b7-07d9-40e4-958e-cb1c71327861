package com.bdtd.modules.working_face.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.base.entity.User;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.modules.working_face.dao.WfpMineLocationManufacturerMapper;
import com.bdtd.modules.working_face.entity.WfpLocationManufacturer;
import com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer;
import com.bdtd.modules.working_face.service.IWfpMineLocationManufacturerService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 煤矿定位厂家统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
@Slf4j
public class WfpMineLocationManufacturerServiceImpl extends ServiceImpl<WfpMineLocationManufacturerMapper, WfpMineLocationManufacturer>
        implements IWfpMineLocationManufacturerService
{

    private final WfpMineLocationManufacturerMapper mineLocationManufacturerMapper;

    public WfpMineLocationManufacturerServiceImpl(
            WfpMineLocationManufacturerMapper mineLocationManufacturerMapper
    )
    {
        this.mineLocationManufacturerMapper = mineLocationManufacturerMapper;
    }


    @Override
    public Boolean createOrUpdate(WfpMineLocationManufacturer saveEntity) throws BadRequestException
    {
        // 字段检查
        if (StringUtil.isEmpty(saveEntity.getGroupCode())) {
            throw new BadRequestException("二级公司编码不能为空");
        }
        if (StringUtil.isEmpty(saveEntity.getMineCode())) {
            throw new BadRequestException("煤矿编码不能为空");
        }
        if (saveEntity.getManufacturerId() == null) {
            throw new BadRequestException("厂家未指定");
        }

        // 检查对象是否存在
        QueryWrapper<WfpMineLocationManufacturer> queryWrap = new QueryWrapper<>();
        queryWrap.eq("mine_code", saveEntity.getMineCode());
        // queryWrap.eq("manufacturer_id", saveEntity.getManufacturerId());

        // 是否存在
        WfpMineLocationManufacturer existed = getOne(queryWrap);
        if (existed != null && existed.getId() != null) {
            if (saveEntity.getId() == null) {
                throw new BadRequestException("煤矿厂家信息已存在，不能重复录入");
            }
            else if (!Objects.equals(saveEntity.getId(), existed.getId())) {
                throw new BadRequestException("煤矿厂家信息已录入");
            }
        }

        // 取得当前用户ID
        User currentUser = null;
        try {
            Long userId = BackendUtil.getInstance().getCurrentUserId();
            currentUser = BackendUtil.getInstance().getUserById(userId);
            if (currentUser != null) {
                if (saveEntity.getId() == null) {
                    saveEntity.setCreateUser(currentUser.getAccount());
                }
                else {
                    saveEntity.setUpdateUser(currentUser.getAccount());
                }
            }
        }
        catch (Exception e) {
            log.warn("get currentUser from header failed: {}", e.getMessage(), e);
        }

        // 新增时间
        if (saveEntity.getId() == null) {
            saveEntity.setCreateTime(LocalDateTime.now());
        }

        try {
            Boolean result = null;
            if (saveEntity.getId() == null) {
                result = save(saveEntity);
            }
            else {
                result = updateById(saveEntity);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("保存时发生错误");
        }

        return null;
    }

    @Override
    public WfpMineLocationManufacturer findById(Long id)
    {
        return mineLocationManufacturerMapper.findById(id);
    }

    @Override
    public List<WfpMineLocationManufacturer> findList(WfpMineLocationManufacturer queryEntity)
    {
        return mineLocationManufacturerMapper.findList(generateQueryWrapper(queryEntity));
    }

    @Override
    public Page<WfpMineLocationManufacturer> findPage(Page<WfpMineLocationManufacturer> page, WfpMineLocationManufacturer queryEntity)
    {
        return mineLocationManufacturerMapper.findPage(page, generateQueryWrapper(queryEntity));
    }

    private QueryWrapper<WfpMineLocationManufacturer> generateQueryWrapper(WfpMineLocationManufacturer queryEntity)
    {
        QueryWrapper<WfpMineLocationManufacturer> queryWrap = Wrappers.query(queryEntity);
        queryWrap.orderByDesc("group_sort", "mine_sort");

        // 查询条件
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getGroupCode()), "wmlm.group_code", queryEntity.getGroupCode());
        queryWrap.eq(StringUtil.isNotEmpty(queryEntity.getMineCode()), "wmlm.mine_code", queryEntity.getMineCode());
        queryWrap.eq(queryEntity.getManufacturerId() != null, "wmlm.manufacturer_id", queryEntity.getManufacturerId());
        queryWrap.eq(queryEntity.getManufacturerName() != null, "wlm.name", String.format("%%%s%%", queryEntity.getManufacturerName()));
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatSignal()),
                "wmlm.feat_signal",
                queryEntity.getFeatSignal()
        );
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatPower()),
                "wmlm.feat_power",
                queryEntity.getFeatPower()
        );
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatPowerAlarm()),
                "wmlm.feat_power_alarm",
                queryEntity.getFeatPowerAlarm()
        );

        queryWrap.isNull(
                queryEntity.getDeletedAtIsNull() == null || Objects.equals(1, queryEntity.getDeletedAtIsNull()),
                "wmlm.deleted_at"
        );
        queryWrap.isNotNull(
                Objects.equals(0, queryEntity.getDeletedAtIsNull()),
                "wmlm.deleted_at"
        );

        return queryWrap;
    }

}
