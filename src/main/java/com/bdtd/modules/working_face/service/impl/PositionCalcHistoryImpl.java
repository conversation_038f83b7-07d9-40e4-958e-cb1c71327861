package com.bdtd.modules.working_face.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.PositionCalcHistory;
import com.bdtd.modules.working_face.service.IPositionCalcHistoryService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import com.bdtd.util.tdengine.entity.TaosStableEntity;
import com.bdtd.util.tdengine.entity.TaosTableEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpRequest;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.io.support.ClassicRequestBuilder;
import org.apache.hc.core5.net.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 定位解算原始数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@Service
@Slf4j
public class PositionCalcHistoryImpl implements IPositionCalcHistoryService
{
    @Resource
    private TaosConnector taosConnector;

    @Resource(name = "positionHistoryLastSyncTimeMap")
    private ConcurrentHashMap<String, Timestamp> positionHistoryLastSyncTimeMap;

    @Value("${scheduled.position-calc-history-sync-url}")
    private String positionCalcBizUrl;

    /**
     * 同步人员定位解算历史数据
     *
     * @param syncCriteria 同步条件
     * @param actionStr    操作区分
     * @return 同步结果
     */
    @Override
    public PositionCalcSyncResult syncPositionCalcHistory(
            PositionCalcQueryCriteria syncCriteria,
            String actionStr
    )
    {
        PositionCalcSyncResult result = new PositionCalcSyncResult();
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(syncCriteria.getStartDate())) {
            queryMap.put("startDate", syncCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getEndDate())) {
            queryMap.put("endDate", syncCriteria.getEndDate());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getMineCode())) {
            queryMap.put("mineCode", syncCriteria.getMineCode());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getWorkFaceId())) {
            queryMap.put("workFaceId", syncCriteria.getWorkFaceId());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getStationCode())) {
            queryMap.put("stationCode", syncCriteria.getStationCode());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getCardNumber())) {
            queryMap.put("cardNumber", syncCriteria.getCardNumber());
        }

        JSONObject respObj = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet;
            // 接口代理
            if (positionCalcBizUrl.endsWith("proxy")) {
                URIBuilder uriBuilder = new URIBuilder(positionCalcBizUrl);
                for (Map.Entry<String, Object> param : queryMap.entrySet()) {
                    uriBuilder.addParameter(param.getKey(), param.getValue().toString());
                }
                httpGet = ClassicRequestBuilder.get(uriBuilder.toString()).build();
            } else {
                httpGet = ClassicRequestBuilder.get(positionCalcBizUrl).build();
                StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
                httpGet.setEntity(entity);
            }

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    respObj = JSON.parseObject(is, JSONObject.class);
                    log.debug(String.format(
                            "syncPositionCalcHistory return result (startDate: %s, endDate: %s, mineCode: %s, workFaceId: %s): %s",
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate(),
                            "null",
                            "null",
                            (respObj == null ? "" : respObj.toJSONString())
                    ));
                }
            }
        }
        catch (Exception ex) {
            String errMsg = String.format(
                "%s 接口调用错误（%s ~ %s）, %s",
                actionStr,
                syncCriteria.getStartDate(),
                syncCriteria.getEndDate(),
                ex.getMessage()
            );
            serviceCallLogs.add(ServiceCallLog.createNew(
                errMsg,
                (byte) 1,
                "0"
            ));
            log.error("{}: {}", positionCalcBizUrl, errMsg, ex);

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        // 返回内容无效
        if (respObj == null
            || !respObj.containsKey("data")
            || respObj.getJSONArray("data") == null
        ) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    String.format(
                            "%s 返回内容为空（%s ~ %s）",
                            actionStr,
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate()
                    ),
                    (byte) 1,
                    "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
        // 返回内容为空
        if (respObj.getJSONArray("data").isEmpty()) {
            // 返回数据为空，即此时间段没有数据，记录时间，防止重复同步该时间区间
            positionHistoryLastSyncTimeMap.put("calc", Timestamp.valueOf(syncCriteria.getEndDate()));

            serviceCallLogs.add(ServiceCallLog.createNew(
                    String.format(
                            "%s 无同步数据（%s ~ %s）",
                            actionStr,
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate()
                    ),
                    (byte) 0,
                    "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        List<PositionCalcHistory> insertList = new ArrayList<>();
        Map<String, Integer> insertLogMap = new HashMap<>();

        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        JSONArray historyArr = null;

        try {
            historyArr = respObj.getJSONArray("data");
            for (Object obj : historyArr) {
                JSONObject jsonObj = (JSONObject) obj;

                // 基于基站的数据列表
                List<PositionCalcHistory> itemList = new ArrayList<>();

                // 基站原始数据
                String mqInfoStr = jsonObj.getString("mqInfo");
                // 解析队列消息
                if (StringUtil.isNotEmpty(mqInfoStr)) {
                    try {
                        List<JSONObject> mqList = JSON.parseArray(mqInfoStr, JSONObject.class);
                        if (mqList != null && !mqList.isEmpty()) {
                            for (JSONObject stationObj : mqList) {
                                PositionCalcHistory stationItem = new PositionCalcHistory();
                                stationItem.setMqInfo(mqInfoStr);
                                stationItem.setStationCode(stationObj.getString("bs_sn"));
                                stationItem.setChannel(stationObj.getInteger("channel"));
                                stationItem.setDistance(stationObj.getInteger("dist"));
                                stationItem.setDataTime(
                                        new Timestamp(stationObj.getLong("timestamp") * 1000)
                                );
                                itemList.add(stationItem);
                            }
                        }
                    }
                    catch (Exception ex) {
                        log.warn(String.format("mqInfo string parse error: %s", mqInfoStr), ex);
                    }
                }

                // 未解析出基站数据
                if (itemList.isEmpty()) {
                    PositionCalcHistory historyObj = new PositionCalcHistory();
                    historyObj.setMqInfo(mqInfoStr);
                    itemList.add(historyObj);
                }

                // 赋值基础字段
                for (PositionCalcHistory historyObj : itemList) {
                    historyObj.setUpdateTime(jsonObj.getObject("updateTime", Timestamp.class));
                    historyObj.setX(jsonObj.getString("x"));
                    historyObj.setY(jsonObj.getString("y"));
                    historyObj.setPowerValue(jsonObj.getInteger("powerValue"));
                    historyObj.setSignalValue(jsonObj.getInteger("signalValue"));
                    historyObj.setOffline(jsonObj.getString("offLine"));
                    historyObj.setUpdatedAt(nowTime);
                    historyObj.setCardNumber(jsonObj.getString("cardNumber"));
                    historyObj.setWorkFaceId(jsonObj.getString("workFaceId"));
                    historyObj.setWorkFaceName(jsonObj.getString("workFaceName"));
                    historyObj.setMineCode(jsonObj.getString("mineCode"));

                    // String logKey = String.format(
                    //         "%s,%s,%s",
                    //         historyObj.getWorkFaceId(),
                    //         historyObj.getWorkFaceName(),
                    //         historyObj.getMineCode()
                    // );
                    String logKey = historyObj.getMineCode();
                    insertLogMap.computeIfAbsent(logKey, k -> 0);
                    insertLogMap.put(logKey, insertLogMap.get(logKey) + 1);
                }

                insertList.addAll(itemList);
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    String.format(
                            "%s 接口返回解析错误（%s ~ %s）",
                            actionStr,
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate()
                    ),
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);
        }

        try {
            if (!insertList.isEmpty()) {
                bulkCreate(insertList);
            }

            // entry: mineCode -> 定位解算记录数
            for (Map.Entry<String, Integer> entry : insertLogMap.entrySet()) {
                String[] keyArr = entry.getKey().split(",");
                log.debug(
                        "Position calc data ({} - {}) sync result: {} => {} insert, {} received",
                        syncCriteria.getStartDate(),
                        syncCriteria.getEndDate(),
                        keyArr[0],
                        entry.getValue(),
                        historyArr.size()
                );

                serviceCallLogs.add(ServiceCallLog.createNew(
                        String.format(
                                "%s 完成数据同步（%s ~ %s, 插入 %d, 总共 %d, 获取 %d）",
                                actionStr,
                                syncCriteria.getStartDate(),
                                syncCriteria.getEndDate(),
                                entry.getValue(),
                                insertList.size(),
                                historyArr.size()
                        ),
                        (byte) 0,
                        keyArr[0]
                ));
            }

            // 返回数据不为空，清空缓存的最后同步时间，继续从历史库取得最后同步时间
            positionHistoryLastSyncTimeMap.remove("calc");

            // // 检查接入数量
            // int logMapTotal = insertLogMap.values().stream().mapToInt(Integer::intValue).sum();
            // if (historyArr != null && logMapTotal != historyArr.size()) {
            //     log.warn(String.format(
            //             "Position calc data sync not all done, %s ~ %s, %d received, %d real insert, details: %s",
            //             syncCriteria.getStartDate(),
            //             syncCriteria.getEndDate(),
            //             logMapTotal,
            //             historyArr.size(),
            //             insertLogMap.entrySet()
            //                         .stream()
            //                         .map(e -> e.getKey() + "=" + e.getValue())
            //                         .collect(Collectors.joining("&"))
            //     ));
            // }
            // else {
            //     log.info(
            //             "Position calc data sync all done, {} ~ {}, {} received, {} real insert, details: {}",
            //             syncCriteria.getStartDate(),
            //             syncCriteria.getEndDate(),
            //             logMapTotal,
            //             historyArr.size(),
            //             insertLogMap.entrySet()
            //                         .stream()
            //                         .map(e -> e.getKey() + "=" + e.getValue())
            //                         .collect(Collectors.joining("&"))
            //     );
            // }

            result.setSucceed(true);
            result.setCount(insertList.size());
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    String.format(
                            "%s 完成数据同步（%s ~ %s）",
                            actionStr,
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate()
                    ),
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
    }

    /**
     * 代理人员定位解算历史数据
     *
     * @param queryCriteria 同步条件
     * @return 同步结果
     */
    @Override
    public Object passPositionCalcHistory(PositionCalcQueryCriteria queryCriteria)
    {
        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStartDate())) {
            queryMap.put("startDate", queryCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
            queryMap.put("endDate", queryCriteria.getEndDate());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            queryMap.put("mineCode", queryCriteria.getMineCode());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            queryMap.put("workFaceId", queryCriteria.getWorkFaceId());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            queryMap.put("stationCode", queryCriteria.getStationCode());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            queryMap.put("cardNumber", queryCriteria.getCardNumber());
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet;
            // 接口代理
            if (positionCalcBizUrl.endsWith("proxy")) {
                URIBuilder uriBuilder = new URIBuilder(positionCalcBizUrl);
                for (Map.Entry<String, Object> param : queryMap.entrySet()) {
                    uriBuilder.addParameter(param.getKey(), param.getValue().toString());
                }
                httpGet = ClassicRequestBuilder.get(uriBuilder.toString()).build();
            } else {
                httpGet = ClassicRequestBuilder.get(positionCalcBizUrl).build();
                StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
                httpGet.setEntity(entity);
            }

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    return JSON.parseObject(is, JSONObject.class);
                }
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    /**
     * 分页查询人员定位解算历史数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public Map<String, Object> queryPositionCalcHistoryPage(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
            throws BadRequestException
    {
        // 查询时间范围
        if ((StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate()))
            && (StringUtil.isEmpty(queryCriteria.getDataStart()) || StringUtil.isEmpty(queryCriteria.getDataEnd()))
        ) {
            throw new BadRequestException("未提供查询时间范围");
        }
        // 基站编码、标签卡号对应关系列表字符串, 基站1,标签卡1;基站2,标签卡2;
        Set<String[]> stationCardSet = new HashSet<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStationCards())) {
            stationCardSet = Arrays.stream(queryCriteria.getStationCards().split(";"))
                                   .filter(StringUtil::isNotEmpty)
                                   .map(s -> s.split(","))
                                   .collect(Collectors.toSet());
            if (stationCardSet.stream().anyMatch(sc -> sc.length < 2)) {
                throw new BadRequestException("未提供完整的基站编码、标签卡号");
            }
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate()) && StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        LocalDateTime lDataStart = null;
        LocalDateTime lDataEnd = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getDataStart()) && StringUtil.isNotEmpty(queryCriteria.getDataEnd())) {
                lDataStart = LocalDateTime.parse(queryCriteria.getDataStart(), inputDtf);
                lDataEnd = LocalDateTime.parse(queryCriteria.getDataEnd(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("数据时间格式错误");
        }

        // 开始时间应早于结束时间
        if (lStartTime != null && !lStartTime.isBefore(lEndTime)) {
            throw new BadRequestException("开始时间应早于结束时间");
        }
        // 数据开始时间应早于数据结束时间
        if (lDataStart != null && !lDataEnd.isBefore(lEndTime)) {
            throw new BadRequestException("数据开始时间应早于数据结束时间");
        }

        // if (!TaosConnector.isPolymerizeValid(polymerize, false)) {
        //     throw new BadRequestException("聚合度无效");
        // }

        // 超级表信息
        TaosStableEntity stable = PositionCalcHistory.buildTaosStableEntity();

        // 查询字段
        String queryFieldStr = "`ts` as `update_time`, `x`, `y`, `power_value`, `signal_value`, `offline`, `channel`, `distance`, `data_time`, `mq_info`, "
                               + "`updated_at`, "
                               + "`card_number`, `station_code`, `work_face_id`, `work_face_name`, `mine_code` ";

        // 表名
        String tableName = stable.getName();
        // if (!StringUtil.isEmpty(queryCriteria.getWorkFaceId())) {
        //     tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        // }

        // 查询条件
        StringBuffer whereBuffer = new StringBuffer();

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        int timeMode = queryCriteria.getTimeMode() == null
                       ? 2
                       : queryCriteria.getTimeMode();
        switch (timeMode) {
            case 0:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 1:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 3:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 2:
            default:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
        }

        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBuffer.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("' ");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }
        // 工作面名称
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceName())) {
            whereBuffer.append("and `work_face_name` like '").append(queryCriteria.getWorkFaceName()).append("%' ");
        }
        // 基站编码、标签卡号
        if (!stationCardSet.isEmpty()) {
            String scWhereStr = stationCardSet
                    .stream()
                    .map(sca -> String.format("(`station_code` = '%s' and `card_number` = '%s')", sca[0], sca[1]))
                    .collect(Collectors.joining(" or "));
            whereBuffer.append(String.format("and (%s)", scWhereStr));
        }
        // 基站编码
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            whereBuffer.append("and `station_code` like '%").append(queryCriteria.getStationCode()).append("%' ");
        }
        // 标签卡号
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            whereBuffer.append("and `card_number` like '%").append(queryCriteria.getCardNumber()).append("%' ");
        }
        // 是否断线
        if (StringUtil.isNotEmpty(queryCriteria.getOffline())) {
            whereBuffer.append("and `offline` = '").append(queryCriteria.getOffline()).append("' ");
        }
        // 通道
        if (queryCriteria.getChannel() != null) {
            whereBuffer.append("and `channel` = ").append(queryCriteria.getChannel()).append(" ");
        }
        // 距离范围低
        if (queryCriteria.getMinDistance() != null) {
            whereBuffer.append("and `distance` >= ").append(queryCriteria.getMinDistance()).append(" ");
        }
        // 距离范围高
        if (queryCriteria.getMaxDistance() != null) {
            whereBuffer.append("and `distance` <= ").append(queryCriteria.getMaxDistance()).append(" ");
        }

        // 查询条件
        StringBuffer limitBuffer = new StringBuffer();

        // 排序字段
        String order = "ts";
        boolean asc = false;
        if (page.getOrders() != null && !page.getOrders().isEmpty()) {
            order = page.getOrders().get(0).getColumn();
            asc = page.getOrders().get(0).isAsc();
        }
        if (!asc) {
            limitBuffer.append(String.format("order by `%s` desc ", order));
        }
        else {
            limitBuffer.append(String.format("order by `%s` asc ", order));
        }

        // 分页
        long current = page.getCurrent() < 1 ? 1 : page.getCurrent();
        limitBuffer.append(String.format("limit %d,%d ", (current - 1) * page.getSize(), page.getSize()));

        // 返回内容
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("current", current);
        retMap.put("size", page.getSize());

        try {
            // 查询列表
            String listSql = "select " + queryFieldStr
                             + " from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                             + (whereBuffer.length() > 0 ? "where " + whereBuffer.substring(4) : "") + " "
                             + limitBuffer;
            List<PositionCalcHistory> content = taosConnector.queryList(listSql, PositionCalcHistory.class);
            retMap.put("content", content);

            // 查询数量
            String countSql = "select count(1) from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                              + (whereBuffer.length() > 0 ? " where " + whereBuffer.substring(4) : "");
            Long total = taosConnector.queryCount(countSql);

            total = total == null ? 0 : total;
            retMap.put("total", total);
            retMap.put("pages", Math.ceil((double) total / page.getSize()));

            return retMap;
        }
        catch (TaosDBException ex) {
            log.error("list error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 查询人员定位解算历史数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public Map<String, Object> queryPositionCalcHistoryList(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
            throws BadRequestException
    {
        // 查询时间范围
        if ((StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate()))
            && (StringUtil.isEmpty(queryCriteria.getDataStart()) || StringUtil.isEmpty(queryCriteria.getDataEnd()))
        ) {
            throw new BadRequestException("未提供查询时间范围");
        }
        // 基站编码、标签卡号对应关系列表字符串, 基站1,标签卡1;基站2,标签卡2;
        Set<String[]> stationCardSet = new HashSet<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStationCards())) {
            stationCardSet = Arrays.stream(queryCriteria.getStationCards().split(";"))
                                   .filter(StringUtil::isNotEmpty)
                                   .map(s -> s.split(","))
                                   .collect(Collectors.toSet());
            if (stationCardSet.stream().anyMatch(sc -> sc.length < 2)) {
                throw new BadRequestException("未提供完整的基站编码、标签卡号");
            }
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate()) && StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        LocalDateTime lDataStart = null;
        LocalDateTime lDataEnd = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getDataStart()) && StringUtil.isNotEmpty(queryCriteria.getDataEnd())) {
                lDataStart = LocalDateTime.parse(queryCriteria.getDataStart(), inputDtf);
                lDataEnd = LocalDateTime.parse(queryCriteria.getDataEnd(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("数据时间格式错误");
        }

        // 开始时间应早于结束时间
        if (lStartTime != null && !lStartTime.isBefore(lEndTime)) {
            throw new BadRequestException("开始时间应早于结束时间");
        }
        // 数据开始时间应早于数据结束时间
        if (lDataStart != null && !lDataStart.isBefore(lDataEnd)) {
            throw new BadRequestException("数据开始时间应早于数据结束时间");
        }

        // if (!TaosConnector.isPolymerizeValid(polymerize, false)) {
        //     throw new BadRequestException("聚合度无效");
        // }

        // 超级表信息
        TaosStableEntity stable = PositionCalcHistory.buildTaosStableEntity();

        // 查询字段
        String queryFieldStr = "`ts` as `update_time`, `x`, `y`, `power_value`, `signal_value`, `offline`, `channel`, `distance`, `data_time`, `mq_info`, "
                               + "`updated_at`, "
                               + "`card_number`, `station_code`, `work_face_id`, `work_face_name`, `mine_code` ";

        // 表名
        String tableName = stable.getName();
        // if (!StringUtil.isEmpty(queryCriteria.getWorkFaceId())) {
        //     tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        // }

        // 查询条件
        StringBuffer whereBuffer = new StringBuffer();

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        int timeMode = queryCriteria.getTimeMode() == null
                       ? 2
                       : queryCriteria.getTimeMode();
        switch (timeMode) {
            case 0:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 1:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 3:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 2:
            default:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
        }

        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBuffer.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("' ");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }
        // 工作面名称
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceName())) {
            whereBuffer.append("and `work_face_name` like '").append(queryCriteria.getWorkFaceName()).append("%' ");
        }
        // 基站编码、标签卡号
        if (!stationCardSet.isEmpty()) {
            String scWhereStr = stationCardSet
                    .stream()
                    .map(sca -> String.format("(`station_code` = '%s' and `card_number` = '%s')", sca[0], sca[1]))
                    .collect(Collectors.joining(" or "));
            whereBuffer.append(String.format("and (%s)", scWhereStr));
        }
        // 基站编码
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            whereBuffer.append("and `station_code` like '%").append(queryCriteria.getStationCode()).append("%' ");
        }
        // 标签卡号
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            whereBuffer.append("and `card_number` like '%").append(queryCriteria.getCardNumber()).append("%' ");
        }
        // 是否断线
        if (StringUtil.isNotEmpty(queryCriteria.getOffline())) {
            whereBuffer.append("and `offline` = '").append(queryCriteria.getOffline()).append("' ");
        }
        // 通道
        if (queryCriteria.getChannel() != null) {
            whereBuffer.append("and `channel` = ").append(queryCriteria.getChannel()).append(" ");
        }
        // 距离范围低
        if (queryCriteria.getMinDistance() != null) {
            whereBuffer.append("and `distance` >= ").append(queryCriteria.getMinDistance()).append(" ");
        }
        // 距离范围高
        if (queryCriteria.getMaxDistance() != null) {
            whereBuffer.append("and `distance` <= ").append(queryCriteria.getMaxDistance()).append(" ");
        }

        // 查询条件
        StringBuffer limitBuffer = new StringBuffer();

        // 排序字段
        String order = "ts";
        boolean asc = false;
        if (page.getOrders() != null && !page.getOrders().isEmpty()) {
            order = page.getOrders().get(0).getColumn();
            asc = page.getOrders().get(0).isAsc();
        }
        if (!asc) {
            limitBuffer.append(String.format("order by `%s` desc ", order));
        }
        else {
            limitBuffer.append(String.format("order by `%s` asc ", order));
        }

        // 返回内容
        Map<String, Object> retMap = new HashMap<>();

        try {
            // 查询列表
            String listSql = "select "
                             + queryFieldStr
                             + " from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                             + (whereBuffer.length() > 0 ? "where " + whereBuffer.substring(4) : "") + " "
                             + limitBuffer;
            List<PositionCalcHistory> content = taosConnector.queryList(listSql, PositionCalcHistory.class);
            retMap.put("content", content);

            return retMap;
        }
        catch (TaosDBException ex) {
            log.error("list error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 得最后同步的时间
     */
    @Override
    public Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria)
    {
        // 超级表信息
        TaosStableEntity stable = PositionCalcHistory.buildTaosStableEntity();
        // 普通表名
        String tableName = stable.getName();
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        }

        // 查询字段
        String queryFieldStr = "LAST(`ts`) as latestTs";

        // 查询条件
        StringBuilder whereBuffer = new StringBuilder();
        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("'");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBuffer.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("'");
        }

        Timestamp latestTs = null;
        try {
            // 查询列表
            String listSql = "select " + queryFieldStr
                             + " from " + TaosConnector.prependDatabaseName(true, tableName) + (
                                     whereBuffer.length() > 0
                                     ? " where " + whereBuffer.substring(5)
                                     : ""
                             );

            latestTs = taosConnector.queryField(listSql);
        }
        catch (TaosDBException ex) {
            log.error("query latest time error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("query latest time error: {}", e.getMessage(), e);
        }

        return latestTs;
    }

    /**
     * 写入解算历史数据
     *
     * @param historyObj 解算历史数据内容
     * @throws BadRequestException 输入验证错误
     */
    @Override
    public void create(PositionCalcHistory historyObj) throws BadRequestException
    {
        List<String> errList = new ArrayList<>();
        if (historyObj.getUpdateTime() == null) {
            errList.add("未提供采集时间");
        }
        if (StringUtil.isEmpty(historyObj.getWorkFaceId())) {
            errList.add("未提供工作面ID");
        }
        if (StringUtil.isEmpty(historyObj.getMineCode())) {
            errList.add("未提供煤矿编码");
        }

        if (!errList.isEmpty()) {
            throw new BadRequestException(String.join(",", errList));
        }

        // 插入表内容
        TaosTableEntity table = historyObj.buildTaosTableEntity();

        try {
            taosConnector.insert(table);
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 批量写入解算历史数据
     *
     * @param historyList 解算历史数据内容列表
     * @throws BadRequestException 输入验证错误
     */
    @Override
    public void bulkCreate(List<PositionCalcHistory> historyList) throws BadRequestException
    {
        List<String> errSet = new ArrayList<>();
        historyList.forEach(historyObj -> {
            if (historyObj.getUpdateTime() == null) {
                errSet.add("未提供采集时间");
            }
            if (StringUtil.isEmpty(historyObj.getWorkFaceId())) {
                errSet.add("未提供工作面ID");
            }
            if (StringUtil.isEmpty(historyObj.getMineCode())) {
                errSet.add("未提供煤矿编码");
            }
        });

        if (!errSet.isEmpty()) {
            throw new BadRequestException(String.join(",", errSet));
        }

        List<TaosTableEntity> insTableList = new ArrayList<>();
        historyList.forEach(historyObj -> {
            insTableList.add(historyObj.buildTaosTableEntity());
        });

        try {
            if (!insTableList.isEmpty()) {
                taosConnector.insert(insTableList);
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

}
