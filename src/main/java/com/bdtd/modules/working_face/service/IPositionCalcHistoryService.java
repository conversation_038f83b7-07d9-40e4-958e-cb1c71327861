package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.PositionCalcHistory;
import com.bdtd.util.exception.BadRequestException;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定位解算历史数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
public interface IPositionCalcHistoryService
{

    /**
     * 同步人员定位解算历史数据
     *
     * @param syncCriteria 同步条件
     * @param actionStr    操作区分
     * @return 同步结果
     */
    PositionCalcSyncResult syncPositionCalcHistory(PositionCalcQueryCriteria syncCriteria, String actionStr);

    /**
     * 代理人员定位解算历史数据
     *
     * @param queryCriteria 同步条件
     * @return 返回结果
     */
    Object passPositionCalcHistory(PositionCalcQueryCriteria queryCriteria);

    /**
     * 分页查询人员定位解算历史数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    Map<String, Object> queryPositionCalcHistoryPage(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 查询人员定位解算历史数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    Map<String, Object> queryPositionCalcHistoryList(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 得最后同步的时间
     */
    Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria);

    /**
     * 写入解算历史数据
     *
     * @param historyObj 解算历史数据内容
     * @throws BadRequestException 输入验证错误
     */
    void create(PositionCalcHistory historyObj) throws BadRequestException;

    /**
     * 批量写入解算历史数据
     *
     * @param historyList 解算历史数据内容列表
     * @throws BadRequestException 输入验证错误
     */
    void bulkCreate(List<PositionCalcHistory> historyList) throws BadRequestException;

}
