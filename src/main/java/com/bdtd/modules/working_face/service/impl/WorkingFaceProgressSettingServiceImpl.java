package com.bdtd.modules.working_face.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.conf.MesBusinessEnum;
import com.bdtd.feign.GisBaseFeignService;
import com.bdtd.feign.MineBizFeignService;
import com.bdtd.modules.data_access.entity.ModelSystem;
import com.bdtd.modules.data_access.service.IModelSystemService;
import com.bdtd.modules.monitor.entity.ModelSystemAlive;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IModelSystemAliveService;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressDefinitionMapper;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressSettingMapper;
import com.bdtd.modules.working_face.dto.DepartmentDto;
import com.bdtd.modules.working_face.dto.ModelSystemInitResult;
import com.bdtd.modules.working_face.dto.WorkingFaceSettingCacheResult;
import com.bdtd.modules.working_face.dto.WorkingFaceSyncInfo;
import com.bdtd.modules.working_face.entity.*;
import com.bdtd.modules.working_face.service.IWorkingFaceDefinitionService;
import com.bdtd.modules.working_face.service.IWorkingFaceEventService;
import com.bdtd.modules.working_face.service.IWorkingFaceLocatorDeviceService;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressSettingService;
import com.bdtd.util.DateUtil;
import com.bdtd.util.SnowflakeUtil;
import com.bdtd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bdtd.handlers.base.AbstractMessageHandler.distinctByField;

/**
 * <p>
 * 工作面解析配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
@Slf4j
public class WorkingFaceProgressSettingServiceImpl extends ServiceImpl<WorkingFaceProgressSettingMapper, WorkingFaceProgressSetting>
        implements IWorkingFaceProgressSettingService
{
    // region 成员变量 & 构造函数

    private final WorkingFaceProgressSettingMapper workingFaceProgressSettingMapper;
    private final WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper;
    private final IModelSystemService modelSystemService;
    private final IModelSystemAliveService modelSystemAliveService;
    private final IWorkingFaceEventService workingFaceEventService;
    private final IWorkingFaceDefinitionService workingFaceDefinitionService;
    private final MineBizFeignService mineBizFeignService;
    private final IWorkingFaceLocatorDeviceService workingFaceLocatorDeviceService;
    private final GisBaseFeignService gisBaseFeignService;

    @Value("${biz.working-face-state-include-work-mode}")
    private Boolean stateIncludeWorkMode;

    @Value("#{'${biz.mining-face-working-state-list:}'.empty ? null : '${biz.mining-face-working-state-list:}'.split(',')}")
    private List<String> miningFaceWorkingStateList;
    private List<String> miningFaceWorkingStateTextList;
    private List<String> miningFaceWorkingStateTextListV2;
    @Value("#{'${biz.driving-face-working-state-list:}'.empty ? null : '${biz.driving-face-working-state-list:}'.split(',')}")
    private List<String> drivingFaceWorkingStateList;
    private List<String> drivingFaceWorkingStateTextList;

    public WorkingFaceProgressSettingServiceImpl(
            WorkingFaceProgressSettingMapper workingFaceProgressSettingMapper,
            WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper,
            IModelSystemService modelSystemService,
            IModelSystemAliveService modelSystemAliveService,
            IWorkingFaceEventService workingFaceEventService,
            IWorkingFaceDefinitionService workingFaceDefinitionService,
            MineBizFeignService mineBizFeignService,
            IWorkingFaceLocatorDeviceService workingFaceLocatorDeviceService,
            GisBaseFeignService gisBaseFeignService
    )
    {
        this.workingFaceProgressSettingMapper = workingFaceProgressSettingMapper;
        this.workingFaceProgressDefinitionMapper = workingFaceProgressDefinitionMapper;
        this.modelSystemService = modelSystemService;
        this.modelSystemAliveService = modelSystemAliveService;
        this.workingFaceEventService = workingFaceEventService;
        this.workingFaceDefinitionService = workingFaceDefinitionService;
        this.mineBizFeignService = mineBizFeignService;
        this.workingFaceLocatorDeviceService = workingFaceLocatorDeviceService;
        this.gisBaseFeignService = gisBaseFeignService;
    }

    @PostConstruct
    public void init()
    {
        // 采煤面状态 描述
        // V1：待开采，生产中，已暂停，停采
        Map<String, String> miningFaceWorkingStateMap = new HashMap<String, String>();
        miningFaceWorkingStateMap.put("1", "待开采");
        miningFaceWorkingStateMap.put("2", "生产中");
        miningFaceWorkingStateMap.put("3", "已暂停");
        miningFaceWorkingStateMap.put("4", "停采");
        // V2：设计，掘进，贯通，治灾，生产，暂停，停采
        Map<String, String> miningFaceWorkingStateMapV2 = new HashMap<String, String>();
        miningFaceWorkingStateMapV2.put("10", "设计");
        miningFaceWorkingStateMapV2.put("11", "掘进");
        miningFaceWorkingStateMapV2.put("12", "贯通");
        miningFaceWorkingStateMapV2.put("13", "治灾");
        miningFaceWorkingStateMapV2.put("20", "生产");
        miningFaceWorkingStateMapV2.put("30", "暂停");
        miningFaceWorkingStateMapV2.put("40", "停采");

        miningFaceWorkingStateTextList = miningFaceWorkingStateList
                .stream()
                .filter(s -> s != null && !s.isEmpty())
                .map(miningFaceWorkingStateMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        miningFaceWorkingStateTextListV2 = miningFaceWorkingStateList
                .stream()
                .filter(s -> s != null && !s.isEmpty())
                .map(miningFaceWorkingStateMapV2::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 掘进面状态 描述
        Map<String, String> drivingFaceWorkingStateMap = new HashMap<String, String>();
        drivingFaceWorkingStateMap.put("1", "待施工");
        drivingFaceWorkingStateMap.put("2", "施工中");
        drivingFaceWorkingStateMap.put("3", "已暂停");
        drivingFaceWorkingStateMap.put("4", "停掘");

        drivingFaceWorkingStateTextList = drivingFaceWorkingStateList
                .stream()
                .filter(s -> s != null && !s.isEmpty())
                .map(drivingFaceWorkingStateMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // endregion 成员变量 & 构造函数

    // region 获取二级公司列表

    /**
     * 获取二级公司列表
     *
     * @return /
     */
    @Override
    public List<DepartmentDto> getCompanyList()
    {
        return workingFaceProgressSettingMapper.selectCompanyList();
    }

    // endregion 获取二级公司列表

    // region 获取煤矿列表

    /**
     * 获取煤矿列表
     *
     * @param companyCode 二级公司编码
     * @return /
     */
    @Override
    public List<DepartmentDto> getMineList(String companyCode)
    {
        return workingFaceProgressSettingMapper.selectMineList(companyCode);
    }

    // endregion 获取煤矿列表

    // region 取得最新的配置

    /**
     * 取得最新的配置
     *
     * @return /
     */
    @Override
    public WorkingFaceProgressSetting getLatestSetting(String mineCode)
    {
        return workingFaceProgressSettingMapper.selectLatestSetting(mineCode);
    }

    // endregion 取得最新的配置

    // region 根据煤矿编码和配置时间取得煤矿配置

    /**
     * 根据煤矿编码和配置时间取得煤矿配置
     *
     * @param mineCode   煤矿编码
     * @param updateTime 配置时间
     */
    @Override
    public WorkingFaceProgressSetting selectWithMineCodeAndUpdateTime(String mineCode, Timestamp updateTime)
    {
        return workingFaceProgressSettingMapper.selectWithMineCodeAndUpdateTime(mineCode, updateTime);
    }

    // endregion 根据煤矿编码和配置时间取得煤矿配置

    // region 解析煤矿工作面配置

    /**
     * 解析煤矿的定位解析配置
     * 解析数据来源
     * 1）接口下发数据直接解析，/workingFace/workProgress/postSettings
     * 2）【废弃】收到业务侧通知，主动拉取配置进行解析， WorkingFaceProgressSettingUpdateHandler
     * 3）【废弃】定时任务同步下发配置，WorkingProgressSettingsSyncTask，/workingFace/workProgress/syncSettings
     *
     * @param content                定位解析配置
     * @param groupCode              集团编码
     * @param companyCode            二级公司编码
     * @param mineCode               煤矿编码
     * @param lastUpdateTime         上次同步时间
     * @param fullDataOfMine         加载的是否煤矿全量数据
     * @param containsWorkMode       配置是否包含工作模式
     * @param forceUpdateModelSystem 强制更新接入系统信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ServiceCallLog> parseProgressSettings(
            String actionStr,
            JSONObject content,
            String groupCode,
            String companyCode,
            String mineCode,
            Long lastUpdateTime,
            Boolean fullDataOfMine,
            Boolean containsWorkMode,
            String forceUpdateModelSystem
    )
    {
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        // region 解析消息

        // 缓存消息
        String msgType = content.getString("type_code");
        if (StringUtil.isEmpty(msgType) || !"pos_cfg".equalsIgnoreCase(msgType)) {
            serviceCallLogs.add(
                    ServiceCallLog.createNew(
                            String.format("%s 返回格式错误（no type_code or not pos_cfg type）", actionStr),
                            (byte) 1,
                            StringUtil.isEmpty(mineCode) ? "0" : mineCode
                    )
            );
            log.error(
                    "type_code 'pos_cfg' is required, groupCode: {}, companyCode: {}, mineCode: {},  lastUpdateTime: {}, return content: {}",
                    groupCode,
                    companyCode,
                    mineCode,
                    0L,
                    content.toJSONString()
            );
            return serviceCallLogs;
        }

        // 消息内容列表
        JSONArray contentList = content.getJSONArray("content");
        if (contentList == null) {
            serviceCallLogs.add(
                    ServiceCallLog.createNew(
                            String.format("%s 获取配置为空（content null）, 同步时间: %s", actionStr, lastUpdateTime),
                            (byte) 1,
                            StringUtil.isEmpty(mineCode) ? "0" : mineCode
                    )
            );
            log.error(
                    "content is empty, input params: groupCode->{}, companyCode->{}, mineCode->{}, lastUpdateTime->{}",
                    groupCode,
                    companyCode,
                    mineCode,
                    0L
            );
            return serviceCallLogs;
        }

        // endregion 解析消息

        // region 缓存工作面配置 & 解析工作面设备

        // 缓存工作面配置
        List<WorkingFaceSettingCacheResult> cacheResults = cacheWorkingFaceSettings(groupCode, contentList, actionStr);
        if (cacheResults == null || cacheResults.isEmpty()) {
            serviceCallLogs.add(
                    ServiceCallLog.createNew(
                            String.format(
                                    "%s 缓存配置为空, 取得记录数: %d, 同步时间: %s",
                                    actionStr,
                                    contentList.size(),
                                    lastUpdateTime
                            ),
                            (byte) 1,
                            StringUtil.isEmpty(mineCode) ? "0" : mineCode
                    )
            );
            if (!contentList.isEmpty()) {
                log.warn(
                        "synced content is not empty, but empty result got when parse it. content: {}",
                        JSON.toJSONString(contentList)
                );
            }
            return serviceCallLogs;
        }

        // endregion 缓存工作面配置 & 解析工作面设备

        // region 更新子系统信息

        // 默认强制更新子系统信息
        boolean forceUpdate = StringUtil.isEmpty(forceUpdateModelSystem) || "1".equals(forceUpdateModelSystem);

        // 初始化接入子系统 model_system, model_system_alive, working_face_progress_definition
        ModelSystemInitResult initResult = initializeModelSystems(actionStr, contentList, fullDataOfMine, containsWorkMode, forceUpdate);
        if (initResult.getErrors() != null && !initResult.getErrors().isEmpty()) {
            serviceCallLogs.add(
                    ServiceCallLog.createNew(
                            String.format(
                                    "%s 初始化接入配置失败: %s",
                                    actionStr,
                                    String.join(", ", initResult.getErrors())
                            ),
                            (byte) 1,
                            StringUtil.isEmpty(mineCode) ? "0" : mineCode
                    )
            );
        }

        // endregion 更新子系统信息

        // region 记录日志

        // 初始化日志
        if (initResult.getServiceLogs() != null && !initResult.getServiceLogs().isEmpty()) {
            serviceCallLogs.addAll(initResult.getServiceLogs());
        }

        // 缓存和初始化成功
        if (!cacheResults.isEmpty() && CollectionUtils.isEmpty(initResult.getErrors())) {
            for (WorkingFaceSettingCacheResult cacheRes : cacheResults) {
                serviceCallLogs.add(
                        ServiceCallLog.createNew(
                                String.format(
                                        "%s 成功缓存配置并完成接入系统配置(mine_code: %s, update_time: %s)",
                                        actionStr,
                                        cacheRes.getMineCode(),
                                        cacheRes.getUpdateTime()
                                ),
                                (byte) 0,
                                cacheRes.getMineCode()
                        )
                );
            }
        }

        // endregion 记录日志

        return serviceCallLogs;
    }

    // endregion 解析煤矿工作面配置

    // region 同步煤矿配置

    /**
     * 从业务侧获取工作面解析配置信息
     *
     * @param mineCode         煤矿编码
     * @param fullDataOfMine   加载的是否煤矿全量数据
     * @param containsWorkMode 配置是否包含工作模式
     * @param lastUpdateTime   最后上传时间，如果配置则取得大于这个时间的配置
     * @param actionStr        操作区分
     */
    @Override
    public List<ServiceCallLog> syncProgressSettings(
            String mineCode,
            Boolean fullDataOfMine,
            Boolean containsWorkMode,
            Long lastUpdateTime,
            String actionStr
    )
    {
        if (StringUtil.isEmpty(actionStr)) {
            actionStr = "定时更新配置调用业务侧配置接口";
        }

        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        // 从业务侧获取工作面解析配置信息
        JSONObject result = mineBizFeignService.queryWorkingFaceProgressSettings(
                null,
                null,
                mineCode,
                lastUpdateTime,
                MesBusinessEnum.HEADER.desc
        );
        log.debug(
                "result: {}, input params: groupCode->{}, companyCode->{}, mineCode->{}, lastUpdateTime->{}",
                (result == null ? "" : JSON.toJSONString(result)),
                null,
                null,
                mineCode,
                0L
        );

        // 解析处理配置
        if (result == null) {
            // 在指定了煤矿编码的情况下，记录返回空值错误
            if (StringUtil.isNotEmpty(mineCode)) {
                serviceCallLogs.add(
                        ServiceCallLog.createNew(
                                actionStr + " 返回内容为空",
                                (byte) 1,
                                mineCode
                        )
                );
            }
            log.error(
                    "result is null, input params: groupCode->{}, companyCode->{}, mineCode->{}, lastUpdateTime->{}",
                    null,
                    null,
                    mineCode,
                    0L
            );

            return serviceCallLogs;
        }

        // 解析配置
        List<ServiceCallLog> processLogs = parseProgressSettings(
                actionStr,
                result,
                null,
                null,
                mineCode,
                lastUpdateTime,
                fullDataOfMine,
                containsWorkMode,
                "1"
        );
        serviceCallLogs.addAll(processLogs);

        return serviceCallLogs;
    }

    // endregion 同步煤矿配置

    // region 私有方法

    // region 缓存工作面配置

    /**
     * 缓存工作面配置
     *
     * @param groupCode   集团编码
     * @param contentList 煤矿工作面配置列表
     * @param actionStr   操作方式
     */
    private List<WorkingFaceSettingCacheResult> cacheWorkingFaceSettings(String groupCode, JSONArray contentList, String actionStr)
    {
        List<WorkingFaceSettingCacheResult> result = new ArrayList<>();

        // 工作面配置新增更新列表
        List<WorkingFaceProgressSetting> addOrUpdateList = new ArrayList<>();
        // 设备信息新增更新列表
        List<WorkingFaceLocatorDevice> deviceList = new ArrayList<>();

        // 当前时间
        Timestamp nowTime = DateUtil.getTimestamp();

        // 遍历内容列表
        for (JSONObject jsonObj : contentList.toJavaList(JSONObject.class)) {
            // 煤矿编码
            String mineCode = jsonObj.getString("mine_code");
            // 配置更新时间
            Long updateTimeTs = jsonObj.getLong("update_time");
            if (updateTimeTs == null) {
                // 未提供配置更新时间
                updateTimeTs = nowTime.getTime();
            }
            Timestamp updateTime = new Timestamp(updateTimeTs);

            // 配置是否存在, 存在的情况则做更新
            boolean isUpdate = false;
            WorkingFaceProgressSetting settingEntity = selectWithMineCodeAndUpdateTime(mineCode, updateTime);
            if (settingEntity == null) {
                settingEntity = new WorkingFaceProgressSetting();
            }
            else {
                isUpdate = true;
            }

            if (StringUtil.isNotEmpty(jsonObj.getString("group_code"))) {
                groupCode = jsonObj.getString("group_code");
            }
            if (StringUtil.isEmpty(groupCode)) {
                groupCode = "10000";
            }

            settingEntity.setGroupCode(groupCode);
            settingEntity.setCompanyCode(jsonObj.getString("company_code"));
            settingEntity.setCompanyName(jsonObj.getString("company_name"));
            settingEntity.setMineCode(mineCode);
            settingEntity.setMineName(jsonObj.getString("mine_name"));

            // 配置明细
            settingEntity.setSettings(JSON.toJSONString(jsonObj, SerializerFeature.WriteMapNullValue));

            // 是否配置更新
            if (isUpdate) {
                settingEntity.setUpdatedAt(nowTime);
            }
            else {
                settingEntity.setUpdateTime(updateTime);
                settingEntity.setCreatedAt(nowTime);
                settingEntity.setUpdatedAt(nowTime);

                // 新增ID
                settingEntity.setId(SnowflakeUtil.nextId());
            }

            settingEntity.setMemo(actionStr);

            addOrUpdateList.add(settingEntity);
            result.add(
                    WorkingFaceSettingCacheResult
                            .builder()
                            .settingId(settingEntity.getId())
                            .mineCode(mineCode)
                            .updateTime(settingEntity.getUpdateTime().getTime())
                            .build()
            );

            // 设备信息存储
            deviceList.addAll(parseLocatorDeviceList(jsonObj.getJSONArray("lane"), settingEntity));
            deviceList.addAll(parseLocatorDeviceList(jsonObj.getJSONArray("wf"), settingEntity));
        }
        try {
            // 更新配置缓存
            if (!addOrUpdateList.isEmpty()) {
                saveOrUpdateBatch(addOrUpdateList);
                log.info("working face progress settings is cached, size: {}", addOrUpdateList.size());
            }

            // 更新工作面设置
            if (!deviceList.isEmpty()) {
                // 根据设备编码去重
                List<WorkingFaceLocatorDevice> listFilter = deviceList
                        .stream()
                        .filter(distinctByField((wf) -> String.format("%s@%s", wf.getWorkingFaceId(), wf.getDeviceCode())))
                        .collect(Collectors.toList());

                workingFaceLocatorDeviceService.saveOrUpdateBatchByMultiId(listFilter);
                log.info("working face's device saved, size: {}", listFilter.size());
            }

            return result;
        }
        catch (Exception ex) {
            log.error(
                    "working_face_progress_setting save or update error: {}, contentList: {}",
                    ex.getMessage(),
                    contentList.toJSONString(),
                    ex
            );
            return new ArrayList<>();
        }
    }

    // endregion 缓存工作面配置

    // region 初始化接入子系统

    /**
     * 初始化接入子系统
     *
     * @param actionStr        操作区分
     * @param contentList      煤矿工作面配置列表
     * @param containsAll      配置是否全量数据
     * @param containsWorkMode 配置是否包含工作模式
     * @param forceUpdate      强制更新子系统配置
     */
    private ModelSystemInitResult initializeModelSystems(
            String actionStr,
            JSONArray contentList,
            boolean containsAll,
            boolean containsWorkMode,
            boolean forceUpdate
    )
    {
        ModelSystemInitResult initResult = new ModelSystemInitResult();

        List<ModelSystem> systemSaveList = new ArrayList<>();
        List<ModelSystemAlive> aliveSaveList = new ArrayList<>();
        List<WorkingFaceProgressDefinition> workingFaceDefList = new ArrayList<>();
        List<WorkingFaceEvent> workingFaceEventList = new ArrayList<>();
        List<WorkingFaceDefinition> workingFaceList = new ArrayList<>();

        // 错误信息
        List<String> errMsgList = new ArrayList<>();
        // 接收到的有效工作面ID列表
        List<WorkingFaceSyncInfo> enabledWfList = new ArrayList<>();
        // 接收到的无效工作面ID列表
        List<WorkingFaceSyncInfo> disabledWfList = new ArrayList<>();
        // 所有上传的煤矿编码列表
        Set<String> wfPostMineCodes = new HashSet<>();
        // 存在有效配置煤矿编码列表
        Set<String> wfOnlineMineCodes = new HashSet<>();

        // 当前时间
        Timestamp nowTime = DateUtil.getTimestamp();

        // 遍历内容列表
        for (JSONObject mineCfg : contentList.toJavaList(JSONObject.class)) {
            String mineCode = mineCfg.getString("mine_code");
            if (StringUtil.isEmpty(mineCode)) {
                log.warn("mine_code is empty, do not initialize model_system");
                continue;
            }
            String mineName = mineCfg.getString("mine_name");
            if (StringUtil.isEmpty(mineName)) {
                log.warn("[MINE_CODE: {}] mine_name is empty.", mineCode);
            }

            // 本模块的 group_code 使用的是业务上的二级公司编码，统一命名为 company_code
            String groupCode = mineCfg.getString("company_code");
            if (StringUtil.isEmpty(groupCode)) {
                log.warn("group_code[ob: company_code] is empty, do not initialize model_system");
                continue;
            }

            JSONArray wfCfgList = mineCfg.getJSONArray("wf");
            boolean isWfCfgEmpty = false;
            if (wfCfgList != null && !wfCfgList.isEmpty()) {
                parseWorkingFaceProgressDefinitionList(
                        initResult,
                        actionStr,
                        forceUpdate,
                        nowTime,
                        groupCode,
                        mineCfg.getString("company_name"),
                        mineCode,
                        mineCfg.getString("mine_name"),
                        enabledWfList,
                        disabledWfList,
                        systemSaveList,
                        aliveSaveList,
                        workingFaceDefList,
                        workingFaceEventList,
                        workingFaceList,
                        wfCfgList,
                        "wf_name",
                        "mining_status",
                        miningFaceWorkingStateTextList,
                        miningFaceWorkingStateTextListV2,
                        WorkingFaceDefinition.FACE_TYPE_MINING
                );
            }
            else {
                isWfCfgEmpty = true;
                log.warn("[MINE_CODE: {}] no mwf working face setting is post.", mineCode);
            }

            JSONArray laneCfgList = mineCfg.getJSONArray("lane");
            boolean isLaneCfgEmpty = false;
            if (laneCfgList != null && !laneCfgList.isEmpty()) {
                parseWorkingFaceProgressDefinitionList(
                        initResult,
                        actionStr,
                        forceUpdate,
                        nowTime,
                        groupCode,
                        mineCfg.getString("company_name"),
                        mineCode,
                        mineCfg.getString("mine_name"),
                        enabledWfList,
                        disabledWfList,
                        systemSaveList,
                        aliveSaveList,
                        workingFaceDefList,
                        workingFaceEventList,
                        workingFaceList,
                        laneCfgList,
                        "lane_name",
                        "driving_status",
                        drivingFaceWorkingStateTextList,
                        null,
                        WorkingFaceDefinition.FACE_TYPE_EXCAVATION
                );
            }
            else {
                isLaneCfgEmpty = true;
                log.warn("[MINE_CODE: {}] no ewf working face setting is post.", mineCode);
            }

            wfPostMineCodes.add(mineCode);
            if (!isWfCfgEmpty || !isLaneCfgEmpty) {
                wfOnlineMineCodes.add(mineCode);
            }
            else {
                errMsgList.add(String.format(
                        "%s(%s)有效配置为空",
                        mineCode,
                        (StringUtil.isEmpty(mineName) ? "未提供名称" : mineName)
                ));
            }
        }

        if (!errMsgList.isEmpty()) {
            initResult.getErrors().addAll(errMsgList);
            return initResult;
        }

        // region 非全量场合加载煤矿工作面全量数据

        // Feat. 禁用不存在于全量工作面列表里的工作面

        // 单独同步到的煤矿编码
        String syncMineCode = null;
        // 单独同步到的工作面编码列表
        Set<String> syncedWfIds = new HashSet<>();

        // 非全量下发、上载煤矿信息也存在的场合，加载煤矿全量工作面数据
        if (!containsAll && !wfPostMineCodes.isEmpty()) {
            try {
                syncMineCode = wfPostMineCodes.iterator().next();

                JSONObject resObj = gisBaseFeignService.queryWorkFaceInfo(syncMineCode);
                JSONObject dataObj = resObj.getJSONObject("data");

                // 掘进面
                JSONArray ewfList = dataObj.getJSONArray("ewf");
                if (ewfList != null) {
                    for (JSONObject ewf : ewfList.toJavaList(JSONObject.class)) {
                        syncedWfIds.add(ewf.getString("workFaceId"));
                    }
                }
                // 采煤面
                JSONArray mwfList = dataObj.getJSONArray("mwf");
                if (mwfList != null) {
                    for (JSONObject mwf : mwfList.toJavaList(JSONObject.class)) {
                        syncedWfIds.add(mwf.getString("workFaceId"));
                    }
                }
            }
            catch (Exception e) {
                log.error("加载工作面全量数据失败, 煤矿编码: {}, 异常: {}", syncMineCode, e.getMessage(), e);
            }
        }

        // endregion 非全量场合加载煤矿工作面全量数据

        try {
            // region 接入系统、接入配置

            // 接入系统
            if (!systemSaveList.isEmpty()) {
                // 更新时只开启接入标志，不关闭
                // 目的是一旦开启接入就不能关闭，即启用（生产）的工作面，无论是否有工作模式都进行接入并监控
                modelSystemService.upsertBatchOnlyEnableRoute(systemSaveList);
                log.info("model_system is initialized or updated, size: {}", systemSaveList.size());
            }
            // 接入配置
            if (!aliveSaveList.isEmpty()) {
                modelSystemAliveService.upsertBatch(aliveSaveList);
                log.info("model_system_alive is initialized or updated, size: {}", aliveSaveList.size());
            }

            // endregion 接入系统、接入配置

            // region 工作面定义、工作面基本信息、工作面变更事件

            // 工作面定义
            if (!workingFaceDefList.isEmpty()) {
                // 数据源中是否包含工作模式，业务侧接口和下发没有 workMode，GIS侧下发接口中含有 workMode
                if (containsWorkMode) {
                    workingFaceProgressDefinitionMapper.upsertWfpdBatchWithWorkMode(workingFaceDefList);
                }
                else {
                    workingFaceProgressDefinitionMapper.upsertWfpdBatch(workingFaceDefList);
                }

                log.info("working face definition created or updated, size: {}", workingFaceDefList.size());
            }
            // 工作面变更事件
            if (!workingFaceEventList.isEmpty()) {
                workingFaceEventService.createRecord(workingFaceEventList);
                log.info("working face event created or updated, size: {}", workingFaceEventList.size());
            }
            // 工作面信息
            if (!workingFaceList.isEmpty()) {
                workingFaceDefinitionService.upsertBatchWfDef(workingFaceList);
                log.info("working face definition created or updated, size: {}", workingFaceList.size());
            }

            // endregion 工作面基本信息、工作面变更事件

            // region 全量同步时, 禁用未同步到任何工作面的煤矿系统

            if (!wfPostMineCodes.isEmpty() && !wfOnlineMineCodes.isEmpty()) {
                wfPostMineCodes.removeAll(wfOnlineMineCodes);
            }
            if (containsAll && !wfPostMineCodes.isEmpty()) {
                modelSystemService.disableByEnabledMineCodes(wfPostMineCodes);
                initResult.getServiceLogs()
                          .add(ServiceCallLog.createNew(
                                  String.format(
                                          "%s 以下煤矿同步到有效工作面: %s, 以下煤矿工作面全被禁用: %s",
                                          actionStr,
                                          (wfOnlineMineCodes.isEmpty() ? "无" : String.join("|", wfOnlineMineCodes)),
                                          String.join("|", wfPostMineCodes)
                                  ),
                                  (byte) 0,
                                  "0"
                          ));
            }

            // endregion 全量同步时, 禁用未同步到任何工作面的煤矿系统

            // region 按煤矿输出日志、禁用未同步到的工作面、更新禁用工作面的在线状态

            Set<String> mineCodes = enabledWfList
                    .stream()
                    .map(WorkingFaceSyncInfo::getMineCode)
                    .collect(Collectors.toSet());
            for (String mineCode : mineCodes) {
                Set<String> wfIds = enabledWfList
                        .stream()
                        .filter(wf -> wf.getMineCode().equals(mineCode))
                        .map(WorkingFaceSyncInfo::getWfId)
                        .collect(Collectors.toSet());
                // 按煤矿删除未上传的工作面
                if (containsAll) {
                    // 更新禁用工作面的在线状态
                    workingFaceDefinitionService.updateDataSystemMonitorStatusWithEnabledWfd(mineCode, wfIds);
                    // 更新禁用工作面的断线记录
                    workingFaceDefinitionService.updateModelSystemAliveStatisticsWithEnabledWfd(mineCode, wfIds);
                    // 禁用未同步的工作面
                    modelSystemService.disableByEnabledSourceSystemCodes(mineCode, !wfIds.isEmpty() ? wfIds : null);

                    log.info("[All mode]working face of {} has been disabled except: {}", mineCode, String.join(",", syncedWfIds));
                    initResult.getServiceLogs()
                              .add(ServiceCallLog.createNew(
                                      String.format("%s 同步到以下工作面: %s， 其他工作面被禁用", actionStr, String.join(",", wfIds)),
                                      (byte) 0,
                                      mineCode
                              ));
                }
                else {
                    initResult.getServiceLogs()
                              .add(ServiceCallLog.createNew(
                                      String.format("%s 同步到以下工作面: %s", actionStr, String.join(",", wfIds)),
                                      (byte) 0,
                                      mineCode
                              ));
                }
            }

            // 非全量同步、同步到煤矿存在有效工作面
            if (!containsAll) {
                // 更新禁用工作面的在线状态
                if (!disabledWfList.isEmpty()) {
                    Set<String> disabledWfIds = disabledWfList.stream()
                                                              .map(WorkingFaceSyncInfo::getWfId)
                                                              .collect(Collectors.toSet());
                    // 更新工作面的在线状态
                    workingFaceDefinitionService.updateDataSystemMonitorStatus(syncMineCode, disabledWfIds);
                    // 更新工作面的断线记录
                    workingFaceDefinitionService.updateModelSystemAliveStatistics(syncMineCode, disabledWfIds);
                    // 禁用工作面
                    modelSystemService.disableBySourceSystemCodes(syncMineCode, disabledWfIds);
                }

                // 禁用未同步到的工作面，疑似已删除
                if (syncMineCode != null && !syncedWfIds.isEmpty()) {
                    // 更新禁用工作面的在线状态
                    workingFaceDefinitionService.updateDataSystemMonitorStatusWithEnabledWfd(syncMineCode, syncedWfIds);
                    // 更新禁用工作面的断线记录
                    workingFaceDefinitionService.updateModelSystemAliveStatisticsWithEnabledWfd(syncMineCode, syncedWfIds);
                    // 禁用未同步的工作面
                    modelSystemService.disableByEnabledSourceSystemCodes(syncMineCode, syncedWfIds);
                }

                log.info("[New mode]working face of {} has been disabled except: {}", syncMineCode, String.join(",", syncedWfIds));
            }
            else {
                log.info(
                        "[New mode]no need to do update by enabled working faces, containsAll: {}, syncMineCode: {}, syncedWfIds: {}",
                        containsAll,
                        syncMineCode,
                        String.join(",", syncedWfIds)
                );
            }

            // endregion 按煤矿输出日志、禁用未同步到的工作面、更新禁用工作面的在线状态
        }
        catch (Exception ex) {
            errMsgList.add("发生异常 " + ex.getMessage());
            initResult.getErrors().addAll(errMsgList);

            log.error(
                    "model_system info save or update error: {}, contentList: {}",
                    ex.getMessage(),
                    contentList.toJSONString(),
                    ex
            );
        }

        return initResult;
    }

    // endregion 初始化接入子系统

    // region 解析工作面基本信息

    /**
     * @param initResult           初始化结果引用
     * @param actionStr            操作区分
     * @param forceUpdate          是否强制更新
     * @param nowTime              当前时间
     * @param groupCode            二级公司编码
     * @param groupName            二级公司名称
     * @param mineCode             煤矿编码
     * @param mineName             煤矿名称
     * @param enabledWfs           启用的工作面列表
     * @param disabledWfs          禁用的工作面列表
     * @param systemSaveList       接入系统保存列表
     * @param aliveSaveList        接入配置保存列表
     * @param workingFaceDefList   工作面定义列表
     * @param workingFaceEventList 工作面事件列表
     * @param workingFaceList      工作面基本信息列表
     * @param wfCfgList            工作面列表
     * @param wfNameKey            工作面名称键值
     * @param statusKey            工作面状态键值
     * @param statusTextList       工作面状态文本列表V1
     * @param statusTextListV2     工作面状态文本列表V2
     * @param faceType             工作面类型
     */
    private void parseWorkingFaceProgressDefinitionList(
            ModelSystemInitResult initResult,
            String actionStr,
            boolean forceUpdate,
            Timestamp nowTime,
            String groupCode,
            String groupName,
            String mineCode,
            String mineName,
            List<WorkingFaceSyncInfo> enabledWfs,
            List<WorkingFaceSyncInfo> disabledWfs,
            List<ModelSystem> systemSaveList,
            List<ModelSystemAlive> aliveSaveList,
            List<WorkingFaceProgressDefinition> workingFaceDefList,
            List<WorkingFaceEvent> workingFaceEventList,
            List<WorkingFaceDefinition> workingFaceList,
            JSONArray wfCfgList,
            String wfNameKey,
            String statusKey,
            List<String> statusTextList,
            List<String> statusTextListV2,
            String faceType
    )
    {
        if (wfCfgList == null || wfCfgList.isEmpty()) {
            return;
        }

        for (JSONObject wfCfg : wfCfgList.toJavaList(JSONObject.class)) {
            // 工作面ID
            String wfId = wfCfg.getString("wf_id");
            wfId = (wfId == null ? null : wfId.trim());

            // 工作面名称(wf_name 采煤面、lane_name 掘进面)
            String wfName = wfCfg.getString(wfNameKey);
            wfName = (wfName == null ? null : wfName.trim());

            if (StringUtil.isEmpty(wfId) || StringUtil.isEmpty(wfName)) {
                initResult.getServiceLogs()
                          .add(ServiceCallLog.createNew(
                                  String.format("%s 下发工作面ID或者名称为空", actionStr),
                                  (byte) 1,
                                  mineCode
                          ));
                continue;
            }

            // 根据工作面 ID 检查是否同步过该工作面
            QueryWrapper<ModelSystem> codeExistedQueryWrap = new QueryWrapper<>();
            codeExistedQueryWrap.eq("source_system_code", wfId);
            int codeExistedCount = modelSystemService.count(codeExistedQueryWrap);
            // 已同步过，并且不强制更新，跳出
            if (codeExistedCount > 0 && !forceUpdate) {
                log.warn(
                        "[WF_ID: {}] model_system of this working face is ready, ignore it.",
                        wfId
                );
                continue;
            }

            // 根据工作面名称检查是否同步过该工作面, 防止重名出现
            QueryWrapper<ModelSystem> nameQueryWrap = new QueryWrapper<>();
            nameQueryWrap.eq("source_system_name", wfName);
            int nameExistedCount = modelSystemService.count(nameQueryWrap);
            if (nameExistedCount > 0) {
                log.debug(
                        "[WF_NAME: {}] model_system of this working face has already been initialized.",
                        wfName
                );
            }

            // 配置更新时间
            Long updateTime = wfCfg.getLong("update_time");
            if (updateTime == null) {
                updateTime = System.currentTimeMillis();
            }
            Timestamp updateStamp = new Timestamp(updateTime);

            // 是否删除
            boolean isDeleted = false;
            if (wfCfg.containsKey("is_delete")) {
                String isDelStr = wfCfg.getString("is_delete");
                if (StringUtil.isNotEmpty(isDelStr) && "1".equalsIgnoreCase(isDelStr)) {
                    isDeleted = true;
                }
            }

            // 工作面状态
            boolean isWorking = false;
            String statusStr = null;
            if (wfCfg.containsKey(statusKey)) {
                statusStr = wfCfg.getString(statusKey);
                if (StringUtil.isNotEmpty(statusStr)
                    && (
                            statusTextList != null && statusTextList.contains(statusStr)
                            || statusTextListV2 != null && statusTextListV2.contains(statusStr)
                    )
                ) {
                    isWorking = true;
                }
            }

            // 是否有工作模式，没下发的情况默认有工作模式
            boolean hasWorkMode = !wfCfg.containsKey("work_mode") || StringUtil.isNotEmpty(wfCfg.getString("work_mode"));
            // 接入是否启用
            // 有工作模式的情况下才开启数据接入，一旦开启则不关闭
            short routeEnabled = (short) (hasWorkMode ? 1 : 0);

            // 【Feat. 工作面状态判定】接入系统是否启用
            short systemEnabled = 1;
            // 判断系统是否启用时是否包含工作模式
            if (Boolean.TRUE.equals(stateIncludeWorkMode)) {
                systemEnabled = (short) ((!isDeleted && isWorking && hasWorkMode) ? 1 : 0);
            }
            else {
                systemEnabled = (short) ((!isDeleted && isWorking) ? 1 : 0);
            }
            // 系统已经启用的情况，记录工作面 ID，供全量接入时禁用其他工作面
            WorkingFaceSyncInfo wfSyncInfo = WorkingFaceSyncInfo
                    .builder()
                    .wfId(wfId)
                    .mineCode(mineCode)
                    .build();
            if (systemEnabled == 1) {
                enabledWfs.add(wfSyncInfo);
            }
            else {
                disabledWfs.add(wfSyncInfo);
            }

            // 工作面信息
            WorkingFaceProgressDefinition wfpd = new WorkingFaceProgressDefinition();
            wfpd.setGroupCode(groupCode);
            wfpd.setGroupName(groupName);
            wfpd.setMineCode(mineCode);
            wfpd.setMineName(mineName);
            wfpd.setSystemCode("990" + wfId);
            // wfpd.setSystemName(wfName + "进尺监控");
            wfpd.setSystemName(wfName);
            wfpd.setWorkingFaceId(wfId);
            wfpd.setWorkingFaceName(wfName);
            wfpd.setWorkingFaceType(faceType);
            wfpd.setMemo(actionStr);
            wfpd.setDataTime(updateStamp);
            wfpd.setCollectTime(nowTime);
            wfpd.setCollectStatus(100);
            wfpd.setCreatedAt(nowTime);
            wfpd.setUpdatedAt(nowTime);

            // 工作面状态
            wfpd.setWorkState(statusStr);
            // 工作模式
            // 通过 SQL 语句判断工作模式是否变更、变更时间
            // Integer workMode = wfCfg.getInteger("work_mode");
            // if (workMode != null) {
            //     wfpd.setWorkMode(String.valueOf(workMode));
            //     wfpd.setWorkModeChanged("1");
            //     wfpd.setWorkModeChangedAt(updateStamp);
            // }
            // else {
            //     wfpd.setWorkMode(null);
            //     wfpd.setWorkModeChanged(null);
            //     wfpd.setWorkModeChangedAt(null);
            // }
            wfpd.setWorkMode(wfCfg.getString("work_mode"));

            // 工作面进尺监控 子系统
            if (workingFaceDefList.stream().anyMatch(wf -> wf.getWorkingFaceId().equals(wfpd.getWorkingFaceId()))) {
                log.warn(
                        "[MINE_CODE: {}] [WF_NAME: {}] working_face_progress_definition of this working face has already been generated.",
                        mineCode,
                        wfName
                );
            }
            else {
                workingFaceDefList.add(wfpd);
            }

            // 工作面进尺监控 子系统
            ModelSystem progressMs = generateModelSystem(
                    wfpd,
                    systemEnabled,
                    routeEnabled,
                    faceType,
                    false,
                    // 连巷工作面ID, 取两个工作面中的一个
                    wfCfg.getString("union_id")
            );
            if (systemSaveList.stream().anyMatch(ms -> ms.getId().equals(progressMs.getId()))) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] model_system of this working face has already been generated.", mineCode, wfName);
            }
            else {
                systemSaveList.add(progressMs);
            }

            // 工作面进尺监控 在线监测配置
            ModelSystemAlive progressMsa = generateModelSystemAlive(wfpd, false);
            if (aliveSaveList.stream().anyMatch(msa -> msa.getSystemId().equals(progressMsa.getSystemId()))) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] model_system_alive of this working face has already been generated.", mineCode, wfName);
            }
            else {
                aliveSaveList.add(progressMsa);
            }

            // 定位设备监控 子系统
            ModelSystem devMs = generateModelSystem(
                    wfpd,
                    systemEnabled,
                    routeEnabled,
                    faceType,
                    true,
                    null
            );
            if (systemSaveList.stream().anyMatch(ms -> ms.getId().equals(devMs.getId()))) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] model_system of this working face has already been generated.", mineCode, wfName);
            }
            else {
                systemSaveList.add(devMs);
            }
            // 定位设备监控 在线监测配置
            ModelSystemAlive devMsa = generateModelSystemAlive(wfpd, true);
            if (aliveSaveList.stream().anyMatch(msa -> msa.getSystemId().equals(devMsa.getSystemId()))) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] model_system_alive of this working face has already been generated.", mineCode, wfName);
            }
            else {
                aliveSaveList.add(devMsa);
            }

            // 工作面状态变化
            WorkingFaceEvent event = WorkingFaceEvent.getLifeCycleWorkingFaceEvent(
                    wfpd,
                    nowTime,
                    actionStr
            );
            if (workingFaceEventList.stream().anyMatch(
                    ev -> ev.getWorkFaceId().equals(event.getWorkFaceId()) && ev.getWorkFaceType().equals(event.getWorkFaceType()))
            ) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] working_face_event of this working face has already been generated.", mineCode, wfName);
            }
            else {
                workingFaceEventList.add(event);
            }

            // 工作面基本信息
            WorkingFaceDefinition wfd = new WorkingFaceDefinition(wfpd);
            if (workingFaceList.stream().anyMatch(
                    ev -> ev.getWorkFaceId().equals(wfd.getWorkFaceId()) && ev.getFaceType().equals(wfd.getFaceType()))
            ) {
                log.warn("[MINE_CODE: {}] [WF_NAME: {}] working_face_definition of this working face has already been generated.", mineCode, wfName);
            }
            else {
                workingFaceList.add(wfd);
            }
        }

        // 连采双巷工作面解析
        // 连采双巷工作面，选取其中一个工作面的ID(sourceSystemCode)为作为关联用ID(businessSystemCode)
        Set<String> unionIds = systemSaveList
                .stream()
                .filter(ms -> mineCode.equals(ms.getMineCode()) && StringUtil.isNotEmpty(ms.getBusinessSystemCode()))
                .map(ModelSystem::getBusinessSystemCode)
                .collect(Collectors.toSet());
        for (ModelSystem ms : systemSaveList) {
            if (mineCode.equals(ms.getMineCode()) && unionIds.contains(ms.getSourceSystemCode())) {
                ms.setBusinessSystemCode(ms.getSourceSystemCode());
            }
        }
    }

    // endregion 解析工作面基本信息

    // region 解析工作面定位测距设备

    /**
     * 解析工作面定位测距设备
     *
     * @param wfList        工作面列表
     * @param settingEntity 工作面配置
     * @return /
     */
    private List<WorkingFaceLocatorDevice> parseLocatorDeviceList(JSONArray wfList, WorkingFaceProgressSetting settingEntity) {
        if (wfList == null || wfList.isEmpty()) {
            return Collections.emptyList();
        }

        List<WorkingFaceLocatorDevice> deviceList = new ArrayList<>();
        for (JSONObject wfObj : wfList.toJavaList(JSONObject.class)) {
            JSONObject stationObj = wfObj.getJSONObject("station");
            if (null == stationObj) {
                continue;
            }

            // 工作面ID
            String workFaceId = wfObj.getString("wf_id");
            // 工作模式
            String workMode = stationObj.getString("workingMode");

            // 当定位卡存在时需要存储设备信息
            if (StringUtils.isNotEmpty(stationObj.getString("positionLabel"))) {
                WorkingFaceLocatorDevice locatorDevice = new WorkingFaceLocatorDevice();
                locatorDevice.setGroupCode(settingEntity.getCompanyCode());
                locatorDevice.setMineCode(settingEntity.getMineCode());
                locatorDevice.setWorkingFaceId(workFaceId);
                locatorDevice.setDeviceCode(stationObj.getString("positionLabel"));
                locatorDevice.setDeviceType(1);
                deviceList.add(locatorDevice);
            }
            // 当靶标卡存在时需要存储设备信息
            if (StringUtils.isNotEmpty(stationObj.getString("target"))) {
                WorkingFaceLocatorDevice locatorDevice = new WorkingFaceLocatorDevice();
                locatorDevice.setGroupCode(settingEntity.getCompanyCode());
                locatorDevice.setMineCode(settingEntity.getMineCode());
                locatorDevice.setWorkingFaceId(workFaceId);
                locatorDevice.setDeviceCode(stationObj.getString("target"));
                locatorDevice.setDeviceType(2);
                if ("2".equals(workMode) || "3".equals(workMode) || "4".equals(workMode)) {
                    locatorDevice.setX(stationObj.getString("targetCoordinateX"));
                    locatorDevice.setY(stationObj.getString("targetCoordinateY"));
                }
                deviceList.add(locatorDevice);
            }
            // 当基站一存在时需要存储设备信息
            if (StringUtils.isNotEmpty(stationObj.getString("baseSation"))) {
                WorkingFaceLocatorDevice locatorDevice = new WorkingFaceLocatorDevice();
                locatorDevice.setGroupCode(settingEntity.getCompanyCode());
                locatorDevice.setMineCode(settingEntity.getMineCode());
                locatorDevice.setWorkingFaceId(workFaceId);
                locatorDevice.setDeviceCode(stationObj.getString("baseSation"));
                locatorDevice.setDeviceType(3);
                if ("1".equals(workMode)) {
                    locatorDevice.setX(stationObj.getString("targetCoordinateX"));
                    locatorDevice.setY(stationObj.getString("targetCoordinateY"));
                }
                locatorDevice.setDeviceRelation(stationObj.getString("stationOneRelevance"));
                deviceList.add(locatorDevice);
            }
            // 当基站二存在时需要存储设备信息
            if (StringUtils.isNotEmpty(stationObj.getString("twoBasestationCodes"))) {
                WorkingFaceLocatorDevice locatorDevice = new WorkingFaceLocatorDevice();
                locatorDevice.setGroupCode(settingEntity.getCompanyCode());
                locatorDevice.setMineCode(settingEntity.getMineCode());
                locatorDevice.setWorkingFaceId(workFaceId);
                locatorDevice.setDeviceCode(stationObj.getString("twoBasestationCodes"));
                locatorDevice.setDeviceType(4);
                locatorDevice.setDeviceRelation(stationObj.getString("stationTwoRelevance"));
                deviceList.add(locatorDevice);
            }
        }

        return deviceList;
    }

    // endregion 解析工作面定位测距设备

    // region 生成接入系统信息

    /**
     * 生成接入系统信息
     *
     * @param wfpd           工作面信息
     * @param systemEnabled  系统是否启用
     * @param routeEnabled   接入是否启用
     * @param category       系统分类
     * @param isStatusSystem 是否设备监测系统
     * @param unionId        连巷工作面 ID
     * @return /
     */
    private ModelSystem generateModelSystem(
            WorkingFaceProgressDefinition wfpd,
            Short systemEnabled,
            Short routeEnabled,
            String category,
            Boolean isStatusSystem,
            String unionId
    )
    {
        String groupCode = wfpd.getGroupCode();
        String mineCode = wfpd.getMineCode();
        String wfId = wfpd.getWorkingFaceId();
        String wfName = wfpd.getWorkingFaceName();

        ModelSystem modelSystem = new ModelSystem();

        // 设备监测系统
        if (Boolean.TRUE.equals(isStatusSystem)) {
            modelSystem.setId("991" + wfId);
            modelSystem.setName(wfName + "定位设备监测");
        }
        else {
            modelSystem.setId("990" + wfId);
            // modelSystem.setName(wfName + "进尺监控");
            modelSystem.setName(wfName);
        }

        modelSystem.setGroupCode(groupCode);
        modelSystem.setMineCode(mineCode);
        modelSystem.setSourceSystemCode(wfId);
        modelSystem.setSourceSystemName(wfName);
        modelSystem.setCategory(category);

        try {
            modelSystem.setMineId(Long.parseLong(mineCode));
        }
        catch (Exception ex) {
            modelSystem.setMineId(0L);
            log.warn("mine_code parse error: {}", ex.getMessage(), ex);
        }

        // 定位设备监测
        if (Boolean.TRUE.equals(isStatusSystem)) {
            modelSystem.setConfTable(null);
            modelSystem.setUpdateTable("working_face_locator_status");
            modelSystem.setHistoryTable("wf_lctr_stts");
            modelSystem.setDefinitionRoutingKey(null);
            modelSystem.setUpdateRoutingKey(String.format("update.%s.%s.%s", groupCode, mineCode, wfId));
            modelSystem.setHistoryRoutingKey(null);
            modelSystem.setDefinitionQueue(null);
            modelSystem.setUpdateQueue("wfl_real_[mine_code]");
            modelSystem.setHistoryQueue(null);
            modelSystem.setExchange("ex_working_face_locator");
            modelSystem.setWarnService((short) 1);
            modelSystem.setConverterService((short) 1);
            modelSystem.setAccessConfigurable("1");
            modelSystem.setPatchEnabled("0");

            modelSystem.setSid(991);
        }
        else {
            // 工作面进尺监控，非设备监测
            modelSystem.setConfTable("working_face_progress_definition");
            modelSystem.setUpdateTable("working_face_progress_realtime");
            modelSystem.setHistoryTable("wf_prgrss");
            modelSystem.setDefinitionRoutingKey(String.format("definition.%s.%s.%s", groupCode, mineCode, wfId));
            modelSystem.setUpdateRoutingKey(String.format("update.%s.%s.%s", groupCode, mineCode, wfId));
            modelSystem.setHistoryRoutingKey(String.format("history.%s.%s.%s", groupCode, mineCode, wfId));
            modelSystem.setDefinitionQueue("wfp_def_[mine_code]");
            modelSystem.setUpdateQueue("wfp_real_[mine_code]");
            modelSystem.setHistoryQueue("wfp_history_[mine_code]");
            modelSystem.setExchange("ex_working_face_progress");
            modelSystem.setWarnService((short) 0);
            modelSystem.setConverterService((short) 0);
            modelSystem.setAccessConfigurable("0");
            modelSystem.setPatchEnabled("1");
            modelSystem.setBusinessSystemCode(StringUtils.isEmpty(unionId) ? null : unionId);

            modelSystem.setSid(990);
        }

        modelSystem.setHandlerType("annotation");
        modelSystem.setRouteService(routeEnabled);
        modelSystem.setEarlyWarnService((short) 0);
        modelSystem.setSoftDeletionEnabled("0");

        // 是否启用
        modelSystem.setFlag(String.valueOf(systemEnabled));
        // 启用时间
        if (systemEnabled == 1) {
            // 实际更新语句中，只有既存 flag ！= 1 时才更新此字段
            modelSystem.setEnabledAt(new Timestamp(System.currentTimeMillis()));
        }

        // 默认只有1条配置
        modelSystem.setControlSourceId("1");
        // modelSystem.setCreatedAt(updateStamp);
        modelSystem.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        return modelSystem;
    }

    // endregion 生成接入系统信息

    // region 生成接入系统在线监测配置

    /**
     * 生成接入系统在线监测配置
     *
     * @param wfpd           工作面信息
     * @param isStatusSystem 是否设备监测系统
     * @return /
     */
    private ModelSystemAlive generateModelSystemAlive(
            WorkingFaceProgressDefinition wfpd,
            Boolean isStatusSystem
    )
    {
        String groupCode = wfpd.getGroupCode();
        String mineCode = wfpd.getMineCode();
        String wfId = wfpd.getWorkingFaceId();
        String wfName = wfpd.getWorkingFaceName();

        ModelSystemAlive modelSystemAlive = new ModelSystemAlive();

        // 设备监测系统
        if (Boolean.TRUE.equals(isStatusSystem)) {
            modelSystemAlive.setSystemId("991" + wfId);
            modelSystemAlive.setSystemName(wfName + "定位设备监测");
        }
        else {
            modelSystemAlive.setSystemId("990" + wfId);
            // modelSystemAlive.setSystemName(wfName + "进尺监控");
            modelSystemAlive.setSystemName(wfName);
        }

        modelSystemAlive.setParentId(wfId);
        modelSystemAlive.setGroupCode(groupCode);
        modelSystemAlive.setMineCode(mineCode);

        try {
            modelSystemAlive.setMineId(Long.parseLong(mineCode));
        }
        catch (Exception ex) {
            modelSystemAlive.setMineId(0L);
            log.warn("mine_code parse error: {}", ex.getMessage(), ex);
        }

        // 定位设备监测
        if (Boolean.TRUE.equals(isStatusSystem)) {
            modelSystemAlive.setUpdateTable("working_face_locator_status");
        }
        else {
            // 工作面进尺监控，非设备监测
            modelSystemAlive.setUpdateTable("working_face_progress_realtime");
        }

        modelSystemAlive.setTimeLimit(1800);
        modelSystemAlive.setDateName("data_time");
        modelSystemAlive.setUpdatedUser("定位解算配置下发");
        modelSystemAlive.setSystemIdField("system_code");
        modelSystemAlive.setSystemIdMode((short) 2);
        modelSystemAlive.setUpdatedAt(DateUtil.getTimestamp());

        return modelSystemAlive;
    }

    // endregion 生成接入系统在线监测配置

    // endregion 私有方法
}
