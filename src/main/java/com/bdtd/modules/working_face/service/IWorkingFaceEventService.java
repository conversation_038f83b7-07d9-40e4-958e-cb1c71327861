package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.entity.WorkingFaceEvent;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public interface IWorkingFaceEventService extends IService<WorkingFaceEvent>
{

    /**
     * 列表查询
     *
     * @return /
     */
    List<WorkingFaceEvent> selectWorkFaceEventList(
            String workFaceId,
            String workFaceType,
            String evenType,
            String recordType,
            String startTime,
            String endTime
    ) throws Exception;

    /**
     * 分页查询
     *
     * @return /
     */
    IPage<WorkingFaceEvent> selectWorkFaceEventPage(
            String workFaceId,
            String workFaceType,
            String evenType,
            String recordType,
            String startTime,
            String endTime,
            Integer pageSize,
            Integer pageIndex
    ) throws Exception;

    /**
     * 创建记录
     *
     * @param workingFaceEventList 新增参数
     * @return /
     */
    int createRecord(List<WorkingFaceEvent> workingFaceEventList) throws Exception;

}
