package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.common.TaosDBException;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定位解算原始数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
public interface IPositionRawHistoryService {

    /**
     * 同步人员定位解算原始数据
     *
     * @param syncCriteria 同步条件
     * @param actionStr    操作区分
     * @param source       数据源
     * @param wrongChnAnal 天线反装报警分析开启
     * @param mockMode     是否模拟模式，只返回同步结果，相关数据不入库
     * @return 同步结果
     */
    PositionCalcSyncResult syncPositionRawHistory(
            PositionCalcQueryCriteria syncCriteria,
            String actionStr,
            String source,
            Boolean wrongChnAnal,
            Boolean mockMode
    );

    /**
     * 代理人员定位解算原始数据
     *
     * @param queryCriteria 同步条件
     * @param source        数据源
     * @return 同步结果
     */
    Object passPositionRawHistory(PositionCalcQueryCriteria queryCriteria, String source);

    /**
     * 修正原始数据接口
     *
     * @param queryCriteria 同步条件
     * @return 同步结果
     */
    Integer modifyPositionRawHistory(PositionCalcQueryCriteria queryCriteria) throws BadRequestException;

    /**
     * 分页查询人员定位解算原始数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    Map<String, Object> queryPositionRawHistoryPage(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 查询人员定位解算原始数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    Map<String, Object> queryPositionRawHistoryList(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException;

    /**
     * 查询人员定位解算原始数据（按时间窗口分组）
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    Map queryIntervalList(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException, SQLException;

    /**
     * 查询点位最新历史值列表（使用 Union 查询）
     *
     * @param workingFaceIds 工作面ID列表
     * @return /
     */
    List<PositionRawHistory> queryLastPositionRawHistoryList(List<String> workingFaceIds);

    /**
     * 查询点位最新历史值列表（使用 IN 查询）
     *
     * @param workingFaceIds 工作面ID列表
     * @return /
     */
    List<PositionRawHistory> queryLastPositionRawHistoryListUsingInQuery(List<String> workingFaceIds);

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 得最后同步的时间
     */
    Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria);

    /**
     * 写入解算原始数据
     *
     * @param historyObj 解算原始数据内容
     * @throws BadRequestException 输入验证错误
     */
    void create(PositionRawHistory historyObj) throws BadRequestException;

    /**
     * 批量写入解算原始数据
     *
     * @param historyList 解算原始数据内容列表
     * @throws BadRequestException 输入验证错误
     */
    void bulkCreate(List<PositionRawHistory> historyList) throws BadRequestException, TaosDBException;

}
