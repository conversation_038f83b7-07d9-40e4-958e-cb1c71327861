package com.bdtd.modules.working_face.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.conf.RabbitTopicConfig;
import com.bdtd.modules.monitor.entity.AutoAlarmMsgs;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IAutoAlarmMsgsService;
import com.bdtd.modules.working_face.dto.*;
import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.modules.working_face.service.IPositionRawHistoryService;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressDefinitionService;
import com.bdtd.util.DateUtil;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.exception.ServerRuntimeException;
import com.bdtd.util.middleware.MqSendUtil;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import com.bdtd.util.tdengine.contract.ITaosEntity;
import com.bdtd.util.tdengine.entity.TaosStableEntity;
import com.bdtd.util.tdengine.entity.TaosTableEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpRequest;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.io.support.ClassicRequestBuilder;
import org.apache.hc.core5.net.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 定位解算原始数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@Service
@Slf4j
public class PositionRawHistoryImpl implements IPositionRawHistoryService {

    @Autowired
    private IWorkingFaceProgressDefinitionService workingFaceProgressDefinitionService;
    @Autowired
    private IAutoAlarmMsgsService autoAlarmMsgsService;

    @Autowired
    private MqSendUtil mqSendUtil;
    @Resource
    private TaosConnector taosConnector;

    @Resource(name = "positionHistoryLastSyncTimeMap")
    private ConcurrentHashMap<String, Timestamp> positionHistoryLastSyncTimeMap;

    @Value("${scheduled.position-raw-history-sync-url}")
    private String positionRawBizUrl;
    @Value("${scheduled.position-calc-history-sync-url}")
    private String positionCalcBizUrl;

    // // 原始数据同步时，数据包出现多天线的数据，是否以较新时间的标签卡进行判断
    // @Value("${biz.position-raw-history.wrong-channel-check-by-newest}")
    // private Boolean checkWrongChannelByNewest;
    // 原始数据同步时，数据包出现多天线的数据，是否丢弃非最新时间的标签卡数据
    @Value("${biz.position-raw-history.discard-not-newest}")
    private Boolean discardNotNewest;

    // 天线反装判断时长，超过此时间才推送报警（秒）
    @Value("${biz.wrong-channel-warn-limit}")
    private Integer wrongChannelWarnLimit;

    // 原始数据查询允许时间范围（小时）
    @Value("${biz.position-raw-history.query-limit}")
    private Integer queryLimit;

    /**
     * 同步人员定位解算原始数据
     *
     * @param syncCriteria 同步条件
     * @param actionStr    操作区分
     * @param source       数据源
     * @param wrongChnAnal 天线反装报警分析开启
     * @param mockMode     是否模拟模式，只返回同步结果，相关数据不入库
     * @return 同步结果
     */
    @Override
    public PositionCalcSyncResult syncPositionRawHistory(
        PositionCalcQueryCriteria syncCriteria,
        String actionStr,
        String source,
        Boolean wrongChnAnal,
        Boolean mockMode
    )
    {
        // region 从接口同步数据

        // 同步数据源地址, 默认原始数据, 也提供历史数据的选项
        String syncUrl = positionRawBizUrl;
        if (StringUtil.isNotEmpty(source) && "calc".equalsIgnoreCase(source)) {
            syncUrl = positionCalcBizUrl;
        }

        PositionCalcSyncResult result = new PositionCalcSyncResult();
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(syncCriteria.getStartDate())) {
            queryMap.put("startDate", syncCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getEndDate())) {
            queryMap.put("endDate", syncCriteria.getEndDate());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getMineCode())) {
            queryMap.put("mineCode", syncCriteria.getMineCode());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getWorkFaceId())) {
            queryMap.put("workFaceId", syncCriteria.getWorkFaceId());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getStationCode())) {
            queryMap.put("stationCode", syncCriteria.getStationCode());
        }
        if (StringUtil.isNotEmpty(syncCriteria.getCardNumber())) {
            queryMap.put("cardNumber", syncCriteria.getCardNumber());
        }

        JSONObject respObj = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet;
            // 接口代理
            if (syncUrl.endsWith("proxy")) {
                URIBuilder uriBuilder = new URIBuilder(syncUrl);
                for (Map.Entry<String, Object> param : queryMap.entrySet()) {
                    uriBuilder.addParameter(param.getKey(), param.getValue().toString());
                }
                httpGet = ClassicRequestBuilder.get(uriBuilder.toString()).build();
            }
            else {
                httpGet = ClassicRequestBuilder.get(syncUrl).build();
                StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
                httpGet.setEntity(entity);
            }

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    respObj = JSON.parseObject(is, JSONObject.class);
                    log.debug(
                        "syncPositionRawHistory return result (startDate: {}, endDate: {}, mineCode: {}, workFaceId: {}): {}",
                        syncCriteria.getStartDate(),
                        syncCriteria.getEndDate(),
                        "null",
                        "null",
                        (respObj == null ? "" : respObj.toJSONString())
                    );
                }
            }
        }
        catch (Exception ex) {
            String errMsg = String.format(
                "%s 接口调用错误（%s ~ %s）, %s",
                actionStr,
                syncCriteria.getStartDate(),
                syncCriteria.getEndDate(),
                ex.getMessage()
            );
            serviceCallLogs.add(ServiceCallLog.createNew(
                errMsg,
                (byte) 1,
                "0"
            ));
            log.error("{}: {}", syncUrl, errMsg, ex);

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        // 返回内容无效
        if (respObj == null ||
            !respObj.containsKey("data") ||
            respObj.getJSONArray("data") == null
        ) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                String.format(
                    "%s 返回内容为空（%s ~ %s）",
                    actionStr,
                    syncCriteria.getStartDate(),
                    syncCriteria.getEndDate()
                ),
                (byte) 1,
                "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }
        // 返回内容为空
        if (respObj.getJSONArray("data").isEmpty()) {
            // PositionRawHistorySyncTask 中统一管理
            // // 返回数据为空，即此时间段没有数据，记录时间，防止重复同步该时间区间
            // positionHistoryLastSyncTimeMap.put("raw", Timestamp.valueOf(syncCriteria.getEndDate()));

            serviceCallLogs.add(ServiceCallLog.createNew(
                String.format(
                    "%s 无同步数据（%s ~ %s）",
                    actionStr,
                    syncCriteria.getStartDate(),
                    syncCriteria.getEndDate()
                ),
                (byte) 0,
                "0"
            ));

            result.setSucceed(false);
            result.setCount(0);
            result.setSyncLogs(serviceCallLogs);
            return result;
        }

        // endregion 从接口同步数据

        // region 解析数据

        // 原始数据写入列表
        List<PositionRawHistory> insertList = new ArrayList<>();
        // 原始数据写入日志
        Map<String, Integer> insertLogMap = new HashMap<>();

        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        JSONArray historyArr = null;

        try {
            historyArr = respObj.getJSONArray("data");
            for (Object obj : historyArr) {
                JSONObject jsonObj = (JSONObject) obj;

                // 基于基站的数据列表
                List<PositionRawHistory> itemList = new ArrayList<>();

                // 工作面ID
                String workingFaceId = jsonObj.getString("workFaceId");
                // 标签卡
                String cardNumber = jsonObj.getString("cardNumber");
                // 基站原始数据
                String mqInfoStr = jsonObj.getString("mqInfo");

                // region 解析队列消息

                if (StringUtil.isNotEmpty(mqInfoStr)) {
                    try {
                        List<JSONObject> mqList = JSON.parseArray(mqInfoStr, JSONObject.class);
                        if (mqList != null && !mqList.isEmpty()) {
                            Timestamp maxTs = null;
                            for (JSONObject stationObj : mqList) {
                                // 数据时间
                                Timestamp dt = new Timestamp(stationObj.getLong("timestamp") * 1000);
                                if (maxTs == null || dt.after(maxTs)) {
                                    maxTs = dt;
                                }

                                PositionRawHistory stationItem = new PositionRawHistory();
                                stationItem.setMqInfo(mqInfoStr);
                                stationItem.setStationCode(stationObj.getString("bs_sn"));
                                stationItem.setChannel(stationObj.getInteger("channel"));
                                stationItem.setDistance(stationObj.getInteger("dist"));
                                stationItem.setDataTime(dt);
                                stationItem.setUpdateTime(dt);
                                itemList.add(stationItem);
                            }

                            // !!! 如果数据包中两条天线的数据时间一样，同样可能存在问题
                            for (PositionRawHistory item : itemList) {
                                // 标识是否是最新记录
                                item.setIsLatestInBatch(!item.getDataTime().before(maxTs));
                            }
                        }
                    }
                    catch (Exception ex) {
                        log.warn(String.format("mqInfo string parse error: %s", mqInfoStr), ex);
                    }
                }

                // 未解析出数据
                if (itemList.isEmpty()) {
                    PositionRawHistory historyObj = new PositionRawHistory();
                    historyObj.setMqInfo(mqInfoStr);
                    historyObj.setIsLatestInBatch(true);
                    itemList.add(historyObj);
                }

                // endregion 解析队列消息

                // region 解析消息处理

                // 赋值基础字段
                for (PositionRawHistory historyObj : itemList) {
                    historyObj.setUpdateTime(jsonObj.getObject("updateTime", Timestamp.class));
                    historyObj.setX(jsonObj.getString("x"));
                    historyObj.setY(jsonObj.getString("y"));
                    historyObj.setPowerValue(jsonObj.getInteger("powerValue"));
                    historyObj.setSignalValue(jsonObj.getInteger("signalValue"));
                    historyObj.setOffline(jsonObj.getString("offLine"));
                    historyObj.setUpdatedAt(nowTime);
                    historyObj.setCardNumber(cardNumber);
                    historyObj.setWorkFaceId(workingFaceId);
                    historyObj.setWorkFaceName(jsonObj.getString("workFaceName"));
                    historyObj.setMineCode(jsonObj.getString("mineCode"));

                    // 不考虑丢弃旧数据 或者 当前是最新时间记录，记录日志
                    if (!Boolean.TRUE.equals(discardNotNewest) || Boolean.TRUE.equals(historyObj.getIsLatestInBatch())) {
                        String logKey = historyObj.getMineCode();
                        insertLogMap.computeIfAbsent(logKey, k -> 0);
                        insertLogMap.put(logKey, insertLogMap.get(logKey) + 1);
                    }
                }

                // endregion 解析消息处理

                // region 同批次旧数据清洗

                // 丢弃旧数据
                if (Boolean.TRUE.equals(discardNotNewest)) {
                    itemList = itemList.stream()
                                       .filter(PositionRawHistory::getIsLatestInBatch)
                                       .collect(Collectors.toList());
                }

                // endregion 同批次旧数据清洗

                // region 数据降采样，每分钟一条

                // 基于基站的数据字典
                // - 键: 工作面ID、基站编码、标签卡编码、数据时间（按分钟取模）
                // - 值: 基站原始数据，只取每分钟第一条
                Map<String, PositionRawHistory> itemMap = new HashMap<>();

                // 清洗原始数据，只取每分钟第一条
                for (PositionRawHistory historyObj : itemList) {
                    long dtTs = historyObj.getDataTime().getTime() / 1000;
                    long sampleDtTs = dtTs - dtTs % 60;
                    String sampleKey = String.format(
                        "%s,%s,%s,%d",
                        workingFaceId,
                        cardNumber,
                        historyObj.getStationCode(),
                        sampleDtTs
                    );

                    if (!itemMap.containsKey(sampleKey) || historyObj.getDataTime().before(itemMap.get(sampleKey).getDataTime())) {
                        historyObj.setUpdateTime(new Timestamp(sampleDtTs * 1000));
                        itemMap.put(sampleKey, historyObj);
                    }
                }

                insertList.addAll(itemMap.values());

                // endregion 数据降采样，每分钟一条
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                String.format(
                    "%s 接口返回解析错误（%s ~ %s）",
                    actionStr,
                    syncCriteria.getStartDate(),
                    syncCriteria.getEndDate()
                ),
                (byte) 1,
                "0"
            ));
            log.error(ex.getMessage(), ex);
        }

        // endregion 解析数据

        // region 天线反装报警分析

        // 工作面更新列表
        List<WorkingFaceProgressDefinition> wfpdUpdList = new ArrayList<>();
        // 需要进行反装报警的工作面
        Map<String, WorkingFaceProgressDefinition> shouldCheckWfMap = new HashMap<>();
        // 天线反装分析状态 Map, 工作面 ID => [ 报警状态(1,开始时间,结束时间), 报警状态(0,开始时间,结束时间) ]
        Map<String, List<WrongChannelCheckDetail>> wrongChannelStateMap = new HashMap<>();
        // 工作面反装报警列表
        List<WrongChannelCheckDetail> wrongChannelList = new ArrayList<>();

        try {
            if (Boolean.TRUE.equals(wrongChnAnal)) {
                // region 查询需要进行天线反装等告警分析的记录

                // 数据接入开启且工作模式为1、2的工作面
                shouldCheckWfMap = workingFaceProgressDefinitionService
                    // .selectWorkModelChangedList()
                    .selectWorkModelAssignedList(Arrays.asList("1", "2"))
                    .stream()
                    .collect(Collectors.toMap(
                        WorkingFaceProgressDefinition::getWorkingFaceId,
                        w -> w
                    ));

                // endregion 查询需要进行天线反装等告警分析的记录

                // region 根据降采样后的数据进行分析天线反装报警

                // 按数据时间排序
                insertList.sort((a, b) -> a.getDataTime().compareTo(b.getDataTime()));
                // 遍历 insertList，根据降采样后的数据进行分析
                for (PositionRawHistory historyObj : insertList) {
                    // 不是需要检查天线反装的工作面，跳过
                    if (!shouldCheckWfMap.containsKey(historyObj.getWorkFaceId())) {
                        continue;
                    }

                    // 工作面定义
                    WorkingFaceProgressDefinition wfpd = shouldCheckWfMap.get(historyObj.getWorkFaceId());
                    // 更新数据变更时间
                    if (Objects.equals(wfpd.getWorkModeChanged(), "1")) {
                        wfpd.setFirstDataTime(historyObj.getDataTime());
                        wfpd.setWorkModeChanged("0");
                        wfpdUpdList.add(wfpd);
                    }

                    // 如果数据时间早于工作面工作模式最后变更时间，则不再分析
                    if (wfpd.getWorkModeChangedAt() != null && !historyObj.getDataTime().after(wfpd.getWorkModeChangedAt())) {
                        log.debug(
                            "### wf {} work mode changed，data time {} is before changed time {}, skipped ###",
                            historyObj.getWorkFaceId(),
                            historyObj.getDataTime(),
                            wfpd.getWorkModeChangedAt()
                        );
                        continue;
                    }

                    // 根据 shouldCheckWfMap 初始化 天线反装分析状态 Map
                    if (!wrongChannelStateMap.containsKey(historyObj.getWorkFaceId())) {
                        // 根据工作面信息初始化 天线反装分析状态
                        wrongChannelStateMap.put(historyObj.getWorkFaceId(), new ArrayList<>());
                        wrongChannelStateMap.get(historyObj.getWorkFaceId()).add(
                            new WrongChannelCheckDetail(shouldCheckWfMap.get(historyObj.getWorkFaceId()))
                        );
                    }

                    // 既存分析状态
                    List<WrongChannelCheckDetail> existedDetailList = wrongChannelStateMap.get(wfpd.getWorkingFaceId());
                    WrongChannelCheckDetail lastDetail = existedDetailList.get(existedDetailList.size() - 1);

                    // 数据时间早于既存状态的结束时间
                    if (lastDetail.getEndTime() != null && !historyObj.getDataTime().after(lastDetail.getEndTime())) {
                        log.debug(
                            "### wf {} data time {} is before end time of latest state （{} ~ {}）, skipped ###",
                            historyObj.getWorkFaceId(),
                            historyObj.getDataTime(),
                            lastDetail.getStartTime(),
                            lastDetail.getEndTime()
                        );
                        continue;
                    }

                    // 天线是否反装, 1 装反 0 正常
                    Integer wrongState =
                        "1".equals(wfpd.getWorkMode()) && historyObj.getChannel() != 2 || "2".equals(wfpd.getWorkMode()) && historyObj.getChannel() != 1
                            ? 1
                            : 0;
                    // 工作面信息中还未设置 天线反装分析状态，直接设置
                    if (lastDetail.getWrongState() == null || lastDetail.getStartTime() == null) {
                        lastDetail.setWrongState(wrongState);
                        lastDetail.setStartTime(historyObj.getDataTime());
                        lastDetail.setEndTime(historyObj.getDataTime());
                        continue;
                    }

                    // 反装状态和既有的一致，检查是否是反装并且超过报警持续阈值
                    if (Objects.equals(lastDetail.getWrongState(), wrongState)) {
                        // 更新状态结束时间
                        lastDetail.setEndTime(historyObj.getDataTime());
                        // 反装状态 并且 超过报警持续阈值，设置报警触发时间
                        if (Objects.equals(wrongState, 1)
                            && (historyObj.getDataTime().getTime() - lastDetail.getStartTime().getTime()) > (wrongChannelWarnLimit * 1000)
                        ) {
                            lastDetail.setTriggerTime(historyObj.getDataTime());
                        }
                    }
                    else {
                        // 反装状态和既有发生变化
                        WrongChannelCheckDetail newDetail = new WrongChannelCheckDetail(historyObj, wrongState);
                        existedDetailList.add(newDetail);
                    }
                }

                // 遍历 wrongChannelStateMap 分析天线反装报警
                Map<String, WrongChannelCheckDetail> latestStateMap = new HashMap<>();
                for (Map.Entry<String, List<WrongChannelCheckDetail>> entry : wrongChannelStateMap.entrySet()) {
                    if (entry.getValue().isEmpty()) {
                        continue;
                    }
                    List<WrongChannelCheckDetail> detailList = entry.getValue();

                    // 既存反装状态 且 已报警，是否解警
                    if (detailList.size() > 1) {
                        if (Objects.equals(detailList.get(0).getWrongState(), 1)
                            && detailList.get(0).getWarnTime() != null
                            && Objects.equals(detailList.get(1).getWrongState(), 0)
                        ) {
                            // 生成解警
                            wrongChannelList.add(detailList.get(1));
                        }
                    }

                    // 最新状态索引
                    int r1stIdx = detailList.size() - 1;
                    // 最新状态是否符合告警条件
                    if (Objects.equals(detailList.get(r1stIdx).getWrongState(), 1)
                        && detailList.get(r1stIdx).getWarnTime() != null
                        && detailList.get(r1stIdx).getTriggerTime() != null
                    ) {
                        // 生成告警
                        wrongChannelList.add(detailList.get(r1stIdx));
                    }

                    // 保存最新状态
                    latestStateMap.put(entry.getKey(), detailList.get(r1stIdx));
                }

                // endregion 根据降采样后的数据进行分析天线反装报警

                // region 根据天线反装报警状态更新工作面信息

                // 根据分析结果更新工作面信息 latestStateMap
                for (Map.Entry<String, WrongChannelCheckDetail> entry : latestStateMap.entrySet()) {
                    Optional<WorkingFaceProgressDefinition> upsertEntity = wfpdUpdList
                        .stream()
                        .filter(wfpd -> wfpd.getWorkingFaceId().equals(entry.getKey()))
                        .findFirst();

                    WorkingFaceProgressDefinition wfpd = null;
                    if (!upsertEntity.isPresent()) {
                        wfpd = shouldCheckWfMap.get(entry.getKey());
                    }
                    else {
                        wfpd = upsertEntity.get();
                    }

                    wfpd.setWrongChannelState(entry.getValue().getWrongState());
                    wfpd.setWrongChannelStateFrom(entry.getValue().getStartTime());
                    wfpd.setWrongChannelWarnTime(entry.getValue().getTriggerTime());

                    if (!upsertEntity.isPresent()) {
                        wfpdUpdList.add(wfpd);
                    }
                }

                // endregion 根据天线反装报警状态更新工作面信息
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                String.format(
                    "%s 天线反装报警分析错误（%s ~ %s）",
                    actionStr,
                    syncCriteria.getStartDate(),
                    syncCriteria.getEndDate()
                ),
                (byte) 1,
                "0"
            ));
            log.error(ex.getMessage(), ex);
        }

        // endregion 天线反装报警分析

        // region 数据入库

        List<JSONObject> pushObjList = null;
        try {
            // 非模拟模式，数据入库
            if (!Boolean.TRUE.equals(mockMode)) {
                // 入库原始数据
                if (!insertList.isEmpty()) {
                    bulkCreate(insertList);
                }
                // 工作面定义更新
                if (!wfpdUpdList.isEmpty()) {
                    workingFaceProgressDefinitionService.updateBatchById(wfpdUpdList);
                }

                // 分析天线反装告警
                if (!wrongChannelList.isEmpty()) {
                    pushObjList = pushWrongChannelWarnToPushMsg(wrongChannelList, shouldCheckWfMap, wrongChannelStateMap, syncCriteria, actionStr, mockMode);
                }

                // 记录同步日志
                // entry: mineCode -> 原始数据记录数
                for (Map.Entry<String, Integer> entry : insertLogMap.entrySet()) {
                    String[] keyArr = entry.getKey().split(",");
                    log.debug(
                        "Position raw data ({} - {}) sync result: {} => {} insert, {} received",
                        syncCriteria.getStartDate(),
                        syncCriteria.getEndDate(),
                        keyArr[0],
                        entry.getValue(),
                        historyArr.size()
                    );

                    serviceCallLogs.add(ServiceCallLog.createNew(
                        String.format(
                            "%s 完成数据同步（%s ~ %s, 插入 %d, 总共 %d, 获取 %d）",
                            actionStr,
                            syncCriteria.getStartDate(),
                            syncCriteria.getEndDate(),
                            entry.getValue(),
                            insertList.size(),
                            historyArr.size()
                        ),
                        (byte) 0,
                        keyArr[0]
                    ));
                }

                // PositionRawHistorySyncTask 中统一管理
                // // 返回数据不为空，清空缓存的最后同步时间，继续从历史库取得最后同步时间
                // positionHistoryLastSyncTimeMap.remove("raw");
            }
            else {
                // 分析天线反装告警
                if (!wrongChannelList.isEmpty()) {
                    pushObjList = pushWrongChannelWarnToPushMsg(wrongChannelList, shouldCheckWfMap, wrongChannelStateMap, syncCriteria, actionStr, mockMode);
                }
            }

            result.setSucceed(true);
            result.setCount(insertList.size());
            return result;
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                String.format(
                    "%s 完成数据同步（%s ~ %s）",
                    actionStr,
                    syncCriteria.getStartDate(),
                    syncCriteria.getEndDate()
                ),
                (byte) 1,
                "0"
            ));
            log.error(ex.getMessage(), ex);

            result.setSucceed(false);
            result.setCount(0);
            return result;
        }
        finally {
            result.setPushWarnObjList(pushObjList);
            result.setWrongChannelList(wrongChannelList);
            result.setWrongChannelStateMap(wrongChannelStateMap);
            result.setWfpdUpdateList(wfpdUpdList);
            result.setRawInsertList(insertList);
            result.setSyncLogs(serviceCallLogs);
        }

        // endregion 数据入库
    }

    /**
     * 代理人员定位解算原始数据
     *
     * @param queryCriteria 同步条件
     * @param source        数据源
     * @return 同步结果
     */
    @Override
    public Object passPositionRawHistory(PositionCalcQueryCriteria queryCriteria, String source)
    {
        // 同步数据源地址, 默认原始数据, 也提供历史数据的选项
        String passUrl = positionRawBizUrl;
        if (StringUtil.isNotEmpty(source) && "calc".equalsIgnoreCase(source)) {
            passUrl = positionCalcBizUrl;
        }

        // 添加参数
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStartDate())) {
            queryMap.put("startDate", queryCriteria.getStartDate());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
            queryMap.put("endDate", queryCriteria.getEndDate());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            queryMap.put("mineCode", queryCriteria.getMineCode());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            queryMap.put("workFaceId", queryCriteria.getWorkFaceId());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            queryMap.put("stationCode", queryCriteria.getStationCode());
        }
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            queryMap.put("cardNumber", queryCriteria.getCardNumber());
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            ClassicHttpRequest httpGet;
            // 接口代理
            if (passUrl.endsWith("proxy")) {
                URIBuilder uriBuilder = new URIBuilder(passUrl);
                for (Map.Entry<String, Object> param : queryMap.entrySet()) {
                    uriBuilder.addParameter(param.getKey(), param.getValue().toString());
                }
                httpGet = ClassicRequestBuilder.get(uriBuilder.toString()).build();
            }
            else {
                httpGet = ClassicRequestBuilder.get(passUrl).build();
                StringEntity entity = new StringEntity(JSON.toJSONString(queryMap), ContentType.APPLICATION_JSON);
                httpGet.setEntity(entity);
            }

            CloseableHttpResponse chr = httpClient.execute(httpGet);
            HttpEntity he = chr.getEntity();
            if (he != null) {
                try (InputStream is = he.getContent()) {
                    return JSON.parseObject(is, JSONObject.class);
                }
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    /**
     * 修正原始数据接口
     *
     * @param queryCriteria 同步条件
     * @return 同步结果
     */
    @Override
    public Integer modifyPositionRawHistory(PositionCalcQueryCriteria queryCriteria) throws BadRequestException
    {
        // 超级表信息
        TaosStableEntity stable = PositionRawHistory.buildTaosStableEntity();

        // 时间参数
        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
            }
            if (StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        // 有效的时间范围
        if (lStartTime == null || lEndTime == null) {
            throw new BadRequestException("未提供有效的时间范围");
        }

        // 查询条件
        StringBuilder whereBuffer = new StringBuilder();

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        whereBuffer.append("where `ts` >= '")
                   .append(queryDtf.format(lStartTime))
                   .append("' and `ts` <= '")
                   .append(queryDtf.format(lEndTime))
                   .append("' ");

        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }

        try {
            // 查询列表
            String listSql = "select "
                             + "  `ts` as `update_time`,  `x`,  `y`,  `power_value`,  `signal_value`, "
                             + "  `offline`,  `channel`,  `distance`,  `data_time`,  `mq_info`, `updated_at`, "
                             + "  `card_number`,  `station_code`,  `work_face_id`,  `work_face_name`,  `mine_code` "
                             + "from " + TaosConnector.prependDatabaseName(true, stable.getName()) + " "
                             + whereBuffer;
            List<PositionRawHistory> historyList = taosConnector.queryList(listSql, PositionRawHistory.class);

            // 修改天线通道
            for (PositionRawHistory historyObj : historyList) {
                try {
                    JSONArray jArr = JSON.parseArray(historyObj.getMqInfo());
                    for (Object obj : jArr) {
                        JSONObject jObj = (JSONObject) obj;
                        if (!historyObj.getStationCode().equals(jObj.getString("bs_sn"))) {
                            continue;
                        }

                        jObj.put("channel", queryCriteria.getChannel());
                    }

                    historyObj.setMqInfo(JSON.toJSONString(jArr));
                    historyObj.setChannel(queryCriteria.getChannel());
                }
                catch (Exception ex) {
                    log.warn(ex.getMessage(), ex);
                }
            }

            // 重新保存数据
            List<ITaosEntity> insPart = historyList
                .stream()
                .map(p -> (ITaosEntity) p)
                .collect(Collectors.toList());
            taosConnector.save(insPart);

            log.info(
                "+++ modify position raw data, total: {}, modified: {} +++",
                historyList.size(),
                insPart.size()
            );
            return historyList.size();
        }
        catch (TaosDBException ex) {
            log.error("list error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list error: {}", e.getMessage(), e);
        }

        return 0;
    }

    /**
     * 分页查询人员定位解算原始数据
     *
     * @param page          分页参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public Map<String, Object> queryPositionRawHistoryPage(
        Page<PositionRawHistory> page,
        PositionCalcQueryCriteria queryCriteria
    )
        throws BadRequestException
    {
        // 查询时间范围
        if ((StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate()))
            && (StringUtil.isEmpty(queryCriteria.getDataStart()) || StringUtil.isEmpty(queryCriteria.getDataEnd()))
        ) {
            throw new BadRequestException("未提供查询时间范围");
        }
        // 基站编码、标签卡号对应关系列表字符串, 基站1,标签卡1;基站2,标签卡2;
        Set<String[]> stationCardSet = new HashSet<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStationCards())) {
            stationCardSet = Arrays.stream(queryCriteria.getStationCards().split(";"))
                                   .filter(StringUtil::isNotEmpty)
                                   .map(s -> s.split(","))
                                   .collect(Collectors.toSet());
            if (stationCardSet.stream().anyMatch(sc -> sc.length < 2)) {
                throw new BadRequestException("未提供完整的基站编码、标签卡号");
            }
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate()) && StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        LocalDateTime lDataStart = null;
        LocalDateTime lDataEnd = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getDataStart()) && StringUtil.isNotEmpty(queryCriteria.getDataEnd())) {
                lDataStart = LocalDateTime.parse(queryCriteria.getDataStart(), inputDtf);
                lDataEnd = LocalDateTime.parse(queryCriteria.getDataEnd(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("数据时间格式错误");
        }

        // 开始时间应早于结束时间
        if (lStartTime != null) {
            if (!lStartTime.isBefore(lEndTime)) {
                throw new BadRequestException("开始时间应早于结束时间");
            }
            if (lStartTime.plusHours(queryLimit).isBefore(lEndTime)) {
                throw new BadRequestException(String.format("查询时间范围不允许超过 %d 小时", queryLimit));
            }
        }
        // 数据开始时间应早于数据结束时间
        if (lDataStart != null) {
            if (!lDataStart.isBefore(lDataEnd)) {
                throw new BadRequestException("数据开始时间应早于数据结束时间");
            }
            if (lDataStart.plusHours(queryLimit).isBefore(lDataEnd)) {
                throw new BadRequestException(String.format("查询数据时间范围不允许超过 %d 小时", queryLimit));
            }
        }

        // if (!TaosConnector.isPolymerizeValid(polymerize, false)) {
        //     throw new BadRequestException("聚合度无效");
        // }

        // 超级表信息
        TaosStableEntity stable = PositionRawHistory.buildTaosStableEntity();

        // 查询字段
        String queryFieldStr = "`ts` as `update_time`, `x`, `y`, `power_value`, `signal_value`, `offline`, `channel`, `distance`, `data_time`, `mq_info`, "
                               + "`updated_at`, "
                               + "`card_number`, `station_code`, `work_face_id`, `work_face_name`, `mine_code` ";

        // 表名
        String tableName = stable.getName();
        // if (!StringUtil.isEmpty(queryCriteria.getWorkFaceId())) {
        //     tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        // }

        // 查询条件
        StringBuffer whereBuffer = new StringBuffer();

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        int timeMode = queryCriteria.getTimeMode() == null
            ? 2
            : queryCriteria.getTimeMode();
        switch (timeMode) {
            case 0:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 1:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` > '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 3:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` <= '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
            case 2:
            default:
                if (lStartTime != null) {
                    whereBuffer.append("and `ts` >= '")
                               .append(queryDtf.format(lStartTime))
                               .append("' and `ts` < '")
                               .append(queryDtf.format(lEndTime))
                               .append("' ");
                }
                if (lDataStart != null) {
                    whereBuffer.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                }
                break;
        }

        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBuffer.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("' ");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }
        // 工作面名称
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceName())) {
            whereBuffer.append("and `work_face_name` like '").append(queryCriteria.getWorkFaceName()).append("%' ");
        }
        // 基站编码、标签卡号
        if (!stationCardSet.isEmpty()) {
            String scWhereStr = stationCardSet
                .stream()
                .map(sca -> String.format("(`station_code` = '%s' and `card_number` = '%s')", sca[0], sca[1]))
                .collect(Collectors.joining(" or "));
            whereBuffer.append(String.format("and (%s)", scWhereStr));
        }
        // 基站编码
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            whereBuffer.append("and `station_code` like '%").append(queryCriteria.getStationCode()).append("%' ");
        }
        // 标签卡号
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            whereBuffer.append("and `card_number` like '%").append(queryCriteria.getCardNumber()).append("%' ");
        }
        // 是否断线
        if (StringUtil.isNotEmpty(queryCriteria.getOffline())) {
            whereBuffer.append("and `offline` = '").append(queryCriteria.getOffline()).append("' ");
        }
        // 通道
        if (queryCriteria.getChannel() != null) {
            whereBuffer.append("and `channel` = ").append(queryCriteria.getChannel()).append(" ");
        }
        // 距离范围低
        if (queryCriteria.getMinDistance() != null) {
            whereBuffer.append("and `distance` >= ").append(queryCriteria.getMinDistance()).append(" ");
        }
        // 距离范围高
        if (queryCriteria.getMaxDistance() != null) {
            whereBuffer.append("and `distance` <= ").append(queryCriteria.getMaxDistance()).append(" ");
        }

        // 查询条件
        StringBuffer limitBdr = new StringBuffer();

        // 排序字段
        String order = "ts";
        boolean asc = false;
        if (page.getOrders() != null && !page.getOrders().isEmpty()) {
            order = page.getOrders().get(0).getColumn();
            asc = page.getOrders().get(0).isAsc();
        }
        if (!asc) {
            limitBdr.append(String.format("order by `%s` desc ", order));
        }
        else {
            limitBdr.append(String.format("order by `%s` asc ", order));
        }

        // 分页
        long current = page.getCurrent() < 1 ? 1 : page.getCurrent();
        limitBdr.append(String.format("limit %d,%d ", (current - 1) * page.getSize(), page.getSize()));

        // 返回内容
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("current", current);
        retMap.put("size", page.getSize());

        try {
            // 查询列表
            String listSql = "select " + queryFieldStr + " "
                             + "from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                             + (whereBuffer.length() > 0 ? "where " + whereBuffer.substring(4) : "") + " "
                             + limitBdr;
            List<PositionRawHistory> content = taosConnector.queryList(listSql, PositionRawHistory.class);
            retMap.put("content", content);

            // 查询数量
            String countSql = "select count(1) from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                              + (whereBuffer.length() > 0 ? " where " + whereBuffer.substring(4) : "");
            Long total = taosConnector.queryCount(countSql);

            total = total == null ? 0 : total;
            retMap.put("total", total);
            retMap.put("pages", Math.ceil((double) total / page.getSize()));

            return retMap;
        }
        catch (TaosDBException ex) {
            log.error("list error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 查询人员定位解算原始数据
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public Map<String, Object> queryPositionRawHistoryList(
        Page<PositionRawHistory> page,
        PositionCalcQueryCriteria queryCriteria
    )
        throws BadRequestException
    {
        // 查询时间范围
        if ((StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate()))
            && (StringUtil.isEmpty(queryCriteria.getDataStart()) || StringUtil.isEmpty(queryCriteria.getDataEnd()))
        ) {
            throw new BadRequestException("未提供查询时间范围");
        }

        // 基站编码、标签卡号对应关系列表字符串, 基站1,标签卡1;基站2,标签卡2;
        Set<String[]> stationCardSet = new HashSet<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStationCards())) {
            stationCardSet = Arrays.stream(queryCriteria.getStationCards().split(";"))
                                   .filter(StringUtil::isNotEmpty)
                                   .map(s -> s.split(","))
                                   .collect(Collectors.toSet());
            if (stationCardSet.stream().anyMatch(sc -> sc.length < 2)) {
                throw new BadRequestException("未提供完整的基站编码、标签卡号");
            }
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate()) && StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        LocalDateTime lDataStart = null;
        LocalDateTime lDataEnd = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getDataStart()) && StringUtil.isNotEmpty(queryCriteria.getDataEnd())) {
                lDataStart = LocalDateTime.parse(queryCriteria.getDataStart(), inputDtf);
                lDataEnd = LocalDateTime.parse(queryCriteria.getDataEnd(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("数据时间格式错误");
        }

        // 开始时间应早于结束时间
        if (lStartTime != null) {
            if (!lStartTime.isBefore(lEndTime)) {
                throw new BadRequestException("开始时间应早于结束时间");
            }
            if (lStartTime.plusHours(queryLimit).isBefore(lEndTime)) {
                throw new BadRequestException(String.format("查询时间范围不允许超过 %d 小时", queryLimit));
            }
        }
        // 数据开始时间应早于数据结束时间
        if (lDataStart != null) {
            if (!lDataStart.isBefore(lDataEnd)) {
                throw new BadRequestException("数据开始时间应早于数据结束时间");
            }
            if (lDataStart.plusHours(queryLimit).isBefore(lDataEnd)) {
                throw new BadRequestException(String.format("查询数据时间范围不允许超过 %d 小时", queryLimit));
            }
        }

        // if (!TaosConnector.isPolymerizeValid(polymerize, false)) {
        //     throw new BadRequestException("聚合度无效");
        // }

        // 超级表信息
        TaosStableEntity stable = PositionRawHistory.buildTaosStableEntity();

        // 查询字段
        String queryFieldStr = "`ts` as `update_time`, `x`, `y`, `power_value`, `signal_value`, "
                               + " `offline`, `channel`, `distance`, `data_time`, `mq_info`, `updated_at`, "
                               + " `card_number`, `station_code`, `work_face_id`, `work_face_name`, `mine_code` ";

        // 表名
        String tableName = stable.getName();
        // if (!StringUtil.isEmpty(queryCriteria.getWorkFaceId())) {
        //     tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        // }

        // 查询条件
        StringBuilder whereBldr = new StringBuilder();

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        int timeMode = queryCriteria.getTimeMode() == null
            ? 2
            : queryCriteria.getTimeMode();
        switch (timeMode) {
            case 0:
                if (lStartTime != null) {
                    whereBldr.append("and `ts` > '")
                             .append(queryDtf.format(lStartTime))
                             .append("' and `ts` < '")
                             .append(queryDtf.format(lEndTime))
                             .append("' ");
                }
                if (lDataStart != null) {
                    whereBldr.append("and `data_time` > '")
                             .append(queryDtf.format(lDataStart))
                             .append("' and `data_time` < '")
                             .append(queryDtf.format(lDataEnd))
                             .append("' ");
                }
                break;
            case 1:
                if (lStartTime != null) {
                    whereBldr.append("and `ts` > '")
                             .append(queryDtf.format(lStartTime))
                             .append("' and `ts` <= '")
                             .append(queryDtf.format(lEndTime))
                             .append("' ");
                }
                if (lDataStart != null) {
                    whereBldr.append("and `data_time` > '")
                             .append(queryDtf.format(lDataStart))
                             .append("' and `data_time` <= '")
                             .append(queryDtf.format(lDataEnd))
                             .append("' ");
                }
                break;
            case 3:
                if (lStartTime != null) {
                    whereBldr.append("and `ts` >= '")
                             .append(queryDtf.format(lStartTime))
                             .append("' and `ts` <= '")
                             .append(queryDtf.format(lEndTime))
                             .append("' ");
                }
                if (lDataStart != null) {
                    whereBldr.append("and `data_time` >= '")
                             .append(queryDtf.format(lDataStart))
                             .append("' and `data_time` <= '")
                             .append(queryDtf.format(lDataEnd))
                             .append("' ");
                }
                break;
            case 2:
            default:
                if (lStartTime != null) {
                    whereBldr.append("and `ts` >= '")
                             .append(queryDtf.format(lStartTime))
                             .append("' and `ts` < '")
                             .append(queryDtf.format(lEndTime))
                             .append("' ");
                }
                if (lDataStart != null) {
                    whereBldr.append("and `data_time` >= '")
                             .append(queryDtf.format(lDataStart))
                             .append("' and `data_time` < '")
                             .append(queryDtf.format(lDataEnd))
                             .append("' ");
                }
                break;
        }

        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBldr.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("' ");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBldr.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }
        // 工作面名称
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceName())) {
            whereBldr.append("and `work_face_name` like '").append(queryCriteria.getWorkFaceName()).append("%' ");
        }
        // 基站编码、标签卡号
        if (!stationCardSet.isEmpty()) {
            String scWhereStr = stationCardSet
                .stream()
                .map(sca -> String.format("(`station_code` = '%s' and `card_number` = '%s')", sca[0], sca[1]))
                .collect(Collectors.joining(" or "));
            whereBldr.append(String.format("and (%s)", scWhereStr));
        }
        // 基站编码
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            whereBldr.append("and `station_code` like '%").append(queryCriteria.getStationCode()).append("%' ");
        }
        // 标签卡号
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            whereBldr.append("and `card_number` like '%").append(queryCriteria.getCardNumber()).append("%' ");
        }
        // 是否断线
        if (StringUtil.isNotEmpty(queryCriteria.getOffline())) {
            whereBldr.append("and `offline` = '").append(queryCriteria.getOffline()).append("' ");
        }
        // 通道
        if (queryCriteria.getChannel() != null) {
            whereBldr.append("and `channel` = ").append(queryCriteria.getChannel()).append(" ");
        }
        // 距离范围低
        if (queryCriteria.getMinDistance() != null) {
            whereBldr.append("and `distance` >= ").append(queryCriteria.getMinDistance()).append(" ");
        }
        // 距离范围高
        if (queryCriteria.getMaxDistance() != null) {
            whereBldr.append("and `distance` <= ").append(queryCriteria.getMaxDistance()).append(" ");
        }

        // 查询条件
        StringBuilder limitBldr = new StringBuilder();

        // 排序字段
        String order = "ts";
        boolean asc = false;
        if (page.getOrders() != null && !page.getOrders().isEmpty()) {
            order = page.getOrders().get(0).getColumn();
            asc = page.getOrders().get(0).isAsc();
        }
        if (!asc) {
            limitBldr.append(String.format("order by `%s` desc ", order));
        }
        else {
            limitBldr.append(String.format("order by `%s` asc ", order));
        }

        // 返回内容
        Map<String, Object> retMap = new HashMap<>();

        try {
            // 查询列表
            String listSql = "select " + queryFieldStr + " "
                             + "from " + TaosConnector.prependDatabaseName(true, tableName) + " "
                             + (whereBldr.length() > 0 ? "where " + whereBldr.substring(4) : "") + " "
                             + limitBldr;
            List<PositionRawHistory> content = taosConnector.queryList(listSql, PositionRawHistory.class);
            retMap.put("content", content);

            return retMap;
        }
        catch (TaosDBException ex) {
            log.error("list error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list error: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 查询人员定位解算原始数据（按时间窗口分组）
     *
     * @param page          排序参数
     * @param queryCriteria 查询条件
     * @return 查询结果
     *
     * @throws BadRequestException 验证错误
     */
    @Override
    public Map queryIntervalList(
        Page<PositionRawHistory> page,
        PositionCalcQueryCriteria queryCriteria
    ) throws BadRequestException, SQLException
    {
        // 必须提供工作面ID或者工作面名称
        if (StringUtil.isEmpty(queryCriteria.getWorkFaceId())
            && StringUtil.isEmpty(queryCriteria.getWorkFaceName())
        ) {
            throw new BadRequestException("必须提供工作面ID或者工作面名称");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getStartDate())) {
                lStartTime = LocalDateTime.parse(queryCriteria.getStartDate(), inputDtf);
            }
            if (StringUtil.isNotEmpty(queryCriteria.getEndDate())) {
                lEndTime = LocalDateTime.parse(queryCriteria.getEndDate(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("查询时间格式错误");
        }

        LocalDateTime lDataStart = null;
        LocalDateTime lDataEnd = null;
        try {
            if (StringUtil.isNotEmpty(queryCriteria.getDataStart())) {
                lDataStart = LocalDateTime.parse(queryCriteria.getDataStart(), inputDtf);
            }
            if (StringUtil.isNotEmpty(queryCriteria.getDataEnd())) {
                lDataEnd = LocalDateTime.parse(queryCriteria.getDataEnd(), inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("数据时间格式错误");
        }

        // 查询时间范围、数据时间范围都未提供
        if ((lStartTime == null || lEndTime == null) && (lDataStart == null || lDataEnd == null)) {
            throw new BadRequestException("请至少完整提供查询时间范围或者数据时间范围");
        }
        // 查询时间范围、数据时间范围不能都提供
        if ((lStartTime != null || lEndTime != null) && (lDataStart != null || lDataEnd != null)) {
            throw new BadRequestException("查询时间范围和数据时间范围不能同时提供");
        }
        // 开始时间应早于结束时间
        if (lStartTime != null) {
            if (!lStartTime.isBefore(lEndTime)) {
                throw new BadRequestException("开始时间应早于结束时间");
            }
            if (lStartTime.plusHours(queryLimit).isBefore(lEndTime)) {
                throw new BadRequestException(String.format("查询时间范围不允许超过 %d 小时", queryLimit));
            }
        }
        // 数据开始时间应早于数据结束时间
        if (lDataStart != null) {
            if (!lDataStart.isBefore(lDataEnd)) {
                throw new BadRequestException("数据开始时间应早于数据结束时间");
            }
            if (lDataStart.plusHours(queryLimit).isBefore(lDataEnd)) {
                throw new BadRequestException(String.format("查询数据时间范围不允许超过 %d 小时", queryLimit));
            }
        }

        // 基站编码、标签卡号对应关系列表字符串, 基站1,标签卡1;基站2,标签卡2;
        Set<String[]> stationCardSet = new HashSet<>();
        if (StringUtil.isNotEmpty(queryCriteria.getStationCards())) {
            // throw new BadRequestException("暂不支持查询基站编码、标签卡号对应的查询方式");
            stationCardSet = Arrays.stream(queryCriteria.getStationCards().split(";"))
                                   .filter(StringUtil::isNotEmpty)
                                   .map(s -> s.split(","))
                                   .collect(Collectors.toSet());
            if (stationCardSet.stream().anyMatch(sc -> sc.length < 2)) {
                throw new BadRequestException("未提供完整的基站编码、标签卡号");
            }
        }

        // 填充模式
        if (StringUtil.isNotEmpty(queryCriteria.getFillMode())
            && !Arrays.asList("NONE", "VALUE", "PREV", "NULL", "LINEAR", "NEXT").contains(queryCriteria.getFillMode().toUpperCase())
        ) {
            throw new BadRequestException("填充模式不支持");
        }

        // 超级表信息
        TaosStableEntity stable = PositionRawHistory.buildTaosStableEntity();

        // 标签列查询条件
        StringBuilder tagQueryBldr = new StringBuilder();

        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            tagQueryBldr.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("' ");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            tagQueryBldr.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("' ");
        }
        // 工作面名称
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceName())) {
            tagQueryBldr.append("and `work_face_name` like '").append(queryCriteria.getWorkFaceName()).append("%' ");
        }
        // 基站编码、标签卡号
        if (!stationCardSet.isEmpty()) {
            String scWhereStr = stationCardSet
                .stream()
                .map(sca -> String.format("(`station_code` = '%s' and `card_number` = '%s')", sca[0], sca[1]))
                .collect(Collectors.joining(" or "));
            tagQueryBldr.append(String.format("and (%s)", scWhereStr));
        }
        // 基站编码
        if (StringUtil.isNotEmpty(queryCriteria.getStationCode())) {
            tagQueryBldr.append("and `station_code` = '").append(queryCriteria.getStationCode()).append("' ");
        }
        // 标签卡号
        if (StringUtil.isNotEmpty(queryCriteria.getCardNumber())) {
            tagQueryBldr.append("and `card_number` = '").append(queryCriteria.getCardNumber()).append("' ");
        }

        // 标签卡号未提供，提取所有存在的标签卡号
        String cardNumQuerySql = "select distinct mine_code, work_face_id, station_code, card_number "
                                 + "from " + TaosConnector.prependDatabaseName(stable.getName()) + " "
                                 + (
                                     tagQueryBldr.length() > 0
                                         ? "where " + tagQueryBldr.substring(4)
                                         : ""
                                 );
        log.info("tag query sql: {}", cardNumQuerySql);

        List<PositionRawHistoryTag> queryTags = null;
        try {
            queryTags = taosConnector.queryList(cardNumQuerySql, PositionRawHistoryTag.class);
        }
        catch (Exception ex) {
            log.error("tag query failed: {}", ex.getMessage(), ex);
            throw new BadRequestException("查询发生异常");
        }

        if (queryTags == null
            || queryTags.isEmpty()
            || queryTags.stream().noneMatch(
            t -> StringUtil.isNotEmpty(t.getCardNumber())
                 && StringUtil.isNotEmpty(t.getStationCode())
                 && StringUtil.isNotEmpty(t.getWorkFaceId())
        )) {
            throw new BadRequestException("没有符合条件的数据");
        }

        // 普通列查询条件
        StringBuilder fieldQueryBldr = new StringBuilder();

        // 是否断线
        if (StringUtil.isNotEmpty(queryCriteria.getOffline())) {
            fieldQueryBldr.append("and `offline` = '").append(queryCriteria.getOffline()).append("' ");
        }
        // 通道
        if (queryCriteria.getChannel() != null) {
            fieldQueryBldr.append("and `channel` = ").append(queryCriteria.getChannel()).append(" ");
        }
        // 距离范围低
        if (queryCriteria.getMinDistance() != null) {
            fieldQueryBldr.append("and `distance` >= ").append(queryCriteria.getMinDistance()).append(" ");
        }
        // 距离范围高
        if (queryCriteria.getMaxDistance() != null) {
            fieldQueryBldr.append("and `distance` <= ").append(queryCriteria.getMaxDistance()).append(" ");
        }

        // 查询时间格式
        DateTimeFormatter queryDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        int timeMode = queryCriteria.getTimeMode() == null
            ? 2
            : queryCriteria.getTimeMode();
        // 提供了数据时间范围，查找对应的查询时间范围
        if (lDataStart != null) {
            StringBuilder tsQueryBldr = new StringBuilder();
            switch (timeMode) {
                case 0:
                    tsQueryBldr.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                    break;
                case 1:
                    tsQueryBldr.append("and `data_time` > '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                    break;
                case 3:
                    tsQueryBldr.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` <= '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                    break;
                case 2:
                default:
                    tsQueryBldr.append("and `data_time` >= '")
                               .append(queryDtf.format(lDataStart))
                               .append("' and `data_time` < '")
                               .append(queryDtf.format(lDataEnd))
                               .append("' ");
                    break;
            }

            // 查询列表
            String tsQuerySql = "select first(ts) as first_ts, last(ts) as last_ts from "
                                + TaosConnector.prependDatabaseName(stable.getName()) + " "
                                + (
                                    (tagQueryBldr.length() > 0 || fieldQueryBldr.length() > 0 || tsQueryBldr.length() > 0)
                                        ? "where " + (tagQueryBldr.append(fieldQueryBldr).append(tsQueryBldr)).substring(4)
                                        : ""
                                );
            log.info("ts query sql: {}", tsQuerySql);

            PositionRawQueryTimeRange tsObj = null;
            try {
                tsObj = taosConnector.queryOne(tsQuerySql, PositionRawQueryTimeRange.class);
                if (tsObj == null
                    || tsObj.getFirstTs() == null
                    || tsObj.getLastTs() == null
                    || !tsObj.getFirstTs().before(tsObj.getLastTs())
                ) {
                    throw new BadRequestException("没有符合条件的数据，没有对应的时间");
                }

                timeMode = 3;
                lStartTime = DateUtil.convertTimestampToLdt(tsObj.getFirstTs());
                lEndTime = DateUtil.convertTimestampToLdt(tsObj.getLastTs());
            }
            catch (BadRequestException bre) {
                throw bre;
            }
            catch (Exception ex) {
                log.error("ts query failed: {}", ex.getMessage(), ex);
                throw new BadRequestException("查询发生异常");
            }
        }

        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        switch (timeMode) {
            case 0:
                fieldQueryBldr.append("and `ts` > '")
                              .append(queryDtf.format(lStartTime))
                              .append("' and `ts` < '")
                              .append(queryDtf.format(lEndTime))
                              .append("' ");
                break;
            case 1:
                fieldQueryBldr.append("and `ts` > '")
                              .append(queryDtf.format(lStartTime))
                              .append("' and `ts` <= '")
                              .append(queryDtf.format(lEndTime))
                              .append("' ");
                break;
            case 3:
                fieldQueryBldr.append("and `ts` >= '")
                              .append(queryDtf.format(lStartTime))
                              .append("' and `ts` <= '")
                              .append(queryDtf.format(lEndTime))
                              .append("' ");
                break;
            case 2:
            default:
                fieldQueryBldr.append("and `ts` >= '")
                              .append(queryDtf.format(lStartTime))
                              .append("' and `ts` < '")
                              .append(queryDtf.format(lEndTime))
                              .append("' ");
                break;
        }

        // 查询条件
        StringBuilder limitBldr = new StringBuilder();

        // 排序字段
        String order = "ts";
        boolean asc = true;
        if (page.getOrders() != null && !page.getOrders().isEmpty()) {
            order = page.getOrders().get(0).getColumn();
            asc = page.getOrders().get(0).isAsc();
        }
        if (!asc) {
            limitBldr.append(String.format("order by `%s` desc ", order));
        }

        // 时间窗口
        int intervalSec = queryCriteria.getIntervalSec() == null ? 90 : queryCriteria.getIntervalSec();
        // 滑动窗口
        int slidingSec = queryCriteria.getSlidingSec() == null ? 60 : queryCriteria.getSlidingSec();
        // 填充模式
        String fillMode = StringUtil.isEmpty(queryCriteria.getFillMode()) ? "null" : queryCriteria.getFillMode();

        // 查询字段
        // region UNION ALL 查询代码
        // String queryFieldStr = "first(`ts`) as `update_time`, first(`x`) as `x`, first(`y`) as `y`, "
        //                        + "first(`power_value`) as `power_value`, first(`signal_value`) as `signal_value`, "
        //                        + "first(`offline`) as `offline`, first(`channel`) as `channel`, first(`distance`) as `distance`, "
        //                        + "first(`data_time`) as `data_time`, first(`mq_info`) as `mq_info`, "
        //                        + "'[CARD_NUMBER]' as `card_number`, '[STATION_CODE]' as `station_code`, '[WORK_FACE_ID]' as `work_face_id` ";
        // endregion UNION ALL 查询代码
        String queryFieldStr = "first(`ts`) as `ts`, first(`ts`) as `update_time`, first(`x`) as `x`, first(`y`) as `y`, "
                               + "first(`power_value`) as `power_value`, first(`signal_value`) as `signal_value`, "
                               + "first(`offline`) as `offline`, first(`channel`) as `channel`, first(`distance`) as `distance`, "
                               + "first(`data_time`) as `data_time`, first(`mq_info`) as `mq_info`, first(`updated_at`) as `updated_at` ";

        Map<String, Object> retMap = new HashMap<>();
        long qs = System.currentTimeMillis();

        try {
            // region UNION ALL 查询代码
            // // 查询列表
            // String listSqlTpl = "select " + queryFieldStr + " "
            //                     + "from " + TaosConnector.prependDatabaseName(true, "[TABLE_NAME]") + " "
            //                     + (
            //                             fieldQueryBldr.length() > 0
            //                                     ? "where " + fieldQueryBldr.substring(4)
            //                                     : ""
            //                     ) + " "
            //                     + "INTERVAL(" + intervalSec + "s) SLIDING(" + slidingSec + "s) FILL(" + fillMode + ") "
            //                     + limitBldr;
            //
            // List<String> sqlList = new ArrayList<>();
            // for (PositionRawHistoryTag tag : queryTags) {
            //     if (StringUtil.isEmpty(tag.getCardNumber())) {
            //         log.warn("list query warn, card_number is empty, work_face_id: {}", tag.getWorkFaceId());
            //         continue;
            //     }
            //     if (StringUtil.isEmpty(tag.getStationCode())) {
            //         log.warn("list query warn, station_code is empty, work_face_id: {}", tag.getWorkFaceId());
            //         continue;
            //     }
            //     sqlList.add(
            //             listSqlTpl.replace("[TABLE_NAME]", tag.getTableName(stable.getName()))
            //                       .replace("[WORK_FACE_ID]", tag.getWorkFaceId())
            //                       .replace("[STATION_CODE]", tag.getStationCode())
            //                       .replace("[CARD_NUMBER]", tag.getCardNumber())
            //     );
            // }
            //
            // String contentSql = String.join(" UNION ALL ", sqlList);
            // log.info("list query sql: " + contentSql);
            //
            // List<PositionRawHistoryInterval> content = taosConnector.queryList(
            //         contentSql,
            //         PositionRawHistoryInterval.class
            // );
            // endregion UNION ALL 查询代码

            // 查询列表
            String listSqlTpl = "select " + queryFieldStr + " "
                                + "from " + TaosConnector.prependDatabaseName(true, "[TABLE_NAME]") + " "
                                + (
                                    fieldQueryBldr.length() > 0
                                        ? "where " + fieldQueryBldr.substring(4)
                                        : ""
                                ) + " "
                                + "INTERVAL(" + intervalSec + "s) SLIDING(" + slidingSec + "s) FILL(" + fillMode + ") "
                                + limitBldr;

            List<PositionRawHistoryInterval> content = new ArrayList<>();
            for (PositionRawHistoryTag tag : queryTags) {
                if (StringUtil.isEmpty(tag.getCardNumber())) {
                    log.warn("list query warn, card_number is empty, work_face_id: {}", tag.getWorkFaceId());
                    continue;
                }
                if (StringUtil.isEmpty(tag.getStationCode())) {
                    log.warn("list query warn, station_code is empty, work_face_id: {}", tag.getWorkFaceId());
                    continue;
                }

                String listSql = listSqlTpl.replace("[TABLE_NAME]", tag.getTableName(stable.getName()));
                log.info("list query sql: {}", listSql);
                List<PositionRawHistoryInterval> tagResult = taosConnector.queryList(
                    listSql,
                    PositionRawHistoryInterval.class
                );

                for (PositionRawHistoryInterval res : tagResult) {
                    res.setCardNumber(tag.getCardNumber());
                    res.setStationCode(tag.getStationCode());
                    res.setWorkFaceId(tag.getWorkFaceId());
                    content.add(res);
                }
            }

            // 时间序列
            List<Timestamp> timeList = content
                .stream()
                .filter(c -> c != null && c.getTs() != null)
                .map(PositionRawHistoryInterval::getTs)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

            List<Map<String, Object>> contentList = new ArrayList<>();
            for (Timestamp ts : timeList) {
                Map<String, Object> map = new HashMap<>();
                map.put("ts", ts);

                List<PositionRawHistoryInterval> tsList = content
                    .stream()
                    .filter(c -> ts.equals(c.getTs()))
                    .collect(Collectors.toList());
                // map.put("content", tsList);

                // 忽略标签卡不完整的时间窗口
                if (Objects.equals(queryCriteria.getIgnorePartial(), 1) && tsList.size() < queryTags.size()) {
                    map.put("sum", null);
                }
                else {
                    map.put(
                        "sum",
                        tsList.stream()
                              .map(PositionRawHistoryInterval::getDistance)
                              .reduce(
                                  0,
                                  (s, a) -> {
                                      return s + (a == null ? 0 : a);
                                  }
                              )
                    );
                }

                contentList.add(map);
            }

            retMap.put("content", content);
            retMap.put("stats", contentList);

            return retMap;
        }
        catch (TaosDBException ex) {
            log.error("list query error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("list query error: {}", e.getMessage(), e);
        }
        finally {
            log.info(
                "list query cost {} ms for queryTags: {}",
                (System.currentTimeMillis() - qs),
                JSON.toJSONString(queryTags)
            );
        }

        return retMap;
    }

    @Override
    public List<PositionRawHistory> queryLastPositionRawHistoryList(@RequestParam(required = true) List<String> workingFaceIds)
    {
        // 查询对应的表
        StringBuilder tblBuf = new StringBuilder();
        tblBuf.append("select tbname from ")
              .append(TaosConnector.prependDatabaseName(true, "pos_raw_history"));
        // // 工作面ID参数
        // if (CollectionUtils.isNotEmpty(workingFaceIds)) {
        //     tblBuf.append(" where ");
        //     tblBuf.append(
        //             workingFaceIds.stream()
        //                           .map(wfId -> "tbname like 'pos_raw_history_" + wfId + "_%'")
        //                           .collect(Collectors.joining(" or "))
        //     );
        // }

        List<String> tableNameList = new ArrayList<>();
        try {
            List<Map<String, Object>> resultList = taosConnector.query(tblBuf.toString());
            if (resultList != null) {
                for (Map<String, Object> map : resultList) {
                    String tblName = new String((byte[]) map.get("tbname"), StandardCharsets.UTF_8);
                    if (workingFaceIds.stream().anyMatch(id -> tblName.startsWith("pos_raw_history_" + id + "_"))) {
                        tableNameList.add(tblName);
                    }
                }
            }
        }
        catch (Exception e) {
            log.error("Query tables error: {}", e.getMessage(), e);
        }

        // 如果没有查询到表，直接返回
        if (tableNameList.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询语句

        // TDengine 中直接查询表格，不能返回标签名，使用 'xxxx' as tag 来替代解析查询结果又有编码问题
        // 所以使用 map 索引表名，解析查询结果是进行替换
        long tblIdx = 0L;
        Map<Long, String> wfIdMap = new HashMap<>();

        List<String> sqlList = new ArrayList<>();
        for (String tableName : tableNameList) {
            // String[] arr = tbl.split("_");
            // String wfId = arr.length > 3 ? arr[3] : null;
            // String stId = arr.length > 4 ? arr[4] : null;
            // String cdNum = arr.length > 5 ? arr[5] : null;
            Long fIdx = tblIdx++;
            wfIdMap.put(fIdx, tableName);

            sqlList.add(
                "select "
                // + "'" + wfId + "' as work_face_id, "
                // + "'" + stId + "' as station_code, "
                // + "'" + cdNum + "' as card_number, "
                + fIdx + " as idx, "
                + "last(`distance`) as distance, last(`channel`) as channel, last(`power_value`) as power_value, "
                + "last(`signal_value`) as signal_value, last(`offline`) as offline, last(`ts`) as ts, "
                + "last(`data_time`) as data_time, last(`updated_at`) as updated_at, last(`x`) as x, last(`y`) as y "
                + " from "
                + TaosConnector.prependDatabaseName(true, tableName)
            );
        }
        String sql = String.join(" union all ", sqlList);

        try {
            log.debug("queryLastPositionRawHistoryList sql: {}", sql);
            List<PositionRawHistory> records = generateLastPositionRawHistoryList(
                taosConnector.query(sql),
                wfIdMap
            );

            // 使用 Stream API 对列表进行分组
            Map<String, PositionRawHistory> groupedRecords = records
                .stream()
                .collect(Collectors.toMap(
                    p -> String.format(
                        "%s_%s_%s", p.getWorkFaceId(), p.getStationCode(), p.getCardNumber()),
                    p -> p,
                    (oldValue, newValue) -> (
                        newValue.getDataTime().after(oldValue.getDataTime()) ? newValue : oldValue
                    )
                ));

            // 对每个分组进行处理，保留最新时间记录
            return new ArrayList<>(groupedRecords.values());
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            throw new ServerRuntimeException("查询发生错误");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: {}", ex.getMessage(), ex);
            throw new ServerRuntimeException("查询发生错误");
        }
    }

    @Override
    public List<PositionRawHistory> queryLastPositionRawHistoryListUsingInQuery(@RequestParam(required = true) List<String> workingFaceIds)
    {
        // 查询语句
        StringBuilder buffer = new StringBuilder();
        buffer.append("select ")
              .append("last(`distance`) as distance, last(`channel`) as channel, ")
              .append("last(`power_value`) as power_value, last(`signal_value`) as signal_value, ")
              .append("last(`offline`) as offline, ")
              .append("last(`ts`) as ts, last(`data_time`) as data_time, last(`updated_at`) as updated_at, ")
              .append("last(`x`) as x, last(`y`) as y ")
              .append("from ")
              .append(TaosConnector.prependDatabaseName(true, "pos_raw_history"))
              .append(" ");

        // 工作面ID参数
        if (CollectionUtils.isNotEmpty(workingFaceIds)) {
            buffer.append("where `work_face_id` in ('").append(String.join("','", workingFaceIds)).append("') ");
        }

        buffer.append("group by `work_face_id`, `station_code`, `card_number` ");

        List<PositionRawHistory> historyList = new ArrayList<>();

        try {
            String sql = StringUtil.trimJoinedSql(buffer.toString());
            log.debug("queryLastPositionRawHistoryList sql: {}", sql);

            return generateLastPositionRawHistoryList(
                taosConnector.query(sql),
                null
            );
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            throw new ServerRuntimeException("查询发生错误");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: {}", ex.getMessage(), ex);
            throw new ServerRuntimeException("查询发生错误");
        }
    }

    /**
     * 取得最后同步的时间
     *
     * @param queryCriteria 查询条件
     * @return 得最后同步的时间
     */
    @Override
    public Timestamp getLatestSyncTime(PositionCalcQueryCriteria queryCriteria)
    {
        // 超级表信息
        TaosStableEntity stable = PositionRawHistory.buildTaosStableEntity();
        // 普通表名
        String tableName = stable.getName();
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            tableName = String.format("%s_%s", stable.getName(), queryCriteria.getWorkFaceId());
        }

        // 查询字段
        String queryFieldStr = "LAST(`ts`) as latestTs";

        // 查询条件
        StringBuilder whereBuffer = new StringBuilder();
        // 煤矿编码
        if (StringUtil.isNotEmpty(queryCriteria.getWorkFaceId())) {
            whereBuffer.append("and `work_face_id` = '").append(queryCriteria.getWorkFaceId()).append("'");
        }
        // 工作面ID
        if (StringUtil.isNotEmpty(queryCriteria.getMineCode())) {
            whereBuffer.append("and `mine_code` = '").append(queryCriteria.getMineCode()).append("'");
        }

        Timestamp latestTs = null;
        try {
            // 查询列表
            String listSql = "select " + queryFieldStr
                             + " from " + TaosConnector.prependDatabaseName(true, tableName) + (
                                 whereBuffer.length() > 0
                                     ? " where " + whereBuffer.substring(5)
                                     : ""
                             );

            latestTs = taosConnector.queryField(listSql);
        }
        catch (TaosDBException ex) {
            log.error("query latest time error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
        }
        catch (Exception e) {
            log.error("query latest time error: {}", e.getMessage(), e);
        }

        return latestTs;
    }

    /**
     * 写入解算原始数据
     *
     * @param historyObj 解算原始数据内容
     * @throws BadRequestException 输入验证错误
     */
    @Override
    public void create(PositionRawHistory historyObj) throws BadRequestException
    {
        List<String> errList = new ArrayList<>();
        if (historyObj.getUpdateTime() == null) {
            errList.add("未提供采集时间");
        }
        if (StringUtil.isEmpty(historyObj.getWorkFaceId())) {
            errList.add("未提供工作面ID");
        }
        if (StringUtil.isEmpty(historyObj.getMineCode())) {
            errList.add("未提供煤矿编码");
        }

        if (!errList.isEmpty()) {
            throw new BadRequestException(String.join(",", errList));
        }

        // 插入表内容
        TaosTableEntity table = historyObj.buildTaosTableEntity();

        try {
            taosConnector.insert(table);
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 批量写入解算原始数据
     *
     * @param historyList 解算原始数据内容列表
     * @throws BadRequestException 输入验证错误
     */
    @Override
    public void bulkCreate(List<PositionRawHistory> historyList) throws BadRequestException, TaosDBException
    {
        List<String> errSet = new ArrayList<>();
        historyList.forEach(historyObj -> {
            if (historyObj.getUpdateTime() == null) {
                errSet.add("未提供采集时间");
            }
            if (StringUtil.isEmpty(historyObj.getWorkFaceId())) {
                errSet.add("未提供工作面ID");
            }
            if (StringUtil.isEmpty(historyObj.getMineCode())) {
                errSet.add("未提供煤矿编码");
            }
        });

        if (!errSet.isEmpty()) {
            throw new BadRequestException(String.join(",", errSet));
        }

        List<TaosTableEntity> insTableList = new ArrayList<>();
        historyList.forEach(historyObj -> {
            insTableList.add(historyObj.buildTaosTableEntity());
        });

        try {
            if (!insTableList.isEmpty()) {
                taosConnector.insert(insTableList);
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw ex;
        }
    }

    /**
     * 推送天线反装报警到预警报警服务
     *
     * @param wrongChannelList     天线反装信息列表
     * @param shouldCheckWfMap     工作面定义Map
     * @param wrongChannelStateMap 反装天线分析明细Map
     * @param syncQueryCriteria    数据同步参数
     * @param actionStr            同步操作源
     * @param mockMode             模拟模式
     */
    private List<JSONObject> pushWrongChannelWarnToPushMsg(
        List<WrongChannelCheckDetail> wrongChannelList,
        Map<String, WorkingFaceProgressDefinition> shouldCheckWfMap,
        Map<String, List<WrongChannelCheckDetail>> wrongChannelStateMap,
        PositionCalcQueryCriteria syncQueryCriteria,
        String actionStr,
        Boolean mockMode
    )
    {
        if (wrongChannelList.isEmpty()) {
            log.info("### push wrongChannelWarn to push-msg, size: 0 ###");
            return null;
        }

        // 消息输出时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 当前时间
        String nowStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        List<JSONObject> pushObjList = new ArrayList<>();
        Map<String, List<WrongChannelCheckDetail>> checkDetailMap = new HashMap<>();
        boolean pushSuccess = false;

        try {
            Set<String> workFaceStates = new HashSet<>();
            // Set<String> warnEndWfIds = new HashSet<>();

            // 循环反装报警信息列表 wrongChannelList 生成告警/解警信息
            for (WrongChannelCheckDetail checkDetail : wrongChannelList) {
                checkDetailMap.put(
                    checkDetail.getWorkingFaceId(),
                    wrongChannelStateMap.get(checkDetail.getWorkingFaceId())
                );

                WorkingFaceProgressDefinition wfpd = shouldCheckWfMap.get(checkDetail.getWorkingFaceId());
                JSONObject obj = new JSONObject();
                obj.put("mineCode", wfpd.getMineCode());
                obj.put("warningAlarmInfoUpdatetime", nowStr);
                obj.put("warningAlarmInfoCreatetime", nowStr);
                obj.put("warningAlarmId", wfpd.getChannelIndicatorId());

                // 告警消息
                if (checkDetail.getWrongState() == 1) {
                    obj.put(
                        "warnContent",
                        String.format(
                            "%s，天线数据异常，时间: %s",
                            wfpd.getWorkingFaceName(),
                            sdf.format(checkDetail.getStartTime())
                        )
                    );
                }
                else {
                    // 解警消息
                    obj.put(
                        "warnContent", String.format(
                            "%s，天线数据正常，时间: %s",
                            wfpd.getWorkingFaceName(),
                            sdf.format(checkDetail.getStartTime())
                        )
                    );
                    // warnEndWfIds.add(wfpd.getWorkingFaceId());
                }
                // 告警状态
                obj.put("status", checkDetail.getWrongState());

                // 添加推送告警
                pushObjList.add(obj);
                // 记录推送的工作面ID和状态，打印日志用
                workFaceStates.add(String.format("%s:%d", wfpd.getWorkingFaceId(), checkDetail.getWrongState()));
            }

            // 推送子系统在线状态到预警报警分析服务
            if (!Boolean.TRUE.equals(mockMode)) {
                mqSendUtil.send(
                    RabbitTopicConfig.SYSTEM_ALIVE_PUSH_EXCHANGE,
                    RabbitTopicConfig.SYSTEM_ALIVE_PUSH_ROUTING_KEY,
                    pushObjList
                );

                log.info(
                    "### push wrongChannelWarn to push-msg, size: {}, content(wfId:state): {} ###",
                    pushObjList.size(),
                    String.join("|", workFaceStates)
                );
            }
            else {
                log.info(
                    "### [MOCK] push wrongChannelWarn to push-msg, size: {}, content(wfId:state): {} ###",
                    pushObjList.size(),
                    String.join("|", workFaceStates)
                );
            }
            pushSuccess = true;
        }
        catch (Exception ex) {
            log.warn("push wrongChannelWarn to push-msg failed: {}", ex.getMessage(), ex);
        }

        try {
            Map<String, Object> memoMap = new HashMap<>();
            memoMap.put(
                "pushSuccess", (
                    !Boolean.TRUE.equals(mockMode)
                        ? pushSuccess
                        : "[mock]"
                )
            );
            memoMap.put("actionStr", actionStr);
            memoMap.put("syncQueryCriteria", syncQueryCriteria);
            memoMap.put("checkDetailMap", checkDetailMap);

            AutoAlarmMsgs backupMsg = new AutoAlarmMsgs(
                "wrongChannelWarn",
                JSON.toJSONString(pushObjList),
                JSON.toJSONString(memoMap, SerializerFeature.WriteDateUseDateFormat)
            );
            autoAlarmMsgsService.save(backupMsg);
        }
        catch (Exception e) {
            log.error("save wrongChannelWarn to autoAlarmMsgs failed: {}", e.getMessage(), e);
        }

        return pushObjList;
    }

    /**
     * 组装工作面最新原始数据列表
     *
     * @param resultList 查询结果
     * @return /
     */
    private List<PositionRawHistory> generateLastPositionRawHistoryList(List<Map<String, Object>> resultList, Map<Long, String> wfIdIdxMap) {
        if (resultList == null || resultList.isEmpty()) {
            return new ArrayList<>();
        }

        List<PositionRawHistory> historyList = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            PositionRawHistory history = new PositionRawHistory();
            if (wfIdIdxMap != null) {
                // history.setWorkFaceId(new String((byte[]) map.get("work_face_id"), StandardCharsets.UTF_8));
                // history.setStationCode(new String((byte[]) map.get("station_code"), StandardCharsets.UTF_8));
                // history.setCardNumber(new String((byte[]) map.get("card_number"), StandardCharsets.UTF_8));
                if (wfIdIdxMap.containsKey(MapUtils.getLong(map, "idx", null))) {
                    String tbl = wfIdIdxMap.get(MapUtils.getLong(map, "idx", null));
                    String[] arr = tbl.split("_");
                    history.setWorkFaceId(arr.length > 3 ? arr[3] : null);
                    history.setStationCode(arr.length > 4 ? arr[4] : null);
                    history.setCardNumber(arr.length > 5 ? arr[5] : null);
                }
            }
            else {
                history.setWorkFaceId(MapUtils.getString(map, "work_face_id", null));
                history.setStationCode(MapUtils.getString(map, "station_code", null));
                history.setCardNumber(MapUtils.getString(map, "card_number", null));
            }

            history.setDistance(MapUtils.getInteger(map, "distance", null));
            history.setChannel(MapUtils.getInteger(map, "channel", null));
            history.setPowerValue(MapUtils.getInteger(map, "power_value", null));
            history.setSignalValue(MapUtils.getInteger(map, "signal_value", null));
            history.setOffline(MapUtils.getString(map, "offline", null));

            String lastTs = MapUtils.getString(map, "ts", null);
            if (lastTs != null) {
                history.setUpdateTime(Timestamp.valueOf(lastTs));
            }
            String lastDataTime = MapUtils.getString(map, "data_time", null);
            if (lastDataTime != null) {
                history.setDataTime(Timestamp.valueOf(lastDataTime));
            }
            String lastUpdatedAt = MapUtils.getString(map, "updated_at", null);
            if (lastUpdatedAt != null) {
                history.setUpdatedAt(Timestamp.valueOf(lastUpdatedAt));
            }

            Object x = map.get("x");
            if (x != null) {
                history.setX(new String((byte[]) x, StandardCharsets.UTF_8));
            }
            Object y = map.get("y");
            if (y != null) {
                history.setY(new String((byte[]) y, StandardCharsets.UTF_8));
            }

            // history.setMineCode(MapUtils.getString(map, "mine_code", null));
            // history.setWorkFaceName(MapUtils.getString(map, "work_face_name", null));
            // history.setMqInfo(MapUtils.getString(map, "last(mq_info)", null));

            historyList.add(history);
        }

        return historyList;
    }
}
