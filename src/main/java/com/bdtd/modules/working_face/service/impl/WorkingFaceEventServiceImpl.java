package com.bdtd.modules.working_face.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.working_face.dao.WorkFaceEventMapper;
import com.bdtd.modules.working_face.entity.WorkingFaceEvent;
import com.bdtd.modules.working_face.service.IWorkingFaceEventService;
import com.bdtd.util.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Slf4j
@Service
public class WorkingFaceEventServiceImpl extends ServiceImpl<WorkFaceEventMapper, WorkingFaceEvent> implements IWorkingFaceEventService
{

    private final WorkFaceEventMapper workFaceEventMapper;

    public WorkingFaceEventServiceImpl(WorkFaceEventMapper workFaceEventMapper)
    {
        this.workFaceEventMapper = workFaceEventMapper;
    }

    @Override
    public List<WorkingFaceEvent> selectWorkFaceEventList(
            String workFaceId, String workFaceType, String evenType, String recordType, String startTime, String endTime
    ) throws Exception
    {
        if (workFaceId == null ^ workFaceType == null) {
            log.warn("查询指定工作面事件时，workFaceId与workFaceType未同时提供");
            throw new BadRequestException("查询指定工作面事件时，workFaceId与workFaceType需同时提供");
        }

        if (startTime != null || endTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                dateFormat.parse(startTime);
                dateFormat.parse(endTime);
            }
            catch (ParseException e) {
                log.warn("日期格式不符合标准格式");
                throw new BadRequestException("日期格式不符合标准格式");
            }
        }

        return workFaceEventMapper.selectWorkFaceEventList(workFaceId, workFaceType, evenType, recordType, startTime, endTime);
    }

    @Override
    public IPage<WorkingFaceEvent> selectWorkFaceEventPage(
            String workFaceId, String workFaceType, String evenType, String recordType, String startTime, String endTime, Integer pageSize, Integer pageIndex
    ) throws Exception
    {
        if (workFaceId == null ^ workFaceType == null) {
            log.warn("查询指定工作面事件时，workFaceId与workFaceType未同时提供");
            throw new BadRequestException("查询指定工作面事件时，workFaceId与workFaceType需同时提供");
        }
        if (startTime != null || endTime != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                dateFormat.parse(startTime);
                dateFormat.parse(endTime);
            }
            catch (ParseException e) {
                log.warn("日期格式不符合标准格式");
                throw new BadRequestException("日期格式不符合标准格式");
            }
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        if (pageIndex == null) {
            pageIndex = 1;
        }
        Page<WorkingFaceEvent> page = new Page(pageIndex, pageSize);

        return workFaceEventMapper.selectWorkFaceEventPage(page, workFaceId, workFaceType, evenType, recordType, startTime, endTime);
    }

    @Override
    public int createRecord(List<WorkingFaceEvent> workingFaceEventList) throws Exception
    {
        // 插入更新数据
        List<WorkingFaceEvent> upsertLists = new ArrayList<>();

        for (WorkingFaceEvent workingFaceEvent : workingFaceEventList) {
            if (workingFaceEvent.getWorkFaceId() == null) {
                throw new BadRequestException("工作面ID不能为空");
            }
            if (workingFaceEvent.getWorkFaceType() == null) {
                throw new BadRequestException("工作面类型不能为空");
            }

            // 事件类型，无结束时间，可直接入库
            if ("event".equals(workingFaceEvent.getRecordType())) {
                upsertLists.add(workingFaceEvent);
            }
            else {
                // 状态类型，带结束时间，当状态变化时，需更新库中记录
                // 查询当前工作面、同一事件当前状态
                QueryWrapper<WorkingFaceEvent> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("work_face_id", workingFaceEvent.getWorkFaceId())
                            .eq("work_face_type", workingFaceEvent.getWorkFaceType())
                            .eq("event_type", workingFaceEvent.getEventType())
                            .le("begin_time", workingFaceEvent.getBeginTime())
                            .isNull("end_time")
                            .orderByDesc("begin_time")
                            .last("limit 1");

                WorkingFaceEvent latestRecord = workFaceEventMapper.selectOne(queryWrapper);

                // 事件状态未发生变化
                if (latestRecord != null && latestRecord.getValue().equals(workingFaceEvent.getValue())) {
                    continue;
                }
                // 事件状态发生变化，更新最近记录的结束数据为当前时间开始时间前一秒
                if (latestRecord != null) {
                    latestRecord.setEndTime(Timestamp.valueOf(workingFaceEvent.getBeginTime().toLocalDateTime().minusSeconds(1)));
                    upsertLists.add(latestRecord);
                }

                upsertLists.add(workingFaceEvent);
            }
        }

        // 入库
        if (upsertLists.isEmpty()) {
            return 0;
        }

        int upsertCount = 0;
        try {
            upsertCount = workFaceEventMapper.saveOrUpdateBatch(upsertLists);
        }
        catch (Exception ex) {
            log.warn("postgres insert error: {}", ex.getMessage());
            throw ex;
        }

        return upsertCount;
    }
}
