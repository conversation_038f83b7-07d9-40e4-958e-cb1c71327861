package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.dto.WorkingFaceWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.util.exception.BadRequestException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面进尺定义 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IWorkingFaceProgressDefinitionService extends IService<WorkingFaceProgressDefinition>
{

    /**
     * 分页查询
     *
     * @param page        分页参数
     * @param queryEntity 查询参数
     * @return /
     */
    Page<WorkingFaceProgressDefinition> selectPage(
            Page<WorkingFaceProgressDefinition> page,
            WorkingFaceProgressDefinition queryEntity
    );

    /**
     * 查询列表带实时值
     *
     * @param queryEntity 查询参数
     * @return /
     */
    List<Map<String, Object>> selectListWithValues(WorkingFaceProgressDefinition queryEntity);

    /**
     * 查询分页带实时值
     *
     * @param page        分页参数
     * @param queryEntity 查询参数
     * @return /
     */
    IPage<Map<String, Object>> selectPageWithValues(
            Page<WorkingFaceProgressDefinition> page,
            WorkingFaceProgressDefinition queryEntity
    );

    /**
     * 查询存在有效工作模式且启用接入的工作面
     *
     * @param workModes 查询的工作模式
     * @return /
     */
    List<WorkingFaceProgressDefinition> selectWorkModelAssignedList(List<String> workModes);

    /**
     * 查询工作模式发生变更的工作面
     *
     * @return /
     */
    List<WorkingFaceProgressDefinition> selectWorkModelChangedList();

    /**
     * 批量插入更新
     *
     * @param insertList 插入列表
     */
    void upsertBatch(List<WorkingFaceProgressDefinition> insertList);

    /**
     * 同步工作面相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置
     * @return /
     */
    boolean submitIndicatorRule(List<WorkingFaceWarnIndicatorSubmitDto> submitDtoList) throws BadRequestException;

}
