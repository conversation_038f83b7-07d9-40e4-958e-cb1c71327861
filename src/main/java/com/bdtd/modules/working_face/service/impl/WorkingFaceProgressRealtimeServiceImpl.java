package com.bdtd.modules.working_face.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressDefinitionMapper;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressRealtimeMapper;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面进尺实时监测 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
@Slf4j
public class WorkingFaceProgressRealtimeServiceImpl extends ServiceImpl<WorkingFaceProgressRealtimeMapper, WorkingFaceProgressRealtime>
    implements IWorkingFaceProgressRealtimeService
{
    @Value("${scheduled.progress-realtime-global-check-threshold:5}")
    private Integer globalCheckThreshold;

    private final WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper;
    private final WorkingFaceProgressRealtimeMapper workingFaceProgressRealtimeMapper;
    private final TaosConnector taosConnector;

    public WorkingFaceProgressRealtimeServiceImpl(
        WorkingFaceProgressDefinitionMapper workingFaceProgressDefinitionMapper,
        WorkingFaceProgressRealtimeMapper workingFaceProgressRealtimeMapper,
        TaosConnector taosConnector
    ) {
        this.workingFaceProgressDefinitionMapper = workingFaceProgressDefinitionMapper;
        this.workingFaceProgressRealtimeMapper = workingFaceProgressRealtimeMapper;
        this.taosConnector = taosConnector;
    }

    @Override
    public Page<WorkingFaceProgressRealtime> selectPage(Page page, WorkingFaceProgressRealtime queryEntity) {
        QueryWrapper<WorkingFaceProgressRealtime> query = Wrappers.query(queryEntity);
        return workingFaceProgressRealtimeMapper.selectPage(page, query);
    }

    @Override
    public List<Map<String, Object>> selectHistoryValues(
        @RequestParam(required = true) String workingFaceId,
        String startTime,
        String endTime,
        String polymerize,
        Integer timeMode
    ) throws Exception {
        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            throw new BadRequestException("no time range, no content");
        }

        DateTimeFormatter inputDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;
        try {
            if (StringUtil.isNotEmpty(startTime)) {
                lStartTime = LocalDateTime.parse(startTime, inputDtf);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime, inputDtf);
            }
        }
        catch (Exception e) {
            throw new BadRequestException("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize, true)) {
            throw new BadRequestException("聚合度无效");
        }

        // 工作面ID 是否存在 
        if (StringUtil.isNotEmpty(workingFaceId)) {
            QueryWrapper<WorkingFaceProgressDefinition> queryWrap = new QueryWrapper<>();
            queryWrap.eq("working_face_id", workingFaceId);
            int existedCount = workingFaceProgressDefinitionMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                throw new BadRequestException("查询的工作面不存在");
            }
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 查询语句
        StringBuffer buffer = new StringBuffer();
        buffer.append("select").append(" ");

        // 根据聚合度参数调整查询字段
        if (StringUtil.isEmpty(polymerize)) {
            buffer.append(
                "`time`, "
                + "`working_face_id` as workingFaceId, "
                + "`machine_pos_x` as `machinePosX`, "
                + "`machine_pos_y` as `machinePosY`, "
                + "`trans_pos_x` as `transPosX`, "
                + "`trans_pos_y` as `transPosY`, "
                + "`air_pos_x` as `airPosX`, "
                + "`air_pos_y` as `airPosY`, "
                + "`average_speed` as `averageSpeed`, "
                + "`sequence_number` as `sequenceNumber`, "
                + "`footage` as `footage`, "
                + "`alarm_distance` as `alarmDistance`, "
                + "`compensation` as `compensation`, "
                + "`source_flag` as `sourceFlag` "
            );
        }
        else {
            buffer.append(
                "`working_face_id` as workingFaceId, "
                + "last(`machine_pos_x`) as `machinePosX`, "
                + "last(`machine_pos_y`) as `machinePosY`, "
                + "last(`trans_pos_x`) as `transPosX`, "
                + "last(`trans_pos_y`) as `transPosY`, "
                + "last(`air_pos_x`) as `airPosX`, "
                + "last(`air_pos_y`) as `airPosY`, "
                + "last(`average_speed`) as `averageSpeed`, "
                + "last(`sequence_number`) as `sequenceNumber`, "
                + "last(`footage`) as `footage`, "
                + "last(`alarm_distance`) as `alarmDistance`, "
                + "last(`compensation`) as `compensation`, "
                + "last(`source_flag`) as `sourceFlag` "
            );
        }
        buffer.append("from `wf_prgrss` ");

        // 时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
        boolean isValidTimeMode = true;
        switch (timeMode) {
            case 0:
                buffer.append("where `time` > '").append(dtf.format(lStartTime)).append("' and `time` < '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 1:
                buffer.append("where `time` > '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 2:
                buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` < '").append(dtf.format(lEndTime)).append("' ");
                break;
            case 3:
                buffer.append("where `time` >= '").append(dtf.format(lStartTime)).append("' and `time` <= '").append(dtf.format(lEndTime)).append("' ");
                break;
            default:
                isValidTimeMode = false;
                break;
        }
        // 工作面ID参数
        if (StringUtil.isNotEmpty(workingFaceId)) {
            if (isValidTimeMode) {
                buffer.append("and `working_face_id` = '").append(workingFaceId).append("' ");
            }
            else {
                buffer.append("where `working_face_id` = '").append(workingFaceId).append("' ");
            }
        }
        // 聚合度
        if (StringUtil.isNotEmpty(polymerize)) {
            buffer.append("interval(").append(polymerize).append(") ");
            buffer.append("group by `working_face_id` ");
        }

        buffer.append("order by `time` ASC ");

        List<Map<String, Object>> mapList;
        // Map<String, List<Map<String, Object>>> retMap = new HashMap<>();

        try {
            String sql = StringUtil.trimJoinedSql(buffer.toString());
            mapList = taosConnector.query(sql);
            // if (mapList != null) {
            //     for (Map<String, Object> map : mapList) {
            //         String pId = (String) map.get("point_id");
            //         retMap.computeIfAbsent(pId, k -> new ArrayList<>());
            //
            //         List<Map<String, Object>> list = retMap.get(pId);
            //         list.add(map);
            //         retMap.put(pId, list);
            //     }
            // }
        }
        catch (TaosDBException ex) {
            log.error("selectHistoryValues error: {}, SQL: {}", ex.getMessage(), ex.getExecuteSql(), ex);
            throw new Exception("tsdb query error");
        }
        catch (Exception ex) {
            log.error("selectHistoryValues error: {}", ex.getMessage(), ex);
            throw new Exception("tsdb query error");
        }

        // return retMap.values().stream().collect(Collectors.toList());
        return mapList;
    }

    /**
     * 取得工作面最新的数据时间
     *
     * @param companyCode 二级公司编码
     * @param mineCode    煤矿编码
     * @param workfaceIds 工作面编码
     * @return 最后同步的时间
     */
    @Override
    public List<WorkingFaceProgressRealtime> getLatestRealtimeList(String companyCode, String mineCode, List<String> workfaceIds) {
        QueryWrapper<WorkingFaceProgressRealtime> queryWrap = new QueryWrapper<>();
        queryWrap.eq(StringUtil.isNotEmpty(companyCode), "group_code", companyCode);
        queryWrap.eq(StringUtil.isNotEmpty(mineCode), "mine_code", mineCode);
        queryWrap.in(workfaceIds != null && !workfaceIds.isEmpty(), "working_face_id", workfaceIds);
        return workingFaceProgressRealtimeMapper.getLatestRealtimeList(queryWrap);
    }

    /**
     * 取得最新的数据时间
     *
     * @param mineCode 煤矿编码
     * @return 得最后同步的时间
     */
    @Override
    public Timestamp getAllLatestDataTime(String mineCode) {
        QueryWrapper<WorkingFaceProgressRealtime> queryWrap = new QueryWrapper<>();
        queryWrap.eq(StringUtil.isNotEmpty(mineCode), "mine_code", mineCode);
        return workingFaceProgressRealtimeMapper.getAllLatestDataTime(queryWrap);
    }

    /**
     * 进尺测距数据采集全局在线检查
     *
     * @return 是否正常，true 正常 false可能断线
     */
    @Override
    public boolean doRealtimeGlobalCheck() {
        try {
            // 获取最新的更新时间
            Timestamp latestTs = getAllLatestDataTime(null);
            LocalDateTime latestLdt = latestTs
                .toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();

            // 当前时间
            LocalDateTime nowLdt = LocalDateTime.now();
            // 最后同步时间+阈值 在 当前时间 之后，没有断线
            if (latestLdt.plusMinutes(globalCheckThreshold).isAfter(nowLdt)) {
                log.info("progressRealtimeGlobalCheck check ends, everything is OK.");
                return true;
            }

            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 记录日志
            log.error(
                "!!! progressRealtimeGlobalCheck result need attention, LAST UPDATE TIME: {}, CHECK TIME: {}, CHECK THRESHOLD: {} min !!!",
                dtf.format(latestLdt),
                dtf.format(nowLdt),
                globalCheckThreshold
            );
        }
        catch (Exception ex) {
            log.error("progressRealtimeGlobalCheck failed: {}", ex.getMessage(), ex);
        }

        return false;
    }

}
