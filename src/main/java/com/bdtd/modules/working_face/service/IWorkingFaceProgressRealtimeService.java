package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面进尺实时监测 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
public interface IWorkingFaceProgressRealtimeService extends IService<WorkingFaceProgressRealtime>
{

    /**
     * 分页查询
     *
     * @param page        分页参数
     * @param queryEntity 查询参数
     * @return /
     */
    Page<WorkingFaceProgressRealtime> selectPage(Page<WorkingFaceProgressRealtime> page, WorkingFaceProgressRealtime queryEntity);

    /**
     * 查询点位历史值列表
     *
     * @param workingFaceId 工作面ID
     * @param startTime     开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime       结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize    采样率, 格式: 1s 或 1m
     * @param timeMode      时间窗口查询模式, 0开区间,1左半开（默认）,2右半开,3闭区间
     * @return /
     */
    List<Map<String, Object>> selectHistoryValues(String workingFaceId, String startTime, String endTime, String polymerize, Integer timeMode) throws Exception;

    /**
     * 取得工作面最新的数据时间
     *
     * @param companyCode 二级公司编码
     * @param mineCode    煤矿编码
     * @param workfaceIds 工作面编码
     * @return 最后同步的时间
     */
    List<WorkingFaceProgressRealtime> getLatestRealtimeList(String companyCode, String mineCode, List<String> workfaceIds);

    /**
     * 取得最新的数据时间
     *
     * @param mineCode 煤矿编码
     * @return 最后同步的时间
     */
    Timestamp getAllLatestDataTime(String mineCode);

    /**
     * 进尺测距数据采集全局在线检查
     *
     * @return 是否正常，true 正常 false可能断线
     */
    boolean doRealtimeGlobalCheck();

}
