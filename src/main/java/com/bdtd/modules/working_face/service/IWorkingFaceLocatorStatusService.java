package com.bdtd.modules.working_face.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.working_face.dto.WorkingFaceLocatorStatusComposite;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus;
import com.bdtd.util.exception.BadRequestException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作面监测信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
public interface IWorkingFaceLocatorStatusService extends IService<WorkingFaceLocatorStatus>
{

    /**
     * 分页查询
     *
     * @param queryEntity 查询参数
     * @param page        分页参数
     * @return /
     */
    Page<WorkingFaceLocatorStatus> selectPage(WorkingFaceLocatorStatus queryEntity, Page<WorkingFaceLocatorStatus> page);

    /**
     * 监测信息汇总分页查询
     *
     * @param queryEntity 查询参数
     * @param page        分页参数
     * @return /
     */
    IPage<WorkingFaceLocatorStatusComposite> selectCompositedPage(WorkingFaceLocatorStatus queryEntity, Page<WorkingFaceLocatorStatusComposite> page)
            throws BadRequestException;

    /**
     * 查询点位历史值列表
     *
     * @param pointId    点位ID
     * @param startTime  开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime    结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize 采样率, 格式: 1s 或 1m
     * @param timeMode   时间窗口查询模式, 0开区间,1左半开（默认）,2右半开,3闭区间
     * @return /
     */
    List<Map<String, Object>> selectHistoryValues(String pointId, String startTime, String endTime, String polymerize, Integer timeMode) throws Exception;

}
