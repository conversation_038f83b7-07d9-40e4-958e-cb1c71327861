package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.PositionCalcHistory;
import com.bdtd.modules.working_face.service.IPositionCalcHistoryService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 定位解算历史数据 控制器
 *
 * <AUTHOR>
 * {@code @date} 2022-04-09
 */
@Slf4j
@RestController
@RequestMapping({ "/workingFace/positionCalcHistory" })
@Api(value = "/workingFace/positionCalcHistory", tags = "工作面定位解算历史数据接口")
public class PositionCalcHistoryController
{
    @Autowired
    private IPositionCalcHistoryService positionCalcHistoryService;
    @Autowired
    IServiceCallLogService serviceCallLogService;

    @GetMapping("/paged")
    @ApiOperation(value = "定位解算历史数据分页接口", notes = "定位解算历史数据分页接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "page", value = "分页参数", dataType = "Page"),
            @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
    })
    public ResponseEntity<Msg> selectPositionCalcHistoryPage(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            Map<String, Object> result = positionCalcHistoryService.queryPositionCalcHistoryPage(page, queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/list")
    @ApiOperation(value = "定位解算历史数据列表接口", notes = "定位解算历史数据列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "page", value = "排序参数", dataType = "Page"),
            @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
    })
    public ResponseEntity<Msg> selectPositionCalcHistoryList(
            Page<PositionCalcHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            Map<String, Object> result = positionCalcHistoryService.queryPositionCalcHistoryList(page, queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/sync")
    @ApiOperation(value = "同步定位解算历史数据", notes = "同步定位解算历史数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "syncCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
    })
    public ResponseEntity<Msg> syncPositionCalcHistory(PositionCalcQueryCriteria syncCriteria)
            throws BadRequestException
    {
        if (StringUtil.isEmpty(syncCriteria.getStartDate()) || StringUtil.isEmpty(syncCriteria.getEndDate())) {
            return new ResponseEntity<>(ReturnMsg.fail("开始时间和结束时间不能不空"), HttpStatus.OK);
        }

        PositionCalcSyncResult result = null;
        try {
            result = positionCalcHistoryService.syncPositionCalcHistory(
                    syncCriteria, "通过接口同步定位解算历史数据"
            );

            return new ResponseEntity<>(ReturnMsg.success(result.getCount()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
        finally {
            try {
                // 记录服务日志
                if (result != null && result.getSyncLogs() != null && !result.getSyncLogs().isEmpty()) {
                    serviceCallLogService.createLogs(result.getSyncLogs());
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

    @GetMapping("/proxy")
    @ApiOperation(value = "定位解算历史数据代理接口", notes = "定位解算历史数据代理接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "syncCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
    })
    public ResponseEntity<Object> passPositionCalcHistory(PositionCalcQueryCriteria queryCriteria)
            throws BadRequestException
    {
        if (StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate())) {
            return new ResponseEntity<>(ReturnMsg.fail("开始时间和结束时间不能不空"), HttpStatus.OK);
        }

        try {
            Object result = positionCalcHistoryService.passPositionCalcHistory(queryCriteria);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

}
