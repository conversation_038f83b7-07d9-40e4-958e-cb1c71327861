package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.MineHeartbeatWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressMineHeartbeat;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressMineHeartbeatService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 矿端采集链路心跳数据 控制器
 *
 * <AUTHOR>
 * {@code @date} 2022-04-09
 */
@Slf4j
@RestController
@RequestMapping({ "/workingFace/mineHeartbeat" })
@Api(value = "/workingFace/mineHeartbeat", tags = "矿端采集链路心跳数据接口")
public class WorkingFaceProgressMineHeartbeatController {
    @Autowired
    private IWorkingFaceProgressMineHeartbeatService workingFaceProgressMineHeartbeatService;
    @Autowired
    IServiceCallLogService serviceCallLogService;

    @GetMapping("/paged")
    @ApiOperation(value = "矿端采集链路心跳数据分页接口", notes = "矿端采集链路心跳数据分页接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "page", value = "分页参数", dataType = "Page"),
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Msg> selectWorkingFaceProgressMineHeartbeatPage(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            IPage<WorkingFaceProgressMineHeartbeat> result = workingFaceProgressMineHeartbeatService.queryWorkingFaceProgressMineHeartbeatPage(
                    page,
                    queryCriteria
            );
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/list")
    @ApiOperation(value = "矿端采集链路心跳数据列表接口", notes = "矿端采集链路心跳数据列表接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "page", value = "排序参数", dataType = "Page"),
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Msg> selectWorkingFaceProgressMineHeartbeatList(
            Page<WorkingFaceProgressMineHeartbeat> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            List<WorkingFaceProgressMineHeartbeat> result = workingFaceProgressMineHeartbeatService.queryWorkingFaceProgressMineHeartbeatList(
                    page,
                    queryCriteria
            );
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/sync")
    @ApiOperation(value = "同步矿端采集链路心跳数据", notes = "同步矿端采集链路心跳数据")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "同步条件", dataType = "PositionCalcQueryCriteria"),
            }
    )
    public ResponseEntity<Msg> syncWorkingFaceProgressMineHeartbeat(PositionCalcQueryCriteria syncCriteria) throws BadRequestException
    {
        PositionCalcSyncResult result = null;
        try {
            result = workingFaceProgressMineHeartbeatService.syncPositionMineHeartbeat(
                    syncCriteria,
                    "通过接口同步矿端采集链路心跳数据"
            );

            return new ResponseEntity<>(ReturnMsg.success(result.getCount()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
        finally {
            try {
                // 记录服务日志
                if (result != null && result.getSyncLogs() != null && !result.getSyncLogs().isEmpty()) {
                    serviceCallLogService.createLogs(result.getSyncLogs());
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

    @GetMapping("/proxy")
    @ApiOperation(value = "矿端采集链路心跳数据代理接口", notes = "矿端采集链路心跳数据代理接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Object> passPositionRawHistory(PositionCalcQueryCriteria queryCriteria)
            throws BadRequestException
    {
        try {
            Object result = workingFaceProgressMineHeartbeatService.passPositionMineHeartbeat(queryCriteria);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    /**
     * 同步矿端采集链路相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置
     * @return /
     */
    @ApiOperation(value = "同步矿端采集链路相关预警报警指标配置", notes = "同步矿端采集链路相关预警报警指标配置")
    @PostMapping("/indicatorSubmit")
    public ResponseEntity<Msg> submitIndicatorAliveRule(
            @RequestBody List<MineHeartbeatWarnIndicatorSubmitDto> submitDtoList
    )
    {
        Msg msg;
        try {
            boolean b = workingFaceProgressMineHeartbeatService.submitIndicatorRule(submitDtoList);
            msg = ReturnMsg.success(b);
        }
        catch (BadRequestException bre) {
            msg = ReturnMsg.fail(bre.getMessage());
        }
        catch (Exception e) {
            msg = ReturnMsg.fail("配置保存错误");
            log.error("submitIndicatorAliveRule for mineHeartbeatWarn, Exception: {}", e.getMessage(), e);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
