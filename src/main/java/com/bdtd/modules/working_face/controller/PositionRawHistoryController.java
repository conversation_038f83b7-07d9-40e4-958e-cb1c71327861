package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.modules.working_face.service.IPositionRawHistoryService;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressDefinitionService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.exception.ServerRuntimeException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定位解算原始数据 控制器
 *
 * <AUTHOR>
 * {@code @date} 2022-04-09
 */
@Slf4j
@RestController
@RequestMapping({ "/workingFace/positionRawHistory" })
@Api(value = "/workingFace/positionRawHistory", tags = "工作面定位解算原始数据接口")
public class PositionRawHistoryController {
    @Autowired
    private IPositionRawHistoryService positionRawHistoryService;
    @Autowired
    private IWorkingFaceProgressDefinitionService workingFaceProgressDefinitionService;
    @Autowired
    IServiceCallLogService serviceCallLogService;

    @GetMapping("/paged")
    @ApiOperation(value = "定位解算原始数据分页接口", notes = "定位解算原始数据分页接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "page", value = "分页参数", dataType = "Page"),
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Msg> selectPositionRawHistoryPage(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            Map<String, Object> result = positionRawHistoryService.queryPositionRawHistoryPage(page, queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/list")
    @ApiOperation(value = "定位解算原始数据列表接口", notes = "定位解算原始数据列表接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "page", value = "排序参数", dataType = "Page"),
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Msg> selectPositionRawHistoryList(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            Map<String, Object> result = positionRawHistoryService.queryPositionRawHistoryList(page, queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @GetMapping("/intervalList")
    @ApiOperation(value = "定位解算原始数据按时间窗口分组列表接口", notes = "定位解算原始数据按时间窗口分组列表接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "page", value = "排序参数", dataType = "Page"),
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Msg> selectIntervalList(
            Page<PositionRawHistory> page,
            PositionCalcQueryCriteria queryCriteria
    )
    {
        try {
            Map result = positionRawHistoryService.queryIntervalList(page, queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (BadRequestException ex) {
            return new ResponseEntity<>(ReturnMsg.fail(ex.getMessage()), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "定位解算原始数据最新值列表接口", notes = "定位解算原始数据最新值列表接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "groupCode", value = "集团编码", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "mineCode", value = "煤矿编码", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "workingFaceId", value = "工作面ID", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "workingFaceType", value = "工作面类型", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "workingFaceName", value = "工作面名称", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "deletedAtIsNull", value = "是否删除", dataType = "Integer")
            }
    )
    @GetMapping("/last")
    public ResponseEntity<Msg> selectPositionRawHistoryLastList(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String workingFaceName,
            Integer deletedAtIsNull,
            String queryMethod
    )
    {
        Msg msg;
        List<WorkingFaceProgressDefinition> listResult;

        try {
            WorkingFaceProgressDefinition queryEntity = new WorkingFaceProgressDefinition();
            if (StringUtil.isNotEmpty(groupCode)) {
                queryEntity.setGroupCode(groupCode);
            }
            if (StringUtil.isNotEmpty(mineCode)) {
                queryEntity.setMineCode(mineCode);
            }
            if (StringUtil.isNotEmpty(workingFaceId)) {
                queryEntity.setWorkingFaceId(workingFaceId);
            }
            if (StringUtil.isNotEmpty(workingFaceType)) {
                queryEntity.setWorkingFaceType(workingFaceType);
            }
            if (StringUtil.isNotEmpty(workingFaceName)) {
                queryEntity.setWorkingFaceName(workingFaceName);
            }

            log.info("selectPositionRawHistoryLastList, queryEntity: {}", queryEntity);
            QueryWrapper<WorkingFaceProgressDefinition> queryWrapper = Wrappers.query(queryEntity);
            // 默认返回未删除的
            queryWrapper
                    // 未删除
                    .isNull(deletedAtIsNull == null || deletedAtIsNull == 1, "deleted_at")
                    // 已删除
                    .isNotNull(deletedAtIsNull != null && deletedAtIsNull == 0, "deleted_at");

            listResult = workingFaceProgressDefinitionService.list(queryWrapper);
            if (listResult == null || listResult.isEmpty()) {
                msg = ReturnMsg.success(new ArrayList<>());
                return new ResponseEntity<>(msg, HttpStatus.OK);
            }

            // 工作面ID
            List<String> workingFaceIds = listResult
                    .stream()
                    .map(WorkingFaceProgressDefinition::getWorkingFaceId)
                    .distinct()
                    .collect(Collectors.toList());
            if (workingFaceIds.isEmpty()) {
                msg = ReturnMsg.success(new ArrayList<>());
                return new ResponseEntity<>(msg, HttpStatus.OK);
            }

            log.debug("selectPositionRawHistoryLastList, query workingFaceIds: {}", workingFaceIds);
            List<PositionRawHistory> result;
            // 默认使用 in 方式查询
            if (StringUtil.isEmpty(queryMethod) || "in".equalsIgnoreCase(queryMethod)) {
                result = positionRawHistoryService.queryLastPositionRawHistoryListUsingInQuery(workingFaceIds);
            }
            else {
                // 使用 union 方式查询
                result = positionRawHistoryService.queryLastPositionRawHistoryList(workingFaceIds);
            }
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (ServerRuntimeException e) {
            log.error("selectPositionRawHistoryLastList, {}", e.getMessage(), e);

            msg = ReturnMsg.fail(e.getMessage(), null);
            return new ResponseEntity<>(msg, HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("selectPositionRawHistoryLastList, {}", e.getMessage(), e);

            msg = ReturnMsg.fail("query error", null);
            return new ResponseEntity<>(msg, HttpStatus.OK);
        }
    }

    @GetMapping("/sync")
    @ApiOperation(value = "同步定位解算原始数据", notes = "同步定位解算原始数据")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "syncCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria"),
                    @ApiImplicitParam(paramType = "query", name = "source", value = "数据源,raw/calc", dataType = "String"),
                    @ApiImplicitParam(paramType = "query", name = "wrongChnAnal", value = "天线反装报警,true/false默认", dataType = "Boolean"),
                    @ApiImplicitParam(paramType = "query", name = "mock", value = "模拟模式,true默认/false,同步数据及关联数据不入库", dataType = "Boolean")
            }
    )
    public ResponseEntity<Msg> syncPositionRawHistory(
            PositionCalcQueryCriteria syncCriteria,
            @RequestParam(required = false, value = "source") String source,
            @RequestParam(required = false, value = "wrongChnAnal", defaultValue = "false") Boolean wrongChnAnal,
            @RequestParam(required = false, value = "mock", defaultValue = "true") Boolean mock
    ) throws BadRequestException
    {
        if (StringUtil.isEmpty(syncCriteria.getStartDate()) || StringUtil.isEmpty(syncCriteria.getEndDate())) {
            return new ResponseEntity<>(ReturnMsg.fail("开始时间和结束时间不能不空"), HttpStatus.OK);
        }

        PositionCalcSyncResult result = null;
        try {
            result = positionRawHistoryService.syncPositionRawHistory(
                    syncCriteria,
                    "通过接口同步定位解算原始数据数据",
                    source,
                    Boolean.TRUE.equals(wrongChnAnal),
                    Boolean.TRUE.equals(mock)
            );
            // 接口调用不返回日志
            if (result.getSyncLogs() != null) {
                result.setSyncLogs(null);
            }

            // 返回分析结果
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
        finally {
            try {
                // 记录服务日志
                if (result != null && result.getSyncLogs() != null && !result.getSyncLogs().isEmpty()) {
                    serviceCallLogService.createLogs(result.getSyncLogs());
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

    @GetMapping("/proxy")
    @ApiOperation(value = "定位解算原始数据代理接口", notes = "定位解算原始数据代理接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    public ResponseEntity<Object> passPositionRawHistory(
            PositionCalcQueryCriteria queryCriteria,
            @RequestParam(required = false, value = "source") String source
    )
    {
        if (StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate())) {
            return new ResponseEntity<>(ReturnMsg.fail("开始时间和结束时间不能不空"), HttpStatus.OK);
        }

        try {
            Object result = positionRawHistoryService.passPositionRawHistory(queryCriteria, source);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "修正原始数据接口", notes = "修正原始数据接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(paramType = "query", name = "queryCriteria", value = "查询参数", dataType = "PositionCalcQueryCriteria")
            }
    )
    @PostMapping("/modify")
    public ResponseEntity<Msg> modifyPositionRawHistory(@RequestBody PositionCalcQueryCriteria queryCriteria)
            throws BadRequestException
    {
        if (StringUtil.isEmpty(queryCriteria.getStartDate()) || StringUtil.isEmpty(queryCriteria.getEndDate())) {
            return new ResponseEntity<>(ReturnMsg.fail("开始时间和结束时间不能不空"), HttpStatus.OK);
        }
        if (StringUtil.isEmpty(queryCriteria.getWorkFaceId())) {
            return new ResponseEntity<>(ReturnMsg.fail("工作面 ID 不能不空"), HttpStatus.OK);
        }
        if (queryCriteria.getChannel() == null) {
            return new ResponseEntity<>(ReturnMsg.fail("天线通道不能不空"), HttpStatus.OK);
        }

        try {
            Integer result = positionRawHistoryService.modifyPositionRawHistory(queryCriteria);
            return new ResponseEntity<>(ReturnMsg.success(result), HttpStatus.OK);
        }
        catch (Exception ex) {
            return new ResponseEntity<>(ReturnMsg.fail("服务器错误"), HttpStatus.OK);
        }
    }

}
