package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.dto.WorkingFaceLocatorStatusComposite;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorStatus;
import com.bdtd.modules.working_face.service.IWorkingFaceLocatorStatusService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作面定位设备监测值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/locatorStatus")
@Api(value = "工作面定位设备监测值", tags = "工作面定位设备监测值接口")
public class WorkingFaceLocatorStatusController {

    private final IWorkingFaceLocatorStatusService workingFaceLocatorStatusService;

    public WorkingFaceLocatorStatusController(IWorkingFaceLocatorStatusService workingFaceLocatorStatusService)
    {
        this.workingFaceLocatorStatusService = workingFaceLocatorStatusService;
    }

    /**
     * 工作面进尺定位设备监测值列表查询
     *
     * @param groupCode     集团编码
     * @param mineCode      煤矿编码
     * @param workingFaceId 工作面ID
     * @param labelCode     标签卡编码
     * @param pointId       点位ID
     * @param pointDesc     点位描述
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定位设备监测值列表查询", notes = "工作面进尺定位设备监测值列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkingFaceLocatorStatusList(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String labelCode,
            String pointId,
            String pointDesc
    )
    {
        Msg msg;
        List<WorkingFaceLocatorStatus> listResult;
        try {
            WorkingFaceLocatorStatus queryEntity = new WorkingFaceLocatorStatus();
            if (StringUtil.isNotEmpty(groupCode)) {
                queryEntity.setGroupCode(groupCode);
            }
            if (StringUtil.isNotEmpty(mineCode)) {
                queryEntity.setMineCode(mineCode);
            }
            if (StringUtil.isNotEmpty(workingFaceId)) {
                queryEntity.setWorkingFaceId(workingFaceId);
            }
            if (StringUtil.isNotEmpty(labelCode)) {
                queryEntity.setLabelCode(labelCode);
            }
            if (StringUtil.isNotEmpty(pointId)) {
                queryEntity.setPointId(pointId);
            }
            if (StringUtil.isNotEmpty(pointDesc)) {
                queryEntity.setPointDesc(pointDesc);
            }

            listResult = workingFaceLocatorStatusService.list(Wrappers.query(queryEntity));
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面进尺定位设备监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺定位设备监测值分页查询
     *
     * @param page          分页对象
     * @param groupCode     集团编码
     * @param mineCode      煤矿编码
     * @param workingFaceId 工作面ID
     * @param labelCode     标签卡编码
     * @param pointId       点位ID
     * @param pointDesc     点位描述
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定位设备监测值分页查询", notes = "工作面进尺定位设备监测值分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkingFaceLocatorStatusPage(
            Page<WorkingFaceLocatorStatus> page,
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String labelCode,
            String pointId,
            String pointDesc
    )
    {
        Msg msg;
        try {
            WorkingFaceLocatorStatus queryEntity = new WorkingFaceLocatorStatus();
            queryEntity.setGroupCode(groupCode);
            queryEntity.setMineCode(mineCode);
            queryEntity.setWorkingFaceId(workingFaceId);
            queryEntity.setLabelCode(labelCode);
            queryEntity.setPointId(pointId);
            queryEntity.setPointDesc(pointDesc);

            msg = ReturnMsg.success(
                    workingFaceLocatorStatusService.selectPage(queryEntity, page)
            );
        }
        catch (Exception e) {
            log.error("工作面进尺定位设备监测值分页查询异常, {}", e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺定位设备监测信息汇总分页查询
     *
     * @param page            分页对象
     * @param groupCode       集团编码
     * @param mineCode        煤矿编码
     * @param workingFaceId   工作面ID，多个工作面ID用半角逗号分隔
     * @param workingFaceType 工作面类型
     * @param state           设备监测数据质量（good, bad）
     * @param systemFlag      接入状态（0,1,9）
     * @param workModeIsNull  工作模式为空
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定位设备监测信息汇总分页查询", notes = "工作面进尺定位设备监测信息汇总分页查询")
    @GetMapping("/compositedPage")
    public ResponseEntity<Msg> getWorkingFaceLocatorStatusCompositedPage(
            Page<WorkingFaceLocatorStatusComposite> page,
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String state,
            String systemFlag,
            Integer workModeIsNull
    )
    {
        Msg msg;
        try {
            WorkingFaceLocatorStatus queryEntity = new WorkingFaceLocatorStatus();
            queryEntity.setGroupCode(groupCode);
            queryEntity.setMineCode(mineCode);
            queryEntity.setWorkingFaceType(workingFaceType);
            if (StringUtil.isNotEmpty(workingFaceId)) {
                if (workingFaceId.contains(",")) {
                    queryEntity.setWorkingFaceIdList(
                            Arrays.stream(workingFaceId.split(","))
                                  .filter(StringUtil::isNotEmpty)
                                  .collect(Collectors.toList())
                    );
                }
                else {
                    queryEntity.setWorkingFaceId(workingFaceId);
                }
            }

            // 默认返回最新上传的标签卡，忽略不再上传的卡号
            if (StringUtil.isEmpty(state)) {
                queryEntity.setState("good");
            }
            else {
                queryEntity.setState(state);
            }

            // 默认获取接入中的工作面
            if (systemFlag == null) {
                queryEntity.setSystemFlag("1");
            }
            else {
                queryEntity.setSystemFlag(systemFlag);
            }
            // 默认获取绑定了工作模式的数据
            if (workModeIsNull == null) {
                queryEntity.setWorkModeIsNull(0);
            }
            else {
                queryEntity.setWorkModeIsNull(workModeIsNull);
            }

            IPage<WorkingFaceLocatorStatusComposite> pageResult = workingFaceLocatorStatusService.selectCompositedPage(queryEntity, page);
            msg = ReturnMsg.success(pageResult);
        }
        catch (BadRequestException bre) {
            log.error("工作面进尺定位设备监测信息汇总分页查询发生错误, {}", bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("工作面进尺定位设备监测信息汇总分页查询异常, {}", e.getMessage(), e);
            msg = ReturnMsg.fail("查询发生错误", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺定位设备监测历史值查询
     *
     * @param pointId    设备编码
     * @param startTime  开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime    结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize 采样率, 格式: 1s 或 1m
     * @param timeMode   时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定位设备监测历史值查询", notes = "工作面进尺定位设备监测历史值查询")
    @GetMapping("/historyValues")
    public ResponseEntity<Msg> getWorkingFaceLocatorStatusHistoryList(
            @RequestParam(required = true) String pointId,
            String startTime,
            String endTime,
            String polymerize,
            Integer timeMode
    ) throws BadRequestException
    {
        Msg msg;
        try {
            if (timeMode == null) {
                timeMode = 2;
            }
            else if (timeMode < 0 || timeMode > 3) {
                return new ResponseEntity<>(ReturnMsg.fail("时间窗口查询模式参数无效"), HttpStatus.OK);
            }

            msg = ReturnMsg.success(
                    workingFaceLocatorStatusService.selectHistoryValues(pointId, startTime, endTime, polymerize, timeMode)
            );
        }
        catch (BadRequestException bre) {
            log.error("工作面进尺定位设备监测历史值查询输入错误, {}", bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("工作面进尺定位设备监测历史值查询异常, {}", e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
