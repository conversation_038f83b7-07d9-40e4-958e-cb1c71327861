package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressSettingService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 工作面配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/progressSettings")
@Api(value = "工作面配置", tags = "工作面配置接口")
public class WorkingFaceProgressSettingController
{

    private final IWorkingFaceProgressSettingService workingFaceProgressSettingService;

    public WorkingFaceProgressSettingController(IWorkingFaceProgressSettingService workingFaceProgressSettingService) {
        this.workingFaceProgressSettingService = workingFaceProgressSettingService;
    }

    /**
     * 工作面配置列表查询
     *
     * @param companyCode 二级公司编码
     * @param mineCode    煤矿编码
     * @return 监测值列表
     */
    @ApiOperation(value = "工作面配置列表查询", notes = "工作面配置列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkingFaceProgressSettingList(
            @RequestParam(value = "companyCode", required = false) String companyCode,
            @RequestParam(required = false) String mineCode
    )
    {
        Msg msg;
        List<WorkingFaceProgressSetting> listResult;

        try {
            WorkingFaceProgressSetting queryEntity = new WorkingFaceProgressSetting();
            if (StringUtil.isNotEmpty(companyCode)) {
                queryEntity.setCompanyCode(companyCode);
            }
            if (StringUtil.isNotEmpty(mineCode)) {
                queryEntity.setMineCode(mineCode);
            }

            // 默认按更新时间降序排列
            QueryWrapper<WorkingFaceProgressSetting> queryWrap = Wrappers.query(queryEntity);
            queryWrap.orderByDesc("updated_at");

            listResult = workingFaceProgressSettingService.list(queryWrap);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.warn("工作面配置列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面配置分页查询
     *
     * @param page        分页对象
     * @param companyCode 二级公司编码
     * @param mineCode    煤矿编码
     * @return 监测值分页结果
     */
    @ApiOperation(value = "工作面配置分页查询", notes = "工作面配置分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkingFaceProgressSettingPage(
            Page<WorkingFaceProgressSetting> page,
            @RequestParam(value = "companyCode", required = false) String companyCode,
            @RequestParam(required = false) String mineCode
    )
    {
        Msg msg;

        WorkingFaceProgressSetting queryEntity = new WorkingFaceProgressSetting();
        if (StringUtil.isNotEmpty(companyCode)) {
            queryEntity.setCompanyCode(companyCode);
        }
        if (StringUtil.isNotEmpty(mineCode)) {
            queryEntity.setMineCode(mineCode);
        }

        // 默认按更新时间降序排列
        if (page.getOrders() == null || page.getOrders().isEmpty()) {
            page.setOrders(Arrays.asList(OrderItem.desc("updated_at")));
        }

        try {
            msg = ReturnMsg.success(workingFaceProgressSettingService.page(page, Wrappers.query(queryEntity)));
        }
        catch (Exception e) {
            log.warn("工作面配置分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 二级公司
     *
     * @return 二级公司列表
     */
    @ApiOperation(value = "二级公司列表查询", notes = "二级公司列表查询")
    @GetMapping("/companyList")
    public ResponseEntity<Msg> getCompanyList() {
        Msg msg;
        try {
            msg = ReturnMsg.success(workingFaceProgressSettingService.getCompanyList());
        }
        catch (Exception e) {
            log.warn("二级公司列表查询查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 煤矿列表查询
     *
     * @param groupCode 二级公司编码
     * @return 煤矿列表
     */
    @ApiOperation(value = "煤矿列表查询", notes = "煤矿列表查询")
    @GetMapping("/mineList")
    public ResponseEntity<Msg> getMineList(@RequestParam(value = "companyCode", required = false) String groupCode) {
        Msg msg;
        try {
            msg = ReturnMsg.success(workingFaceProgressSettingService.getMineList(groupCode));
        }
        catch (Exception e) {
            log.warn("煤矿列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
