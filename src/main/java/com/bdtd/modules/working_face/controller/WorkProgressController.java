package com.bdtd.modules.working_face.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bdtd.conf.MesBusinessEnum;
import com.bdtd.conf.RabbitTopicConfig;
import com.bdtd.feign.GisBaseFeignService;
import com.bdtd.feign.MineBizFeignService;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.mapper.AccessNodeMapper;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.entity.WorkingFaceDefinition;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressSetting;
import com.bdtd.modules.working_face.service.IWorkingFaceDefinitionService;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressSettingService;
import com.bdtd.modules.working_face.util.disruptor.FaceInfoChangedEventManager;
import com.bdtd.util.StringUtil;
import com.bdtd.util.middleware.MqSendUtil;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.*;

/**
 * <p>
 * 工作面采掘生产配置管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-26
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/workProgress")
@Api(value = "工作面采掘生产配置管理", tags = "工作面采掘生产配置管理")
public class WorkProgressController {

    // region 变量 & 构造函数

    /** 工作面配置下发心跳接口工作模式: cache 默认, direct */
    @Value("${biz.working-face-setting-heartbeat-mode}")
    private String settingHeartbeatMode;
    /** 工作面配置下发模式: rmq/api(默认)/all/none, rmq 为业务侧下发变更通知到本服务，本服务去调用业务侧接口, api 为业务侧写本服务接口, all 同时启用, none 关闭 */
    @Value("${biz.working-face-setting-sync-mode}")
    private String settingSyncMode;
    /** 工作面配置接口下发数据模式: new 同步最近更新的工作面, all 全量 */
    @Value("${scheduled.working-progress-settings-api-push-mode}")
    private String settingApiPushMode;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private MqSendUtil mqSendUtil;
    @Autowired
    private FaceInfoChangedEventManager<List<Timestamp>> faceInfoChangedEventManager;

    @Autowired
    MineBizFeignService mineBizFeignService;
    @Autowired
    GisBaseFeignService gisBaseFeignService;

    @Autowired
    IWorkingFaceProgressSettingService workingFaceProgressSettingService;
    @Autowired
    IWorkingFaceDefinitionService workingFaceDefinitionService;
    @Autowired
    IServiceCallLogService serviceCallLogService;
    @Autowired
    AccessNodeMapper accessNodeMapper;

    // endregion 变量 & 构造函数

    // region 定位解析配置下发接口, 供GIS侧或者业务侧直接写入配置

    /**
     * 定位解析配置下发接口, 供GIS侧或者业务侧直接写入配置
     *
     * @param content 煤矿配置
     * @return /
     */
    @ApiOperation(value = "定位解析配置下发接口, 供图纸侧或者业务侧直接写入配置", notes = "定位解析配置下发接口, 供图纸侧或者业务侧直接写入配置")
    @PostMapping("/postSettings")
    public ResponseEntity<Object> postWorkingFaceSettings(@RequestBody JSONObject content)
    {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("type_code", "pos_cfg");
        retMap.put("timestamp", System.currentTimeMillis());

        // 是否启用接口下发配置
        if (!("api".equalsIgnoreCase(settingSyncMode) || "all".equalsIgnoreCase(settingSyncMode))) {
            retMap.put("content", "通过接口下发配置已禁用");
            return new ResponseEntity<>(retMap, HttpStatus.FORBIDDEN);
        }

        try {
            // 解析处理配置
            List<ServiceCallLog> serviceCallLogs = workingFaceProgressSettingService.parseProgressSettings(
                    "配置下发接口",
                    content,
                    "10000",
                    null,
                    null,
                    null,
                    // 工作面配置接口下发数据模式: new 同步最近更新的工作面, all 全量
                    "all".equalsIgnoreCase(settingApiPushMode),
                    // GIS 侧下发配置包含工作模式
                    true,
                    // 强制更新接入系统信息
                    "1"
            );

            try {
                // 自动创建节点
                accessNodeMapper.generatePositionCollectServerNodes();
                accessNodeMapper.generatePositionCollectServiceNodes();
                // 自动创建节点配置
                accessNodeMapper.generatePositionCollectNodeSettings();
                // 自动创建节点关系
                accessNodeMapper.generatePositionCollectNodeRelations();
            }
            catch (Exception e) {
                log.error("自动创建节点信息失败, {}", e.getMessage(), e);
            }

            // 服务调用日志
            if (serviceCallLogs != null && !serviceCallLogs.isEmpty()) {
                serviceCallLogService.createLogs(serviceCallLogs);
            }

            retMap.put("content", "成功");
            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("定位解析配置下发接口, 处理失败: {}", e.getMessage(), e);

            retMap.put("content", "失败");
            return new ResponseEntity<>(retMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        finally {
            log.info("receive working face settings and parsed: {}", JSON.toJSONString(content));
        }
    }

    // endregion 定位解析配置下发接口, 供业务侧或者图纸侧直接写入配置

    // region 查询定位解析配置

    /**
     * 查询定位解析配置
     *
     * @param groupCode      集团编码
     * @param companyCode    二级公司编码
     * @param mineCode       煤矿编码
     * @param lastUpdateTime 已获取的最后配置时间
     * @return /
     */
    @ApiOperation(value = "查询定位解析配置", notes = "查询定位解析配置")
    @GetMapping("/settings")
    public ResponseEntity<Object> getWorkingFaceSettings(
            @RequestParam(required = false, value = "group_code") String groupCode,
            @RequestParam(required = false, value = "company_code") String companyCode,
            @RequestParam(required = false, value = "mine_code") String mineCode,
            @RequestParam(required = false, value = "last_update_time") Long lastUpdateTime
    )
    {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("type_code", "pos_cfg");
        retMap.put("timestamp", System.currentTimeMillis());

        // // 集团编码和煤矿编码都为空
        // if (StringUtil.isEmpty(groupCode) && StringUtil.isEmpty(companyCode) && StringUtil.isEmpty(mineCode)) {
        //     retMap.put("error", "必须提供单位编码");
        //     return new ResponseEntity<>(retMap, HttpStatus.BAD_REQUEST);
        // }

        try {
            // // 构造查询参数
            // Map<String, Object> queryMap = new HashMap<>();
            // if (StringUtil.isNotEmpty(groupCode)) {
            //     queryMap.put("group_code", groupCode);
            // }
            // if (StringUtil.isNotEmpty(companyCode)) {
            //     queryMap.put("company_code", companyCode);
            // }
            // if (StringUtil.isNotEmpty(mineCode)) {
            //     queryMap.put("mine_code", mineCode);
            // }

            // 根据获取参数拉取最新配置
            JSONObject result = mineBizFeignService.queryWorkingFaceProgressSettings(
                    groupCode,
                    companyCode,
                    mineCode,
                    lastUpdateTime,
                    MesBusinessEnum.HEADER.desc
            );

            // 消息类型判断
            String msgType = result.getString("type_code");
            if (StringUtil.isEmpty(msgType) || !"pos_cfg".equalsIgnoreCase(msgType)) {
                retMap.put("content", null);
            }
            else {
                retMap.put("content", result.getJSONArray("content"));
            }

            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("查询定位解析配置, 处理失败: {}", e.getMessage(), e);

            retMap.put("error", "从业务侧获取配置失败");
            return new ResponseEntity<>(retMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // endregion 查询定位解析配置

    // region 自动生成数据接入节点

    /**
     * 自动生成数据接入节点
     *
     * @return /
     */
    @ApiOperation(value = "自动生成数据接入节点", notes = "自动生成数据接入节点")
    @GetMapping("/genAccessNodeInfo")
    public ResponseEntity<Object> genAccessNodeInfo()
    {
        try {
            // 自动创建节点
            accessNodeMapper.generatePositionCollectServerNodes();
            accessNodeMapper.generatePositionCollectServiceNodes();
            // 自动创建节点配置
            accessNodeMapper.generatePositionCollectNodeSettings();
            // 自动创建节点关系
            accessNodeMapper.generatePositionCollectNodeRelations();

            return new ResponseEntity<>(ReturnMsg.success(null), HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("自动创建节点信息失败, {}", e.getMessage(), e);
        }

        return new ResponseEntity<>(ReturnMsg.fail("自动生成失败"), HttpStatus.OK);
    }

    // endregion 自动生成数据接入节点

    // region 心跳接口

    /**
     * 心跳接口
     *
     * @param groupCode      集团编码
     * @param companyCode    二级公司编码
     * @param mineCode       煤矿编码
     * @param lastUpdateTime 已获取的最后配置时间
     * @return /
     */
    @ApiOperation(value = "心跳接口", notes = "心跳接口")
    @GetMapping("/heartbeat")
    public ResponseEntity<Object> getHeartBeat(
            @RequestParam(required = false, value = "group_code") String groupCode,
            @RequestParam(required = false, value = "company_code") String companyCode,
            @RequestParam(required = false, value = "mine_code") String mineCode,
            @RequestParam(required = false, value = "lastest_update_time") Long lastUpdateTime
    )
    {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("type_code", "pos_cfg");
        retMap.put("timestamp", System.currentTimeMillis());

        // 时间为空时只响应心跳, 不返回配置
        if (lastUpdateTime == null) {
            retMap.put("content", null);
            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }

        try {
            // 心跳工作模式，cache 默认, direct
            if (StringUtil.isNotEmpty(settingHeartbeatMode) && "direct".equalsIgnoreCase(settingHeartbeatMode)) {
                // 根据获取参数拉取最新配置
                JSONObject result = mineBizFeignService.queryWorkingFaceProgressSettings(
                        groupCode,
                        companyCode,
                        mineCode,
                        lastUpdateTime,
                        MesBusinessEnum.HEADER.desc
                );

                // 消息类型判断
                String msgType = result.getString("type_code");
                if (StringUtil.isEmpty(msgType) || !"pos_cfg".equalsIgnoreCase(msgType)) {
                    retMap.put("content", null);
                }
                else {
                    retMap.put("content", result.getJSONArray("content"));
                }
            }
            else {
                // // 集团编码、二级公司编码和煤矿编码都为空
                // if (StringUtil.isEmpty(groupCode) && StringUtil.isEmpty(companyCode) && StringUtil.isEmpty(mineCode)) {
                //     retMap.put("error", "必须提供单位编码");
                //     return new ResponseEntity<>(retMap, HttpStatus.BAD_REQUEST);
                // }

                QueryWrapper<WorkingFaceProgressSetting> queryCriteria = new QueryWrapper<>();
                queryCriteria.eq(StringUtil.isNotEmpty(groupCode), "group_code", groupCode);
                queryCriteria.eq(StringUtil.isNotEmpty(companyCode), "company_code", companyCode);
                queryCriteria.eq(StringUtil.isNotEmpty(mineCode), "mine_code", mineCode);

                // 时间查询
                queryCriteria.gt(lastUpdateTime > 0, "update_time", new Timestamp(lastUpdateTime));

                // 执行查询
                List<WorkingFaceProgressSetting> result = workingFaceProgressSettingService.list(queryCriteria);

                // 组装返回结果
                List<JSONObject> retList = new ArrayList<>();
                if (result.isEmpty()) {
                    retMap.put("content", retList);
                }
                else {
                    // 组装返回结果
                    for (WorkingFaceProgressSetting setting : result) {
                        retList.add(JSON.parseObject(setting.getSettings()));
                    }
                    retMap.put("content", retList);
                }
            }

            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("获取心跳返回配置失败, {}", e.getMessage(), e);

            retMap.put("error", "获取心跳返回配置失败");
            return new ResponseEntity<>(retMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // endregion 心跳接口

    // region 【废弃】推送通知到消息队列，触发工作面配置同步

    /**
     * 【废弃】推送通知到消息队列，触发工作面配置同步
     * 备注：测试用，模拟业务侧往消息队列推送数据，触发从业务册同步工作面信息
     *
     * @param groupCode              集团编码
     * @param companyCode            二级公司编码
     * @param mineCode               煤矿编码
     * @param changeTime             配置更新时间
     * @param forceUpdateModelSystem 强制更新子系统信息
     * @return /
     */
    @ApiOperation(value = "推送通知到消息队列，触发工作面配置同步", notes = "推送通知到消息队列，触发工作面配置同步")
    @GetMapping("/pullSettings")
    public ResponseEntity<Object> syncWorkingFaceSettings(
            @RequestParam(required = false, value = "group_code") String groupCode,
            @RequestParam(required = false, value = "company_code") String companyCode,
            @RequestParam(required = false, value = "mine_code") String mineCode,
            @RequestParam(required = false, value = "change_time") Long changeTime,
            @RequestParam(required = false, value = "force_update_model_system") Integer forceUpdateModelSystem
    )
    {

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("type_code", "pos_cfg");
        retMap.put("timestamp", System.currentTimeMillis());
        retMap.put("verify_token", null);

        // 是否启用消息队列同步配置
        if (!("rmq".equals(settingSyncMode) || "all".equals(settingSyncMode))) {
            retMap.put("content", "通过消息队列同步配置已禁用");
            return new ResponseEntity<>(retMap, HttpStatus.FORBIDDEN);
        }

        try {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("group_code", groupCode);
            contentMap.put("company_code", companyCode);
            contentMap.put("mine_code", mineCode);
            contentMap.put(
                    "change_time",
                    changeTime == null
                            ? System.currentTimeMillis()
                            : changeTime
            );
            contentMap.put(
                    "forceUpdateModelSystem",
                    forceUpdateModelSystem == null
                            ? 0
                            : forceUpdateModelSystem
            );
            retMap.put("content", contentMap);

            mqSendUtil.send(
                    RabbitTopicConfig.WORKING_FACE_PROGRESS_SETTING_UPDATE_EXCHANGE,
                    RabbitTopicConfig.WORKING_FACE_PROGRESS_SETTING_UPDATE_ROUTING_KEY,
                    retMap
            );

            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }
        catch (Exception e) {
            log.error("同步工作面配置失败, {}", e.getMessage(), e);

            retMap.put("error", "同步工作面配置失败");
            return new ResponseEntity<>(retMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // endregion 【废弃】推送通知到消息队列，触发工作面配置同步

    // region 【废弃】强制同步定位解析配置

    /**
     * 【废弃】强制同步定位解析配置
     * 备注：不再从业务侧同步工作面信息，统一从GIS获取
     *
     * @param syncMap 煤矿编码
     * @return /
     */
    @ApiOperation(value = "强制同步定位解析配置", notes = "强制同步定位解析配置")
    @PostMapping("/syncSettings")
    public ResponseEntity<Object> forceSyncSettings(@RequestBody Map<String, String> syncMap)
    {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("type_code", "pos_cfg");
        retMap.put("timestamp", System.currentTimeMillis());

        // 是否启用接口下发配置
        if (!("api".equals(settingSyncMode) || "all".equals(settingSyncMode))) {
            retMap.put("content", "通过接口下发配置已禁用");
            return new ResponseEntity<>(retMap, HttpStatus.FORBIDDEN);
        }

        // 必须指定煤矿编码
        if (!syncMap.containsKey("mineCode") || StringUtil.isEmpty(syncMap.get("mineCode"))) {
            retMap.put("content", "必须指定煤矿编码");
            return new ResponseEntity<>(retMap, HttpStatus.BAD_REQUEST);
        }

        String actionStr = "通过接口调用业务侧配置接口";

        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();
        try {
            // 同步配置
            serviceCallLogs = workingFaceProgressSettingService.syncProgressSettings(
                    syncMap.get("mineCode"),
                    true,
                    false,
                    0L,
                    actionStr
            );

            retMap.put("content", "成功");
            return new ResponseEntity<>(retMap, HttpStatus.OK);
        }
        catch (Exception e) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 从业务侧获取下发配置失败",
                    (byte) 1,
                    syncMap.get("mineCode")
            ));
            log.error("强制同步定位解析配置, 处理失败: {}", e.getMessage(), e);

            retMap.put("content", "失败");
            return new ResponseEntity<>(retMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        finally {
            try {
                if (serviceCallLogs != null && !serviceCallLogs.isEmpty()) {
                    serviceCallLogService.createLogs(serviceCallLogs);
                }
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    // endregion 【废弃】强制同步定位解析配置

    // region 【废弃】工作面基本信息变更通知接口，接收 GIS 服务端调用

    /**
     * 【废弃】工作面基本信息变更通知接口，接收 GIS 服务端调用
     * 备注：不再通过该接口下发工作面变化
     *
     * @return /
     */
    @ApiOperation(value = "工作面基本信息变更通知接口", notes = "工作面基本信息变更通知接口")
    @GetMapping("/faceInfoNotify")
    public ResponseEntity<Object> getFaceInfoNotify()
    {
        log.info("Received face info notify request");

        // 工作面列表
        List<WorkingFaceDefinition> workFaceList = null;

        long startMs = System.currentTimeMillis();
        try {
            // [POST] drawingextend/query_workface_info_all
            JSONObject queryRet = gisBaseFeignService.queryAllWorkfaceInfo();
            if (!queryRet.containsKey("data")) {
                log.warn("query_workface_info_all, no returned data");
                return new ResponseEntity<>(ReturnMsg.fail("返回格式不识别"), HttpStatus.OK);
            }
            JSONArray retData = queryRet.getJSONArray("data");
            if (retData == null || retData.isEmpty()) {
                log.warn("query_workface_info_all, returned data is null or empty");
                return new ResponseEntity<>(ReturnMsg.fail("返回数据为空"), HttpStatus.OK);
            }

            // 工作面列表
            workFaceList = JSONArray.parseArray(
                    JSON.toJSONString(retData),
                    WorkingFaceDefinition.class
            );
        }
        catch (Exception ex) {
            log.error("queryAllWorkfaceInfo failed, error: {}", ex.getMessage(), ex);
        }

        if (workFaceList == null || workFaceList.isEmpty()) {
            log.info("Received face info notify request and got empty workface, cost {} ms", (System.currentTimeMillis() - startMs));
            return new ResponseEntity<>(ReturnMsg.success(null), HttpStatus.OK);
        }

        try {
            // 最后更新时间
            Timestamp lastUpdateTime = workingFaceDefinitionService.selectLastUpdateTime();
            // 处理工作面名称
            for (WorkingFaceDefinition wfd : workFaceList) {
                if (StringUtil.isNotEmpty(wfd.getWorkFaceName())) {
                    wfd.setWorkFaceName(wfd.getWorkFaceName().trim());
                }
            }

            // 工作面列表
            List<WorkingFaceDefinition> finalWorkFaceList = workFaceList;
            // 更新工作面信息
            Integer updCount = transactionTemplate.execute(status -> {
                return workingFaceDefinitionService.upsertBatchWfDef(finalWorkFaceList);
            });
            log.info("upsertBatchWfDef, updated count: {}", updCount);

            // 通知处理工作面信息变更
            faceInfoChangedEventManager.publish(
                    // 信息发生变更的更新时间区间
                    Arrays.asList(lastUpdateTime, new Timestamp(System.currentTimeMillis()))
            );
        }
        catch (Exception ex) {
            log.error("queryAllWorkfaceInfo retrieve result process failed: {}", ex.getMessage(), ex);
        }
        finally {
            log.info(
                    "Received face info notify request and got {} workface, cost {} ms",
                    workFaceList.size(),
                    (System.currentTimeMillis() - startMs)
            );
        }

        return new ResponseEntity<>(ReturnMsg.success(null), HttpStatus.OK);
    }

    // endregion 【废弃】工作面基本信息变更通知接口，接收 GIS 服务端调用

}
