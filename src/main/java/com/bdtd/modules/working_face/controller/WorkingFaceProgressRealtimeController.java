package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressRealtime;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressRealtimeService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作面进尺监测值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/progressRealtime")
@Api(value = "工作面进尺监测值", tags = "工作面进尺监测值接口")
public class WorkingFaceProgressRealtimeController
{

    private final IWorkingFaceProgressRealtimeService workingFaceProgressRealtimeService;

    public WorkingFaceProgressRealtimeController(IWorkingFaceProgressRealtimeService workingFaceProgressRealtimeService)
    {
        this.workingFaceProgressRealtimeService = workingFaceProgressRealtimeService;
    }

    /**
     * 工作面进尺监测值列表查询
     *
     * @param groupCode     集团编码
     * @param mineCode      煤矿编码
     * @param workingFaceId 工作面ID
     * @return 监测值列表
     */
    @ApiOperation(value = "工作面进尺监测值列表查询", notes = "工作面进尺监测值列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkingFaceProgressRealtimeList(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId
    )
    {
        Msg msg;
        List<WorkingFaceProgressRealtime> listResult;

        try {
            WorkingFaceProgressRealtime queryEntity = new WorkingFaceProgressRealtime();
            if (StringUtil.isNotEmpty(groupCode)) {
                queryEntity.setGroupCode(groupCode);
            }
            if (StringUtil.isNotEmpty(mineCode)) {
                queryEntity.setMineCode(mineCode);
            }
            if (StringUtil.isNotEmpty(workingFaceId)) {
                queryEntity.setWorkingFaceId(workingFaceId);
            }

            listResult = workingFaceProgressRealtimeService.list(Wrappers.query(queryEntity));
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面进尺监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺监测值分页查询
     *
     * @param page          分页对象
     * @param groupCode     集团编码
     * @param mineCode      煤矿编码
     * @param workingFaceId 工作面ID
     * @return 监测值分页结果
     */
    @ApiOperation(value = "工作面进尺监测值分页查询", notes = "工作面进尺监测值分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkingFaceProgressRealtimePage(
            Page<WorkingFaceProgressRealtime> page,
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId
    )
    {
        Msg msg;

        WorkingFaceProgressRealtime queryEntity = new WorkingFaceProgressRealtime();
        if (StringUtil.isNotEmpty(groupCode)) {
            queryEntity.setGroupCode(groupCode);
        }
        if (StringUtil.isNotEmpty(mineCode)) {
            queryEntity.setMineCode(mineCode);
        }
        if (StringUtil.isNotEmpty(workingFaceId)) {
            queryEntity.setWorkingFaceId(workingFaceId);
        }

        try {
            msg = ReturnMsg.success(
                    workingFaceProgressRealtimeService.selectPage(page, queryEntity)
            );
        }
        catch (Exception e) {
            log.error("工作面进尺监测值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺最新监测值列表
     *
     * @param groupCode      集团编码
     * @param mineCode       煤矿编码
     * @param workingFaceIds 工作面ID，多个ID用半角逗号分隔
     * @return 监测值列表
     */
    @ApiOperation(value = "工作面进尺最新监测值列表", notes = "工作面进尺最新监测值列表")
    @GetMapping("/latest")
    public ResponseEntity<Msg> getWorkingFaceProgressRealtimeLatestList(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceIds
    )
    {
        Msg msg;
        List<WorkingFaceProgressRealtime> listResult;

        try {
            listResult = workingFaceProgressRealtimeService.getLatestRealtimeList(
                    groupCode,
                    mineCode,
                    StringUtil.isNotEmpty(workingFaceIds)
                    ? Arrays.stream(workingFaceIds.split(",")).collect(Collectors.toList())
                    : null
            );
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面进尺最新监测值列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺监测历史值查询
     *
     * @param workingFaceId 工作面ID
     * @param startTime     开始时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param endTime       结束时间, 格式 yyyy-MM-dd HH:mm:ss.SSS
     * @param polymerize    采样率, 格式: 1s 或 1m
     * @param timeMode      时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间
     * @return 历史值列表
     */
    @ApiOperation(value = "工作面进尺监测历史值查询", notes = "工作面进尺监测历史值查询")
    @GetMapping("/historyValues")
    public ResponseEntity<Msg> getWorkingFaceProgressRealtimeHistoryList(
            @RequestParam(required = true) String workingFaceId,
            String startTime,
            String endTime,
            String polymerize,
            Integer timeMode
    )
    {
        Msg msg;
        try {
            if (timeMode == null) {
                timeMode = 2;
            }
            else if (timeMode < 0 || timeMode > 3) {
                return new ResponseEntity<>(ReturnMsg.fail("时间窗口查询模式参数无效"), HttpStatus.OK);
            }

            msg = ReturnMsg.success(
                    workingFaceProgressRealtimeService.selectHistoryValues(workingFaceId, startTime, endTime, polymerize, timeMode)
            );
        }
        catch (BadRequestException bre) {
            log.error("工作面进尺监测历史值查询输入错误, " + bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("工作面进尺监测历史值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 进尺测距数据采集全局在线检查
     *
     * @return 是否正常，true 正常 false可能断线
     */
    @ApiOperation(value = "进尺测距数据采集全局在线检查", notes = "进尺测距数据采集全局在线检查")
    @GetMapping({ "/globalState", "/globalAliveCheck" })
    public ResponseEntity<Msg> doRealtimeGlobalCheck()
    {
        Msg msg;
        try {
            msg = ReturnMsg.success(workingFaceProgressRealtimeService.doRealtimeGlobalCheck());
        }
        catch (Exception e) {
            log.error("进尺测距数据采集全局在线检查发生异常, {}", e.getMessage(), e);
            msg = ReturnMsg.fail("检查发生异常", null);
        }

        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
