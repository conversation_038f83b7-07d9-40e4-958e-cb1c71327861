package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.dto.WorkingFaceWarnIndicatorSubmitDto;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressDefinitionService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 工作面进尺定义 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/progressDefinition")
@Api(value = "工作面进尺定义", tags = "工作面进尺定义接口")
public class WorkingFaceProgressDefinitionController
{

    private final IWorkingFaceProgressDefinitionService workingFaceProgressDefinitionService;

    public WorkingFaceProgressDefinitionController(IWorkingFaceProgressDefinitionService workingFaceProgressDefinitionService)
    {
        this.workingFaceProgressDefinitionService = workingFaceProgressDefinitionService;
    }

    /**
     * 工作面进尺定义列表查询
     *
     * @param groupCode       集团编码
     * @param mineCode        煤矿编码
     * @param workingFaceId   工作面ID
     * @param workingFaceType 工作面类型
     * @param workingFaceName 工作面名称
     * @param deletedAtIsNull 是否删除
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定义列表查询", notes = "工作面进尺定义列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionList(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String workingFaceName,
            Integer deletedAtIsNull
    )
    {
        Msg msg;
        List<WorkingFaceProgressDefinition> listResult;

        try {
            WorkingFaceProgressDefinition queryEntity = new WorkingFaceProgressDefinition();
            if (StringUtil.isNotEmpty(groupCode)) {
                queryEntity.setGroupCode(groupCode);
            }
            if (StringUtil.isNotEmpty(mineCode)) {
                queryEntity.setMineCode(mineCode);
            }
            if (StringUtil.isNotEmpty(workingFaceId)) {
                queryEntity.setWorkingFaceId(workingFaceId);
            }
            if (StringUtil.isNotEmpty(workingFaceType)) {
                queryEntity.setWorkingFaceType(workingFaceType);
            }
            if (StringUtil.isNotEmpty(workingFaceName)) {
                queryEntity.setWorkingFaceName(workingFaceName);
            }

            QueryWrapper<WorkingFaceProgressDefinition> queryWrapper = Wrappers.query(queryEntity);
            queryWrapper
                    // 未删除
                    .isNull(deletedAtIsNull == null || deletedAtIsNull == 1, "deleted_at")
                    // 已删除
                    .isNotNull(deletedAtIsNull != null && deletedAtIsNull == 0, "deleted_at");

            listResult = workingFaceProgressDefinitionService.list(queryWrapper);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("工作面进尺定义列表查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺定义分页查询
     *
     * @param page            分页对象
     * @param groupCode       集团编码
     * @param mineCode        煤矿编码
     * @param workingFaceId   工作面ID
     * @param workingFaceType 工作面类型
     * @param workingFaceName 工作面名称
     * @param deletedAtIsNull 是否删除
     * @return Result
     */
    @ApiOperation(value = "工作面进尺定义分页查询", notes = "工作面进尺定义分页查询")
    @GetMapping("/page")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionPage(
            Page<WorkingFaceProgressDefinition> page,
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String workingFaceName,
            Integer deletedAtIsNull
    )
    {
        Msg msg;

        try {
            WorkingFaceProgressDefinition queryEntity = new WorkingFaceProgressDefinition();
            queryEntity.setGroupCode(groupCode);
            queryEntity.setMineCode(mineCode);
            queryEntity.setWorkingFaceId(workingFaceId);
            queryEntity.setWorkingFaceType(workingFaceType);
            queryEntity.setWorkingFaceName(workingFaceName);
            queryEntity.setDeletedAtIsNull(deletedAtIsNull);

            msg = ReturnMsg.success(
                    workingFaceProgressDefinitionService.selectPage(page, queryEntity)
            );
        }
        catch (Exception e) {
            log.error("工作面进尺定义分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺点位定义带实时值查询
     *
     * @param groupCode       集团编码
     * @param mineCode        煤矿编码
     * @param workingFaceId   工作面ID
     * @param workingFaceType 工作面类型
     * @param workingFaceName 工作面名称
     * @param deletedAtIsNull 是否删除
     * @return Result
     */
    @ApiOperation(value = "工作面进尺点位定义带实时值查询", notes = "工作面进尺点位定义带实时值查询")
    @GetMapping("/listWithValues")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionWithValues(
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String workingFaceName,
            Integer deletedAtIsNull
    )
    {
        Msg msg;
        try {
            WorkingFaceProgressDefinition queryEntity = new WorkingFaceProgressDefinition();
            queryEntity.setGroupCode(groupCode);
            queryEntity.setMineCode(mineCode);
            queryEntity.setWorkingFaceId(workingFaceId);
            queryEntity.setWorkingFaceType(workingFaceType);
            queryEntity.setWorkingFaceName(workingFaceName);
            queryEntity.setDeletedAtIsNull(deletedAtIsNull);

            msg = ReturnMsg.success(
                    workingFaceProgressDefinitionService.selectListWithValues(queryEntity)
            );
        }
        catch (Exception e) {
            log.error("工作面进尺点位定义带实时值查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 工作面进尺点位定义带实时值分页查询
     *
     * @param page            分页对象
     * @param groupCode       集团编码
     * @param mineCode        煤矿编码
     * @param workingFaceId   工作面ID
     * @param workingFaceType 工作面类型
     * @param workingFaceName 工作面名称
     * @param deletedAtIsNull 是否删除
     * @return Result
     */
    @ApiOperation(value = "工作面进尺点位定义带实时值分页查询", notes = "工作面进尺点位定义带实时值分页查询")
    @GetMapping("/pageWithValues")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionWithValuesPage(
            Page<WorkingFaceProgressDefinition> page,
            @RequestParam(value = "companyCode", required = false) String groupCode,
            String mineCode,
            String workingFaceId,
            String workingFaceType,
            String workingFaceName,
            Integer deletedAtIsNull
    )
    {
        Msg msg;

        try {
            WorkingFaceProgressDefinition queryEntity = new WorkingFaceProgressDefinition();
            queryEntity.setGroupCode(groupCode);
            queryEntity.setMineCode(mineCode);
            queryEntity.setWorkingFaceId(workingFaceId);
            queryEntity.setWorkingFaceType(workingFaceType);
            queryEntity.setWorkingFaceName(workingFaceName);
            queryEntity.setDeletedAtIsNull(deletedAtIsNull);

            msg = ReturnMsg.success(
                    workingFaceProgressDefinitionService.selectPageWithValues(page, queryEntity)
            );
        }
        catch (Exception e) {
            log.error("工作面进尺点位定义带实时值分页查询异常, " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 查询存在有效工作模式且启用接入的工作面
     *
     * @param workModes 工作模式
     * @return Result
     */
    @ApiOperation(value = "查询存在有效工作模式且启用接入的工作面", notes = "查询存在有效工作模式且启用接入的工作面")
    @GetMapping("/listWorkModelAssigned")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionAssignedWorkModel(@RequestParam List<String> workModes)
    {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    workingFaceProgressDefinitionService.selectWorkModelAssignedList(workModes)
            );
        }
        catch (Exception e) {
            log.error("查询存在有效工作模式且启用接入的工作面, 发生异常: " + e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 查询工作模式发生变更或者处在反装告警状态、且存在有效工作模式的工作面
     *
     * @return Result
     */
    @ApiOperation(
            value = "查询工作模式发生变更或者处在反装告警状态、且存在有效工作模式的工作面",
            notes = "查询工作模式发生变更或者处在反装告警状态、且存在有效工作模式的工作面"
    )
    @GetMapping("/listWorkModelChanged")
    public ResponseEntity<Msg> getWorkingFaceProgressDefinitionChangedWorkModel()
    {
        Msg msg;
        try {
            msg = ReturnMsg.success(
                    workingFaceProgressDefinitionService.selectWorkModelChangedList()
            );
        }
        catch (Exception e) {
            log.error("查询工作模式发生变更或者处在反装告警状态、且存在有效工作模式的工作面, 发生异常: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("query error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 同步工作面相关预警报警指标配置
     *
     * @param submitDtoList 预警报警侧指标配置
     * @return /
     */
    @ApiOperation(value = "同步工作面相关预警报警指标配置", notes = "同步工作面相关预警报警指标配置")
    @PostMapping("/indicatorSubmit")
    public ResponseEntity<Msg> submitIndicatorAliveRule(
            @RequestBody List<WorkingFaceWarnIndicatorSubmitDto> submitDtoList
    )
    {
        Msg msg;
        try {
            boolean b = workingFaceProgressDefinitionService.submitIndicatorRule(submitDtoList);
            msg = ReturnMsg.success(b);
        }
        catch (BadRequestException bre) {
            msg = ReturnMsg.fail(bre.getMessage());
        }
        catch (Exception e) {
            msg = ReturnMsg.fail("配置保存错误");
            log.error("submitIndicatorAliveRule for wrongChannelWarn, Exception: " + e.getMessage(), e);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
