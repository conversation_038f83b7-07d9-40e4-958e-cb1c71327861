package com.bdtd.modules.working_face.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WfpMineLocationManufacturer;
import com.bdtd.modules.working_face.service.IWfpMineLocationManufacturerService;
import com.bdtd.util.DateUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 煤矿定位厂家统计 控制器
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/wfpMineLocationManufacturer")
@Api(value = "wfpMineLocationManufacturer", tags = "煤矿定位厂家统计管理模块")
public class WfpMineLocationManufacturerController
{

    private final IWfpMineLocationManufacturerService wfpMineLocationManufacturerService;

    public WfpMineLocationManufacturerController(IWfpMineLocationManufacturerService wfpMineLocationManufacturerService)
    {
        this.wfpMineLocationManufacturerService = wfpMineLocationManufacturerService;
    }

    /**
     * 列表查询
     *
     * @param queryEntity 煤矿定位厂家统计
     * @return Result
     */
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWfpMineLocationManufacturerList(WfpMineLocationManufacturer queryEntity)
    {
        Msg msg;
        try {
            List<WfpMineLocationManufacturer> listResult = wfpMineLocationManufacturerService.findList(queryEntity);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param queryEntity 煤矿定位厂家统计
     * @return Result
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getWfpMineLocationManufacturerPage(
            Page<WfpMineLocationManufacturer> page,
            WfpMineLocationManufacturer queryEntity
    )
    {
        Msg msg;
        try {
            Page<WfpMineLocationManufacturer> pageResult = wfpMineLocationManufacturerService.findPage(page, queryEntity);
            msg = ReturnMsg.success(pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过 ID 查询煤矿定位厂家统计
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}")
    public ResponseEntity<Msg> getById(@PathVariable("id") Long id)
    {
        Msg msg;
        try {
            WfpMineLocationManufacturer wfpMineLocationManufacturerResult = wfpMineLocationManufacturerService.findById(id);
            msg = ReturnMsg.success(wfpMineLocationManufacturerResult);
        }
        catch (Exception e) {
            log.error("对象查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 新增煤矿定位厂家统计
     *
     * @param saveEntity 煤矿定位厂家统计
     * @return Result
     */
    @ApiOperation(value = "新增煤矿定位厂家统计", notes = "新增煤矿定位厂家统计")
    @PostMapping
    public ResponseEntity<Msg> save(@RequestBody WfpMineLocationManufacturer saveEntity)
    {
        Msg msg;
        try {
            // 确保新增场合没有id传进来
            if (saveEntity.getId() != null) {
                saveEntity.setId(null);
            }

            Boolean updateResult = wfpMineLocationManufacturerService.createOrUpdate(saveEntity);
            msg = ReturnMsg.success(updateResult);
        }
        catch (BadRequestException bre) {
            msg = ReturnMsg.fail(bre.getMessage());
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("新增时发生错误");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 修改煤矿定位厂家统计
     *
     * @param saveEntity 煤矿定位厂家统计
     * @return Result
     */
    @ApiOperation(value = "修改煤矿定位厂家统计", notes = "修改煤矿定位厂家统计")
    @PutMapping
    public ResponseEntity<Msg> updateById(@RequestBody WfpMineLocationManufacturer saveEntity)
    {
        Msg msg;
        try {
            Boolean updateResult = wfpMineLocationManufacturerService.createOrUpdate(saveEntity);
            msg = ReturnMsg.success(updateResult);
        }
        catch (BadRequestException bre) {
            msg = ReturnMsg.fail(bre.getMessage());
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("更新时发生错误");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过id删除煤矿定位厂家统计
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id删除煤矿定位厂家统计", notes = "通过id删除煤矿定位厂家统计")
    @DeleteMapping("/{id}")
    public ResponseEntity<Msg> removeById(@PathVariable Long id)
    {
        Msg msg;
        try {
            // 检查对象是否存在
            WfpMineLocationManufacturer existed = wfpMineLocationManufacturerService.getById(id);
            if (existed == null) {
                return new ResponseEntity<>(ReturnMsg.fail("删除对象不存在"), HttpStatus.OK);
            }

            // 删除时间
            existed.setDeletedAt(LocalDateTime.now());

            boolean updateResult = wfpMineLocationManufacturerService.updateById(existed);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("删除时发生错误");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 批量通过id删除煤矿定位厂家统计
     *
     * @param ids ids
     * @return Result
     */
    @ApiOperation(value = "批量通过id删除煤矿定位厂家统计", notes = "批量通过id删除煤矿定位厂家统计")
    @PostMapping("/delete")
    public ResponseEntity<Msg> removeById(@RequestBody List<Long> ids)
    {
        Msg msg;
        try {
            // 未指定删除对象
            if (ids == null || ids.isEmpty()) {
                return new ResponseEntity<>(ReturnMsg.fail("未指定删除对象"), HttpStatus.OK);
            }

            // 检查对象是否存在
            QueryWrapper<WfpMineLocationManufacturer> queryWrap = new QueryWrapper<>();
            queryWrap.in("id", ids);

            List<WfpMineLocationManufacturer> existedList = wfpMineLocationManufacturerService.list(queryWrap);
            if (existedList == null
                || existedList.isEmpty()
                || existedList.size() != ids.size()
            ) {
                return new ResponseEntity<>(ReturnMsg.fail("删除对象不存在"), HttpStatus.OK);
            }

            // 设置删除时间
            for (WfpMineLocationManufacturer existed : existedList) {
                existed.setDeletedAt(LocalDateTime.now());
            }

            boolean updateResult = wfpMineLocationManufacturerService.updateBatchById(existedList);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("批量删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("批量删除时发生错误");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @PostMapping("/export")
    @ApiOperation(value = "煤矿定位厂家统计导出", notes = "煤矿定位厂家统计导出")
    public void doExport(@RequestBody WfpMineLocationManufacturer queryEntity, HttpServletResponse response) throws IOException
    {
        List<WfpMineLocationManufacturer> resultList = wfpMineLocationManufacturerService.findList(queryEntity);

        // region 废弃代码
        // ExcelUtil<WfpMineLocationManufacturer> util = new ExcelUtil<>(WfpMineLocationManufacturer.class);
        // util.exportExcel(response, resultList, "厂家统计");
        // endregion 废弃代码

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 使用 URLEncoder.encode 防止中文乱码
        String fileName = URLEncoder.encode("煤矿定位设备厂家统计_" + DateUtil.getDateTimeString("yyyy-MM-dd_HH:mm:ss"), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), WfpMineLocationManufacturer.class)
                 .excelType(ExcelTypeEnum.XLSX)
                 .sheet("煤矿定位设备厂家")
                 .doWrite(resultList);
    }

}
