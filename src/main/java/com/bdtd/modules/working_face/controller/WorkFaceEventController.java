package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bdtd.modules.working_face.entity.WorkingFaceEvent;
import com.bdtd.modules.working_face.service.IWorkingFaceEventService;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Slf4j
@RestController
@RequestMapping("/others/workFaceEvent")
@Api(tags = "workFaceEvent")
public class WorkFaceEventController
{
    @Autowired
    private IWorkingFaceEventService workFaceEventService;

    /**
     * 列表查询
     *
     * @param workFaceId   工作面ID
     * @param workFaceType 工作面类型
     * @param evenType     事件类型
     * @param recordType   记录类型（event、state）
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return Result
     */
    @ApiOperation(value = "列表查询", tags = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkFaceEventList(
            @RequestParam(required = false) String workFaceId,
            String workFaceType,
            String evenType,
            String recordType,
            String startTime,
            String endTime
    )
    {
        Msg msg;
        try {
            List<WorkingFaceEvent> listResult = workFaceEventService.selectWorkFaceEventList(workFaceId, workFaceType, evenType, recordType, startTime, endTime);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }


    /**
     * 分页查询
     *
     * @param workFaceId   工作面ID
     * @param workFaceType 工作面类型
     * @param evenType     事件类型
     * @param recordType   记录类型（event、state）
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param pageSize     分页大小
     * @param pageIndex    当前页吗
     * @return Result
     */
    @ApiOperation(value = "分页查询", tags = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getWorkFaceEventPage(
            @RequestParam(required = false) String workFaceId,
            String workFaceType,
            String evenType,
            String recordType,
            String startTime,
            String endTime,
            Integer pageSize,
            Integer pageIndex
    )
    {
        Msg msg;
        try {
            IPage<WorkingFaceEvent> pageResult = workFaceEventService.selectWorkFaceEventPage(
                    workFaceId, workFaceType, evenType, recordType, startTime, endTime, pageSize, pageIndex);
            msg = ReturnMsg.success(pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 新增
     *
     * @param workingFaceEventList 时间记录
     * @return Result
     */
    @ApiOperation(value = "新增记录", tags = "新增记录")
    @PostMapping("/createRecord")
    public ResponseEntity<Msg> creatRecord(@RequestBody List<WorkingFaceEvent> workingFaceEventList)
    {
        Msg msg;
        try {
            int saveResult = workFaceEventService.createRecord(workingFaceEventList);
            msg = ReturnMsg.success(true);
        }
        catch (BadRequestException bre) {
            log.error("事件记录格式有误, " + bre.getMessage(), bre);
            msg = ReturnMsg.fail(bre.getMessage(), null);
        }
        catch (Exception e) {
            log.error("新增记录异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 修改
     *
     * @param workingFaceEventEntity 事件实体
     * @return Result
     */
    @ApiOperation(value = "修改", tags = "修改")
    @PutMapping
    public ResponseEntity<Msg> updateById(@RequestBody WorkingFaceEvent workingFaceEventEntity)
    {
        Msg msg;
        try {
            // 是否存在
            WorkingFaceEvent existed = workFaceEventService.getById(workingFaceEventEntity.pkVal());
            if (existed == null) {
                return new ResponseEntity<>(ReturnMsg.fail("更新对象不存在"), HttpStatus.OK);
            }

            boolean updateResult = workFaceEventService.updateById(workingFaceEventEntity);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过id删除
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id删除", tags = "通过id删除")
    @DeleteMapping("/{id}")
    public ResponseEntity<Msg> removeById(@PathVariable Integer id)
    {
        Msg msg;
        try {
            boolean delResult = workFaceEventService.removeById(id);
            msg = ReturnMsg.success(delResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
