package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.base.entity.User;
import com.bdtd.modules.conf_distribution.util.BackendUtil;
import com.bdtd.modules.working_face.entity.WfpLocationManufacturer;
import com.bdtd.modules.working_face.service.IWfpLocationManufacturerService;
import com.bdtd.util.StringUtil;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 定位厂家 控制器
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Slf4j
@RestController
@RequestMapping("/workingFace/wfpLocationManufacturer")
@Api(value = "wfpLocationManufacturer", tags = "定位厂家管理模块")
public class WfpLocationManufacturerController
{

    private final IWfpLocationManufacturerService wfpLocationManufacturerService;

    public WfpLocationManufacturerController(IWfpLocationManufacturerService wfpLocationManufacturerService)
    {
        this.wfpLocationManufacturerService = wfpLocationManufacturerService;
    }

    /**
     * 列表查询
     *
     * @param queryEntity 定位厂家
     * @return Result
     */
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWfpLocationManufacturerList(WfpLocationManufacturer queryEntity)
    {
        Msg msg;
        try {
            List<WfpLocationManufacturer> listResult = wfpLocationManufacturerService.list(generateQueryWrapper(queryEntity));
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param queryEntity 定位厂家
     * @return Result
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getWfpLocationManufacturerPage(Page<WfpLocationManufacturer> page, WfpLocationManufacturer queryEntity)
    {
        Msg msg;
        try {
            Page<WfpLocationManufacturer> pageResult = wfpLocationManufacturerService.page(page, generateQueryWrapper(queryEntity));
            msg = ReturnMsg.success(pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过 ID 查询定位厂家
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/{id}")
    public ResponseEntity<Msg> getById(@PathVariable("id") Long id)
    {
        Msg msg;
        try {
            WfpLocationManufacturer resultEntity = wfpLocationManufacturerService.getById(id);
            msg = ReturnMsg.success(resultEntity);
        }
        catch (Exception e) {
            log.error("对象查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 新增定位厂家
     *
     * @param saveEntity 定位厂家
     * @return Result
     */
    @ApiOperation(value = "新增定位厂家", notes = "新增定位厂家")
    @PostMapping
    public ResponseEntity<Msg> save(@RequestBody WfpLocationManufacturer saveEntity)
    {
        Msg msg;
        try {
            // 确保新增场合没有id传进来
            if (saveEntity.getId() != null) {
                saveEntity.setId(null);
            }

            if (StringUtil.isEmpty(saveEntity.getName())) {
                return new ResponseEntity<>(ReturnMsg.fail("名称不能为空"), HttpStatus.OK);
            }

            QueryWrapper<WfpLocationManufacturer> qw = new QueryWrapper<>();
            qw.eq("name", saveEntity.getName());
            WfpLocationManufacturer existed = wfpLocationManufacturerService.getOne(qw);
            if (existed != null) {
                return new ResponseEntity<>(ReturnMsg.fail("厂家已存在"), HttpStatus.OK);
            }

            // 取得当前用户ID
            User currentUser = null;
            try {
                Long userId = BackendUtil.getInstance().getCurrentUserId();
                currentUser = BackendUtil.getInstance().getUserById(userId);
            }
            catch (Exception e) {
                log.warn("get currentUser from header failed: {}", e.getMessage(), e);
            }
            if (currentUser != null) {
                saveEntity.setCreateUser(currentUser.getAccount());
            }

            // 新增信息
            saveEntity.setCreateTime(LocalDateTime.now());

            boolean saveResult = wfpLocationManufacturerService.save(saveEntity);
            msg = ReturnMsg.success(saveResult);
        }
        catch (Exception e) {
            log.error("新增保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 修改定位厂家
     *
     * @param updateEntity 定位厂家
     * @return Result
     */
    @ApiOperation(value = "修改定位厂家", notes = "修改定位厂家")
    @PutMapping
    public ResponseEntity<Msg> updateById(@RequestBody WfpLocationManufacturer updateEntity)
    {
        Msg msg;
        try {
            if (updateEntity.getId() == null) {
                return new ResponseEntity<>(ReturnMsg.fail("ID不能为空"), HttpStatus.OK);
            }
            if (StringUtil.isEmpty(updateEntity.getName())) {
                return new ResponseEntity<>(ReturnMsg.fail("名称不能为空"), HttpStatus.OK);
            }

            QueryWrapper<WfpLocationManufacturer> qw = new QueryWrapper<>();
            qw.eq("name", updateEntity.getName());
            qw.ne("id", updateEntity.getId());
            WfpLocationManufacturer existed = wfpLocationManufacturerService.getOne(qw);
            if (existed != null) {
                return new ResponseEntity<>(ReturnMsg.fail("已存在相同名称的厂家"), HttpStatus.OK);
            }

            // 取得当前用户ID
            User currentUser = null;
            try {
                Long userId = BackendUtil.getInstance().getCurrentUserId();
                currentUser = BackendUtil.getInstance().getUserById(userId);
            }
            catch (Exception e) {
                log.warn("get currentUser from header failed: {}", e.getMessage(), e);
            }
            if (currentUser != null) {
                updateEntity.setUpdateUser(currentUser.getAccount());
            }

            // UpdateWrapper<WfpLocationManufacturer> updateWrapper = new UpdateWrapper<>();
            // updateWrapper.eq("id", updateEntity.getId());

            // 更新信息
            updateEntity.setUpdateTime(LocalDateTime.now());
            // 删除时间
            updateEntity.setDeletedAt(null);

            boolean updateResult = wfpLocationManufacturerService.updateById(updateEntity);
            // boolean updateResult = wfpLocationManufacturerService.update(updateEntity, updateWrapper);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过id删除定位厂家
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id删除定位厂家", notes = "通过id删除定位厂家")
    @DeleteMapping("/{id}")
    public ResponseEntity<Msg> removeById(@PathVariable Long id)
    {
        Msg msg;
        try {
            // 检查对象是否存在
            WfpLocationManufacturer existed = wfpLocationManufacturerService.getById(id);
            if (existed == null) {
                return new ResponseEntity<>(ReturnMsg.fail("删除对象不存在"), HttpStatus.OK);
            }

            // 删除时间
            existed.setDeletedAt(LocalDateTime.now());

            boolean updateResult = wfpLocationManufacturerService.updateById(existed);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    private QueryWrapper<WfpLocationManufacturer> generateQueryWrapper(WfpLocationManufacturer queryEntity)
    {
        QueryWrapper<WfpLocationManufacturer> queryWrap = Wrappers.query(queryEntity);
        queryWrap.like(StringUtil.isNotEmpty(queryEntity.getName()), "name", String.format("%%%s%%", queryEntity.getName()));
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatSignal()),
                "feat_signal",
                queryEntity.getFeatSignal()
        );
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatPower()),
                "feat_power",
                queryEntity.getFeatPower()
        );
        queryWrap.eq(
                Arrays.asList(WfpLocationManufacturer.FEATURE_VALUES).contains(queryEntity.getFeatPowerAlarm()),
                "feat_power_alarm",
                queryEntity.getFeatPowerAlarm()
        );

        queryWrap.isNull(
                queryEntity.getDeletedAtIsNull() == null || Objects.equals(1, queryEntity.getDeletedAtIsNull()),
                "deleted_at"
        );
        queryWrap.isNotNull(
                Objects.equals(0, queryEntity.getDeletedAtIsNull()),
                "deleted_at"
        );

        queryWrap.orderByAsc("id");

        return queryWrap;
    }
}
