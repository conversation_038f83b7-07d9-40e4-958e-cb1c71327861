package com.bdtd.modules.working_face.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.working_face.entity.WorkingFaceLocatorDevice;
import com.bdtd.modules.working_face.service.IWorkingFaceLocatorDeviceService;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作面设备信息表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Slf4j
@RestController
@RequestMapping("/working_face/workingFaceLocatorDevice")
@Api("workingFaceLocatorDevice")
public class WorkingFaceLocatorDeviceController {

    private final IWorkingFaceLocatorDeviceService workingFaceLocatorDeviceService;

    public WorkingFaceLocatorDeviceController(IWorkingFaceLocatorDeviceService workingFaceLocatorDeviceService) {
        this.workingFaceLocatorDeviceService = workingFaceLocatorDeviceService;
    }

    /**
     * 列表查询
     *
     * @param workingFaceLocatorDeviceEntity 工作面设备信息表
     * @return Result
     */
    @ApiOperation(value = "列表查询", tags = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getWorkingFaceLocatorDeviceList(WorkingFaceLocatorDevice workingFaceLocatorDeviceEntity) {
        Msg msg;
        try {
            QueryWrapper<WorkingFaceLocatorDevice> queryWrap = Wrappers.query(workingFaceLocatorDeviceEntity);
            queryWrap.orderByAsc("id");

            List<WorkingFaceLocatorDevice> listResult = workingFaceLocatorDeviceService.list(queryWrap);
            msg = ReturnMsg.success(listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page                           分页对象
     * @param workingFaceLocatorDeviceEntity 工作面设备信息表
     * @return Result
     */
    @ApiOperation(value = "分页查询", tags = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getWorkingFaceLocatorDevicePage(Page<WorkingFaceLocatorDevice> page, WorkingFaceLocatorDevice workingFaceLocatorDeviceEntity) {
        Msg msg;
        try {
            Page<WorkingFaceLocatorDevice> pageResult = workingFaceLocatorDeviceService.page(page, Wrappers.query(workingFaceLocatorDeviceEntity));
            msg = ReturnMsg.success(pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过 ID 查询工作面设备信息表
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过 ID 查询", tags = "通过 ID 查询")
    @GetMapping("/{id}")
    public ResponseEntity<Msg> getById(@PathVariable("id") Integer id) {
        Msg msg;
        try {
            WorkingFaceLocatorDevice workingFaceLocatorDeviceResult = workingFaceLocatorDeviceService.getById(id);
            msg = ReturnMsg.success(workingFaceLocatorDeviceResult);
        }
        catch (Exception e) {
            log.error("对象查询异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 新增工作面设备信息表
     *
     * @param workingFaceLocatorDeviceEntity 工作面设备信息表
     * @return Result
     */
    @ApiOperation(value = "新增工作面设备信息表", tags = "新增工作面设备信息表")
    @PostMapping
    public ResponseEntity<Msg> save(@RequestBody WorkingFaceLocatorDevice workingFaceLocatorDeviceEntity) {
        Msg msg;
        try {
            // 是否存在
            WorkingFaceLocatorDevice existed = workingFaceLocatorDeviceService.getById(workingFaceLocatorDeviceEntity.getDeviceCode());
            if (existed != null) {
                return new ResponseEntity<>(ReturnMsg.fail("对象已存在"), HttpStatus.OK);
            }

            boolean saveResult = workingFaceLocatorDeviceService.save(workingFaceLocatorDeviceEntity);
            msg = ReturnMsg.success(saveResult);
        }
        catch (Exception e) {
            log.error("新增保存异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 修改工作面设备信息表
     *
     * @param workingFaceLocatorDeviceEntity 工作面设备信息表
     * @return Result
     */
    @ApiOperation(value = "修改工作面设备信息表", tags = "修改工作面设备信息表")
    @PutMapping
    public ResponseEntity<Msg> updateById(@RequestBody WorkingFaceLocatorDevice workingFaceLocatorDeviceEntity) {
        Msg msg;
        try {
            // 是否存在
            WorkingFaceLocatorDevice existed = workingFaceLocatorDeviceService.getById(workingFaceLocatorDeviceEntity.getDeviceCode());
            if (existed == null) {
                return new ResponseEntity<>(ReturnMsg.fail("更新对象不存在"), HttpStatus.OK);
            }

            boolean updateResult = workingFaceLocatorDeviceService.updateById(workingFaceLocatorDeviceEntity);
            msg = ReturnMsg.success(updateResult);
        }
        catch (Exception e) {
            log.error("更新保存异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 通过id删除工作面设备信息表
     *
     * @param id id
     * @return Result
     */
    @ApiOperation(value = "通过id删除工作面设备信息表", tags = "通过id删除工作面设备信息表")
    @DeleteMapping("/{id}")
    public ResponseEntity<Msg> removeById(@PathVariable Integer id) {
        Msg msg;
        try {
            boolean delResult = workingFaceLocatorDeviceService.removeById(id);
            msg = ReturnMsg.success(delResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: {}", e.getMessage(), e);
            msg = ReturnMsg.fail("error");
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
