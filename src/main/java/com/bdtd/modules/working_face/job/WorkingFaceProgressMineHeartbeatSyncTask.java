package com.bdtd.modules.working_face.job;

import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressMineHeartbeatService;
import com.bdtd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 从人员定位侧同步矿端采集链路心跳
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnExpression("!T(com.bdtd.util.StringUtil).isEmpty('${scheduled.position-mine-heartbeat-sync}')")
public class WorkingFaceProgressMineHeartbeatSyncTask
{
    @Autowired
    IWorkingFaceProgressMineHeartbeatService mineHeartbeatService;
    @Autowired
    IServiceCallLogService serviceCallLogService;

    @Value("${scheduled.position-mine-heartbeat-check-field:update_time}")
    private String hbStateCheckField;

    @Scheduled(cron = "${scheduled.position-mine-heartbeat-sync}")
    @Transactional(rollbackFor = Exception.class)
    public void syncPositionMineHeartbeat()
    {
        String actionStr = "定时同步矿端采集链路心跳";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 同步数据
        String startDate = null, endDate = null;
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        long startMs = System.currentTimeMillis();
        log.info("syncPositionMineHeartbeat started at: " + LocalDateTime.now().format(dtf));
        try {
            // 获取最新的更新时间
            Timestamp latestTs = mineHeartbeatService.getLatestSyncTime(new PositionCalcQueryCriteria());

            // 当前时间
            LocalDateTime nowLdt = LocalDateTime.now();
            // 获取上之前获取到的时间
            if (latestTs == null) {
                startDate = dtf.format(nowLdt.minusMinutes(10L));
                endDate = dtf.format(nowLdt);
            }
            else {
                LocalDateTime latestLdt = latestTs
                        .toInstant()
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .toLocalDateTime();
                startDate = dtf.format(latestLdt);
                if (latestLdt.plusMinutes(10L).isBefore(nowLdt)) {
                    endDate = dtf.format(latestLdt.plusMinutes(10L));
                }
                else {
                    endDate = dtf.format(nowLdt);
                }
            }

            // 同步查询条件
            PositionCalcQueryCriteria syncQueryCriteria = new PositionCalcQueryCriteria();
            syncQueryCriteria.setStartDate(startDate);
            syncQueryCriteria.setEndDate(endDate);

            // 同步历史数据
            PositionCalcSyncResult result = mineHeartbeatService.syncPositionMineHeartbeat(
                    syncQueryCriteria,
                    actionStr
            );
            if (result != null
                && result.getSyncLogs() != null
                && !result.getSyncLogs().isEmpty()
            ) {
                serviceCallLogs.addAll(result.getSyncLogs());
            }

            // 检查在线状态
            if (StringUtil.isNotEmpty(hbStateCheckField)) {
                mineHeartbeatService.checkPositionMineHeartbeat(hbStateCheckField, syncQueryCriteria);
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 获取最近更新时间失败",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);
        }
        finally {
            try {
                // 记录服务日志
                if (!serviceCallLogs.isEmpty()) {
                    serviceCallLogService.createLogs(serviceCallLogs);
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }

            log.info("syncPositionMineHeartbeat ends, cost: {} ms", (System.currentTimeMillis() - startMs));
        }
    }

}
