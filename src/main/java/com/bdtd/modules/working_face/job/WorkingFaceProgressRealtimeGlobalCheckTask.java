package com.bdtd.modules.working_face.job;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.feign.PushMsgFeignService;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressRealtimeService;
import com.bdtd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 进尺测距数据采集全局在线检查
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnExpression("T(com.bdtd.util.StringUtil).isNotEmpty('${scheduled.progress-realtime-global-check}')")
public class WorkingFaceProgressRealtimeGlobalCheckTask
{
    @Value("${scheduled.progress-realtime-global-check-threshold:5}")
    private Integer globalCheckThreshold;
    @Value("${scheduled.progress-realtime-global-check-notify-phone}")
    private String notifyPhone;

    private final PushMsgFeignService pushMsgFeignService;
    private final IWorkingFaceProgressRealtimeService progressRealtimeService;

    public WorkingFaceProgressRealtimeGlobalCheckTask(
            PushMsgFeignService pushMsgFeignService,
            IWorkingFaceProgressRealtimeService progressRealtimeService
    )
    {
        this.pushMsgFeignService = pushMsgFeignService;
        this.progressRealtimeService = progressRealtimeService;
    }

    @Scheduled(cron = "${scheduled.progress-realtime-global-check}")
    public void doProgressRealtimeGlobalCheck()
    {
        log.info("progressRealtimeGlobalCheck start");

        long startMs = System.currentTimeMillis();
        try {
            // 是否正常
            boolean isNormal = progressRealtimeService.doRealtimeGlobalCheck();
            if (isNormal) {
                return;
            }

            // 未设置接收手机号, 不发送短信
            if (StringUtil.isEmpty(notifyPhone)) {
                log.info("progressRealtimeGlobalCheck, no notify phone set, skip sending sms.");
                return;
            }

            List<String> phones = new ArrayList<>();
            List<String> names = new ArrayList<>();

            String[] phoneItems = notifyPhone.split(",");
            for (String item : phoneItems) {
                String[] secArr = item.split("\\|");
                if (secArr.length > 0 && StringUtil.isNotEmpty(secArr[0])) {
                    phones.add(secArr[0]);
                }
                else {
                    continue;
                }
                if (secArr.length > 1 && StringUtil.isNotEmpty(secArr[1])) {
                    try {
                        byte[] decodedBytes = Base64.getDecoder().decode(secArr[1]);
                        names.add(new String(decodedBytes));
                    }
                    catch (Exception ex) {
                        log.warn("progressRealtimeGlobalCheck, decode sms send name failed: {}", ex.getMessage(), ex);
                        names.add(secArr[0]);
                    }
                }
                else {
                    names.add(secArr[0]);
                }
            }

            // 发送短信
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(
                    "content",
                    String.format("注意：集团生产动态系统已经超过 %d 分钟未接收到新数据, 请关注处理。", globalCheckThreshold)
            );
            paramMap.put("phone", StringUtil.join(phones, ","));
            paramMap.put("personName", StringUtil.join(names, ","));

            JSONObject res = pushMsgFeignService.sendSms(paramMap);
            if (res != null) {
                log.info(
                        "progressRealtimeGlobalCheck, sms send params: {}, result: {}",
                        JSONObject.toJSONString(paramMap),
                        res.toJSONString()
                );
            }
        }
        catch (Exception ex) {
            log.error("progressRealtimeGlobalCheck failed: {}", ex.getMessage(), ex);
        }
        finally {
            log.info("progressRealtimeGlobalCheck end, cost {} ms", (System.currentTimeMillis() - startMs));
        }
    }

}
