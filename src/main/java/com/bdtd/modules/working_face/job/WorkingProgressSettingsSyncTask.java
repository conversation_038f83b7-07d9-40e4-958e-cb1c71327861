package com.bdtd.modules.working_face.job;

import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dao.WorkingFaceProgressSettingMapper;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 从业务侧同步工作面信息到接入系统（已废弃，工作面配置统一从GIS获取）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnExpression("!T(com.bdtd.util.StringUtil).isEmpty('${scheduled.working-progress-settings-sync}')")
public class WorkingProgressSettingsSyncTask
{

    @Autowired
    private WorkingFaceProgressSettingMapper workingFaceProgressSettingMapper;

    @Autowired
    IServiceCallLogService serviceCallLogService;
    @Autowired
    IWorkingFaceProgressSettingService workingFaceProgressSettingService;

    /** 同步模式: new 同步最近更新的工作面, all 全量同步 */
    @Value("${scheduled.working-progress-settings-sync-mode}")
    private String settingSyncMode;

    @Scheduled(cron = "${scheduled.working-progress-settings-sync}")
    @Transactional(rollbackFor = Exception.class)
    public void syncModelSystems() {
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        String actionStr = "定时更新配置调用业务侧配置接口";
        try {
            List<ServiceCallLog> processLogs = null;

            // 同步最新的工作面
            if ("new".equals(settingSyncMode)) {
                actionStr = "定时更新配置调用业务侧配置接口(增量)";

                // 获取最新的更新时间
                Long latestUpdateTime = workingFaceProgressSettingMapper.selectLatestUpdateTime();
                // 同步矿井配置
                // 已废弃，工作面配置统一从GIS获取
                processLogs = workingFaceProgressSettingService.syncProgressSettings(
                        null,
                        // 配置是否包含煤矿全部工作面
                        false,
                        // 工作面配置信息是否包含工作模式
                        false,
                        latestUpdateTime,
                        actionStr
                );
            }
            else {
                actionStr = "定时更新配置调用业务侧配置接口(全量)";

                // 同步全部矿井配置
                processLogs = workingFaceProgressSettingService.syncProgressSettings(
                        null,
                        // 配置是否包含煤矿全部工作面
                        true,
                        // 工作面配置信息是否包含工作模式
                        false,
                        null,
                        actionStr
                );
            }

            if (processLogs != null && !processLogs.isEmpty()) {
                serviceCallLogs.addAll(processLogs);
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 同步下发配置失败",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);
        }
        finally {
            try {
                // 记录服务日志
                if (!serviceCallLogs.isEmpty()) {
                    serviceCallLogService.createLogs(serviceCallLogs);
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

}
