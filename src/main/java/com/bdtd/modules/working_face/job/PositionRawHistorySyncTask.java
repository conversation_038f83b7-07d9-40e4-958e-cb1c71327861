package com.bdtd.modules.working_face.job;

import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.PositionCalcQueryCriteria;
import com.bdtd.modules.working_face.dto.PositionCalcSyncResult;
import com.bdtd.modules.working_face.service.IPositionRawHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 从人员定位侧同步解算原始数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnExpression("!T(com.bdtd.util.StringUtil).isEmpty('${scheduled.position-raw-history-sync}')")
public class PositionRawHistorySyncTask {
    final IPositionRawHistoryService positionRawHistoryService;
    final IServiceCallLogService serviceCallLogService;

    @Resource(name = "positionHistoryLastSyncTimeMap")
    private ConcurrentHashMap<String, LocalDateTime> positionHistoryLastSyncTimeMap;

    @Value("${scheduled.position-raw-history-sync-cron-minutes}")
    private long cronMinutes;
    @Value("${scheduled.position-raw-history-sync-tolerance-minutes}")
    private long toleranceMinutes;
    @Value("${scheduled.position-raw-history-sync-max-minutes}")
    private long syncMaxMinutes;

    public PositionRawHistorySyncTask(
            IPositionRawHistoryService positionRawHistoryService,
            IServiceCallLogService serviceCallLogService
    )
    {
        this.positionRawHistoryService = positionRawHistoryService;
        this.serviceCallLogService = serviceCallLogService;
    }

    @Scheduled(cron = "${scheduled.position-raw-history-sync}")
    @Transactional(rollbackFor = Exception.class)
    public void syncPositionRawHistory() {
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();
        String actionStr = "定时同步定位解算原始数据";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String latestTsSrc;

        try {
            // 获取最新的更新时间
            LocalDateTime latestLdt = null;
            if (positionHistoryLastSyncTimeMap.containsKey("raw") && positionHistoryLastSyncTimeMap.get("raw") != null) {
                latestLdt = positionHistoryLastSyncTimeMap.get("raw");
                latestTsSrc = "cache";
            }
            else {
                // 不再使用同步到时间，和服务器时间可能不同步
                // latestTs = positionRawHistoryService.getLatestSyncTime(new PositionCalcQueryCriteria());
                // latestTsSrc = "db query";
                latestTsSrc = "program";
            }

            // 当前时间
            LocalDateTime nowLdt = LocalDateTime.now();
            LocalDateTime startLdt = null, endLdt = null;

            // 存在同步失败的时间
            if (latestLdt != null) {
                // 开始时间冗余
                startLdt = latestLdt;
                // 限制同步数量，防止同步失败
                endLdt = latestLdt.plusMinutes(syncMaxMinutes);
            }

            // 1）默认同步范围
            // 2）开始时间必须早于当前时间
            // => 同步过去两个同步周期内的数据
            if (startLdt == null || !startLdt.isBefore(nowLdt)) {
                startLdt = nowLdt.minusMinutes(cronMinutes * 2 + toleranceMinutes);
                endLdt = nowLdt;
            }

            String startDate = dtf.format(startLdt);
            String endDate = dtf.format(endLdt);
            log.info(
                    "Raw pos data sync start: {} - {}, latestLdt(from {}): {}, nowTs: {}, toleranceMinutes: {}, syncMaxMinutes: {}",
                    startDate,
                    endDate,
                    latestTsSrc,
                    (latestLdt == null ? null : dtf.format(latestLdt)),
                    dtf.format(nowLdt),
                    toleranceMinutes,
                    syncMaxMinutes
            );

            // 同步查询条件
            PositionCalcQueryCriteria syncQueryCriteria = new PositionCalcQueryCriteria();
            syncQueryCriteria.setStartDate(startDate);
            syncQueryCriteria.setEndDate(endDate);

            // 同步原始数据
            PositionCalcSyncResult result = positionRawHistoryService.syncPositionRawHistory(
                    syncQueryCriteria,
                    actionStr,
                    null,
                    true,
                    false
            );
            // 返回数据不为空，清空缓存的最后同步时间，继续从历史库取得最后同步时间
            if (result != null && result.isSucceed() && result.getCount() > 0) {
                if (positionHistoryLastSyncTimeMap.containsKey("raw")) {
                    positionHistoryLastSyncTimeMap.remove("raw");
                    log.info("Raw pos data sync succeed, clear endLdt in cache.");
                }
            }
            else if (endLdt.isBefore(nowLdt)) {
                positionHistoryLastSyncTimeMap.put("raw", endLdt);
                log.info("Raw pos data sync failed or empty result, endLdt is before nowLdt, set endLdt to cache.");
            }
            else {
                positionHistoryLastSyncTimeMap.put("raw", startLdt);
                log.info("Raw pos data sync failed or empty result, set startLdt to cache.");
            }

            if (result != null && result.getSyncLogs() != null && !result.getSyncLogs().isEmpty()) {
                serviceCallLogs.addAll(result.getSyncLogs());
            }
        }
        catch (Exception ex) {
            serviceCallLogs.add(ServiceCallLog.createNew(
                    actionStr + " 获取最近更新时间失败",
                    (byte) 1,
                    "0"
            ));
            log.error(ex.getMessage(), ex);
        }
        finally {
            try {
                // 记录服务日志
                if (!serviceCallLogs.isEmpty()) {
                    serviceCallLogService.createLogs(serviceCallLogs);
                }
            }
            catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

}
