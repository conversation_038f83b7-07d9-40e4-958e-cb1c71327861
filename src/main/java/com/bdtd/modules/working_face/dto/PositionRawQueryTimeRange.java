package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 定位解算数据查询时间区间对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "定位解算数据查询时间区间对象", description = "定位解算数据查询时间区间对象")
public class PositionRawQueryTimeRange implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    private Timestamp firstTs;

    @ApiModelProperty(value = "结束时间")
    private Timestamp lastTs;

}
