package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 下发工作面配置缓存处理结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "下发工作面配置缓存处理结果", description = "下发工作面配置缓存处理结果")
public class WorkingFaceSettingCacheResult implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "缓存ID")
    private Long settingId;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "更新时间")
    private Long updateTime;
}
