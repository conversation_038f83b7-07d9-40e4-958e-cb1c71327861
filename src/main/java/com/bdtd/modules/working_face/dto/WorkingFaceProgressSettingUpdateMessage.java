package com.bdtd.modules.working_face.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 工作面进尺配置更新消息
 *
 * <AUTHOR>
 */
@Data
public class WorkingFaceProgressSettingUpdateMessage implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 集团编码 */
    private String groupCode;

    /** 二级公司编码 */
    private String companyCode;

    /** 煤矿编码 */
    private String mineCode;

    /** 配置更新时间 */
    private Long changeTime;

    /** 是否强制更新子系统配置信息 */
    private String forceUpdateModelSystem;

}