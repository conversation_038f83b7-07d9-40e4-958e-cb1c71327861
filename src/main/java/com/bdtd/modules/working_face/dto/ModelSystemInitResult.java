package com.bdtd.modules.working_face.dto;

import com.bdtd.modules.monitor.entity.ServiceCallLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 接入系统同步结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "接入系统同步结果", description = "接入系统同步结果")
public class ModelSystemInitResult implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "错误信息列表")
    private List<String> errors = new ArrayList<>();

    @ApiModelProperty(value = "接口调用日志")
    private List<ServiceCallLog> serviceLogs = new ArrayList<>();

}
