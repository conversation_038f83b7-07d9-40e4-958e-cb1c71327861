package com.bdtd.modules.working_face.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 工作面监测汇总信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkingFaceLocatorStatusComposite implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 集团编码（二级公司） */
    @ApiModelProperty(value = "集团编码（二级公司）")
    private String groupCode;

    /** 集团名称（二级公司） */
    @ApiModelProperty(value = "集团名称（二级公司）")
    private String groupName;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    private String workingFaceId;

    /** 工作面名称 */
    @ApiModelProperty(value = "工作面名称")
    private String workingFaceName;

    /** 工作面ID列表 */
    @ApiModelProperty(value = "工作面ID列表")
    @JsonIgnore
    private List<String> workingFaceIdList;

    /**
     * 工作模式
     * 1：固定基站+定位标签
     * 2：固定靶标+移动基站
     * 3：靶标标签+移动基站+定位标签
     * 4：定位标签+移动双基站+靶标标签
     */
    @ApiModelProperty(value = "工作模式")
    private String workMode;

    /**
     * 工作面状态
     */
    @ApiModelProperty(value = "工作面状态")
    private String workState;

    /** 标签卡编码 */
    @ApiModelProperty(value = "标签卡编码")
    private String labelCode;

    /** 测距值 */
    @ApiModelProperty(value = "测距值")
    private String dist;

    /** 电量值 */
    @ApiModelProperty(value = "电量值")
    private String powerValue;

    /** 电量告警 */
    @ApiModelProperty(value = "电量告警")
    private String powerAlarm;

    /** 信号值 */
    @ApiModelProperty(value = "信号值")
    private String signalValue;

    /** 支持信号 */
    @ApiModelProperty(value = "支持信号, 0不支持，1支持")
    private String featSignal;

    /** 支持电量 */
    @ApiModelProperty(value = "支持电量, 0不支持，1支持")
    private String featPower;

    /** 支持电量告警 */
    @ApiModelProperty(value = "支持电量告警, 0不支持，1支持")
    private String featPowerAlarm;

    /** 采集值状态 */
    @ApiModelProperty(value = "采集值状态")
    private String state;

    /** 数据时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 设备类型 1 定位卡 2 靶标卡 3 基站 */
    @ApiModelProperty(value = "设备类型 1 定位卡 2 靶标卡 3 基站")
    private Integer deviceType;

}
