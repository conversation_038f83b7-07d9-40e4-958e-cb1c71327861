package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 定位解算原始数据（按时间窗口分组）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@Accessors(chain = true)
public class PositionRawHistoryInterval implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间窗口")
    private Timestamp ts;

    /** 值同 ts 字段 */
    @Deprecated
    @ApiModelProperty(value = "时间戳")
    private Timestamp updateTime;

    @ApiModelProperty(value = "坐标X")
    private String x;

    @ApiModelProperty(value = "坐标Y")
    private String y;

    @ApiModelProperty(value = "电量值")
    private Integer powerValue;

    @ApiModelProperty(value = "信号值")
    private Integer signalValue;

    @ApiModelProperty(value = "是否离线")
    private String offline;

    @ApiModelProperty(value = "通道")
    private Integer channel;

    @ApiModelProperty(value = "距离")
    private Integer distance;

    @ApiModelProperty(value = "数据时间")
    private Timestamp dataTime;

    @ApiModelProperty(value = "原始消息内容")
    private String mqInfo;

    @ApiModelProperty(value = "入库时间")
    private Timestamp updatedAt;

    @ApiModelProperty(value = "标签卡编号")
    private String cardNumber;

    @ApiModelProperty(value = "定位基站编码")
    private String stationCode;

    @ApiModelProperty(value = "工作面ID")
    private String workFaceId;

}
