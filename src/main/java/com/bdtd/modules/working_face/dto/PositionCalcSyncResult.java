package com.bdtd.modules.working_face.dto;

import com.alibaba.fastjson.JSONObject;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定位解算数据同步结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "定位解算数据同步结果", description = "定位解算数据同步结果")
public class PositionCalcSyncResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "同步数量")
    private int count = 0;

    @ApiModelProperty(value = "是否成功")
    private boolean succeed = true;

    @ApiModelProperty(value = "接口调用日志")
    private List<ServiceCallLog> syncLogs;

    @ApiModelProperty(value = "原始数据写入列表")
    private List<PositionRawHistory> rawInsertList;
    @ApiModelProperty(value = "工作面更新列表")
    private List<WorkingFaceProgressDefinition> wfpdUpdateList;
    @ApiModelProperty(value = "工作面反装报警列表")
    private List<WrongChannelCheckDetail> wrongChannelList;
    @ApiModelProperty(value = "天线反装分析状态 Map, 工作面 ID => [ 报警状态(1,开始时间,结束时间), 报警状态(0,开始时间,结束时间) ]")
    private Map<String, List<WrongChannelCheckDetail>> wrongChannelStateMap;
    @ApiModelProperty(value = "推送预警报警列表")
    private List<JSONObject> pushWarnObjList;

}
