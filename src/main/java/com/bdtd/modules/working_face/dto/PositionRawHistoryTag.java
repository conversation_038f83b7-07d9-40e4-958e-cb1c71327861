package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 定位解算原始数据标签卡编号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PositionRawHistoryTag implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标签卡编号")
    private String cardNumber;

    @ApiModelProperty(value = "定位基站编码")
    private String stationCode;

    @ApiModelProperty(value = "工作面名称")
    private String workFaceName;

    @ApiModelProperty(value = "工作面ID")
    private String workFaceId;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    /**
     * 获取对应的普通表名
     *
     * @return /
     */
    public String getTableName(String stableName) {
        return String.format(
                "%s_%s_%s_%s",
                stableName,
                this.workFaceId,
                this.stationCode,
                this.cardNumber
        );
    }

}
