package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 下发基础工作面信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "下发基础工作面信息", description = "下发基础工作面信息")
public class WorkingFaceSyncInfo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "工作面ID")
    private String wfId;

    @ApiModelProperty(value = "工作面类型")
    private String wfType;

}
