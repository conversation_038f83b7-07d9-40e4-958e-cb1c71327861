package com.bdtd.modules.working_face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 定位解算数据查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "定位解算数据查询对象", description = "定位解算数据查询对象")
public class PositionCalcQueryCriteria implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "工作面编码")
    private String workFaceId;

    @ApiModelProperty(value = "工作面名称")
    private String workFaceName;

    @ApiModelProperty(value = "基站编码")
    private String stationCode;

    @ApiModelProperty(value = "标签卡号")
    private String cardNumber;

    @ApiModelProperty(value = "基站编码、标签卡号对应关系列表字符串")
    private String stationCards;

    @ApiModelProperty(value = "通道")
    private Integer channel;

    @ApiModelProperty(value = "距离范围低")
    private Integer minDistance;

    @ApiModelProperty(value = "距离范围高")
    private Integer maxDistance;

    @ApiModelProperty(value = "是否断线")
    private String offline;

    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "数据时间开始日期")
    private String dataStart;

    @ApiModelProperty(value = "数据时间结束时间")
    private String dataEnd;

    @ApiModelProperty(value = "时间窗口查询模式, 0开区间,1左半开,2右半开（默认）,3闭区间")
    private Integer timeMode = 2;

    @ApiModelProperty(value = "时间窗口（秒），默认90")
    private Integer intervalSec;

    @ApiModelProperty(value = "滑动窗口（秒），默认60")
    private Integer slidingSec;

    @ApiModelProperty(value = "填充模式，NONE | VALUE | PREV | NULL | LINEAR | NEXT")
    private String fillMode;

    @ApiModelProperty(value = "忽略标签卡不全的时间窗口，0不忽略（默认），1忽略")
    private Integer ignorePartial;

}
