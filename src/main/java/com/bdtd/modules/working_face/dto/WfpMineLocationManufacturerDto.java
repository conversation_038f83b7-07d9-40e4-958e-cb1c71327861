package com.bdtd.modules.working_face.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bdtd.util.json.JsonLocalDateTimeDeserializer;
import com.bdtd.util.json.JsonLocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 煤矿定位厂家统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "煤矿定位厂家对象", description = "煤矿定位厂家统计")
public class WfpMineLocationManufacturerDto implements Serializable
{

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @ApiModelProperty(value = "二级公司编码")
    private String groupCode;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "厂家ID")
    private Long manufacturerId;

    @ApiModelProperty(value = "支持信号, 0不支持，1支持")
    private String featSignal;

    @ApiModelProperty(value = "支持电量, 0不支持，1支持")
    private String featPower;

    @ApiModelProperty(value = "支持电量告警, 0不支持，1支持")
    private String featPowerAlarm;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @ApiModelProperty(value = "删除时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime deletedAt;

    // =============================

    @ApiModelProperty(value = "二级公司名称")
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty(value = "煤矿名称")
    @TableField(exist = false)
    private String mineName;

}
