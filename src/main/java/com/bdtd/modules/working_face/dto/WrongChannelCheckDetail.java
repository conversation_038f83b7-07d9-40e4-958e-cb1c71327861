package com.bdtd.modules.working_face.dto;

import com.bdtd.modules.working_face.entity.PositionRawHistory;
import com.bdtd.modules.working_face.entity.WorkingFaceProgressDefinition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <p>
 * 天线反装报警信息明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "天线反装报警信息明细", description = "天线反装报警信息明细")
public class WrongChannelCheckDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    public WrongChannelCheckDetail()
    {
    }
    
    public WrongChannelCheckDetail(PositionRawHistory history, Integer wrongChannelState)
    {
        this.workingFaceId = history.getWorkFaceId();
        this.wrongState = wrongChannelState;
        this.warnTime = null;
        this.startTime = history.getDataTime();
        // 状态结束时间默认为当前数据时间，用于过滤历史数据
        this.endTime = history.getDataTime();
    }

    public WrongChannelCheckDetail(WorkingFaceProgressDefinition wfpd)
    {
        this.workingFaceId = wfpd.getWorkingFaceId();
        this.wrongState = Objects.equals(wfpd.getWrongChannelState(), 1) ? 1 : 0;
        this.warnTime = wfpd.getWrongChannelWarnTime();
        this.startTime = wfpd.getWrongChannelStateFrom();
        // 状态结束时间默认为天线异常开始时间，便于过滤历史数据
        this.endTime = wfpd.getWrongChannelStateFrom();
    }

    @ApiModelProperty(value = "工作面编码")
    private String workingFaceId;

    @ApiModelProperty(value = "天线反装状态")
    private Integer wrongState;

    @ApiModelProperty(value = "状态开始时间")
    private Timestamp startTime;

    @ApiModelProperty(value = "状态结束时间")
    private Timestamp endTime;

    // 已经入库的告警开始
    @ApiModelProperty(value = "生成告警时间, 当前是告警状态")
    private Timestamp warnTime;

    // 分析过程中的报警应产生时间，还未入库
    // 如果未配置持续时间或者配置为 0，triggerTime 应该等于 startTime，
    // 如果已配置持续时间且大于 0，triggerTime 最小也是 startTime 加上持续时间。
    @ApiModelProperty(value = "触发告警时间")
    private Timestamp triggerTime;
    
    // public static void main(String[] args) {
    //     String workFaceId = "1876928963101323265";
    //     WrongChannelCheckDetail detail = new WrongChannelCheckDetail();
    //     detail.setWorkingFaceId(workFaceId);
    //     detail.setWrongState(1);
    //     detail.setStartTime(new Timestamp(System.currentTimeMillis()));
    //     detail.setEndTime(new Timestamp(System.currentTimeMillis()));
    //     detail.setWarnTime(new Timestamp(System.currentTimeMillis()));
    //     detail.setTriggerTime(new Timestamp(System.currentTimeMillis()));
    //    
    //     Map<String, List<WrongChannelCheckDetail>> checkDetailMap = new HashMap<>();
    //     checkDetailMap.put(workFaceId, Arrays.asList(detail));
    //    
    //     Map<String, Object> memoMap = new HashMap<>();
    //     memoMap.put("pushSuccess", true);
    //     memoMap.put("actionStr", "通过接口同步定位解算原始数据数据");
    //     memoMap.put("syncQueryCriteria", "syncQueryCriteria");
    //     memoMap.put("checkDetailMap", checkDetailMap);
    //
    //     System.out.println(JSON.toJSONString(memoMap));
    //     System.out.println(JSON.toJSONString(memoMap, SerializerFeature.WriteDateUseDateFormat));
    // }

}
