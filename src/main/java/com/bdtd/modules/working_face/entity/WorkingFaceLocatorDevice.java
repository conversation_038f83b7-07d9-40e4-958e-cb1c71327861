package com.bdtd.modules.working_face.entity;

import com.github.jeffreyning.mybatisplus.anno.MppMultiId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工作面定位测距设备
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "WorkingFaceLocatorDevice对象", description = "")
public class WorkingFaceLocatorDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    @ApiModelProperty(value = "工作面编码")
    @MppMultiId
    private String workingFaceId;

    @ApiModelProperty(value = "设备编码")
    @MppMultiId
    private String deviceCode;

    @ApiModelProperty(value = "设备类型 1 定位卡 2 靶标卡 3 基站1 4 基站2")
    private Integer deviceType;

    @ApiModelProperty(value = "设备关系")
    private String deviceRelation;

    @ApiModelProperty(value = "X坐标")
    private String x;

    @ApiModelProperty(value = "Y坐标")
    private String y;

    @ApiModelProperty(value = "插入时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "删除时间")
    private LocalDateTime deletedAt;


}
