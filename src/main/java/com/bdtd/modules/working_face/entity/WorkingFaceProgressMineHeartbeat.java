package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 矿端采集链路心跳
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkingFaceProgressMineHeartbeat implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "心跳时间")
    private Timestamp hbTime;

    @ApiModelProperty(value = "服务状态（telnet 服务端口，0离线1在线)")
    private String serviceState;

    @ApiModelProperty(value = "服务状态监测时间")
    private Timestamp serviceStateMonitorTime;

    @ApiModelProperty(value = "服务在线最后心跳时间")
    private Timestamp serviceLastOnlineTime;

    @ApiModelProperty(value = "连通状态（ping IP，0离线1在线）")
    private String connectState;

    @ApiModelProperty(value = "连通状态监测时间")
    private Timestamp connectStateMonitorTime;

    @ApiModelProperty(value = "连通最后心跳时间")
    private Timestamp connectLastOnlineTime;

    @ApiModelProperty(value = "解算入库时间")
    private Timestamp updateTime;

    @ApiModelProperty(value = "断线判断时长，秒")
    private Integer timeLimit;

    @ApiModelProperty(value = "是否在线,0在线1离线")
    private String offline;

    @ApiModelProperty(value = "断线检查描述")
    private String checkDesc;

    @ApiModelProperty(value = "断线指标ID")
    private String indicatorId;

    @ApiModelProperty(value = "断线指标下发时间")
    private Timestamp indicatorUpdatedAt;

    @ApiModelProperty(value = "插入时间")
    @JsonIgnore
    private Timestamp createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonIgnore
    private Timestamp updatedAt;

    @ApiModelProperty(value = "状态是否发生变化")
    @JsonIgnore
    @TableField(exist = false)
    private Boolean offlineChanged;

}
