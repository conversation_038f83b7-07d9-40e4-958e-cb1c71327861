package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.util.json.GeometryPointDeserializer;
import com.bdtd.util.json.GeometryPointJSONSerializer;
import com.bdtd.util.sql.GeometryTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.postgis.Point;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面进尺实时值
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "working_face_progress_realtime", autoResultMap = true)
public class WorkingFaceProgressRealtime implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableId
    private String workingFaceId;

    /** 基站编码 */
    @ApiModelProperty(value = "基站编码")
    private String stationCode;

    /** 掘进机头位置, 工作面类型: 掘进 ewf */
    @ApiModelProperty(value = "掘进机头位置", hidden = true)
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryPointJSONSerializer.class)
    @JsonDeserialize(using = GeometryPointDeserializer.class)
    private Point machinePos;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置", hidden = true)
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryPointJSONSerializer.class)
    @JsonDeserialize(using = GeometryPointDeserializer.class)
    private Point transPos;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置", hidden = true)
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryPointJSONSerializer.class)
    @JsonDeserialize(using = GeometryPointDeserializer.class)
    private Point airPos;

    /** 进尺平均速度 */
    @ApiModelProperty(value = "进尺平均速度")
    private Double averageSpeed;

    /** 自增序号 */
    @ApiModelProperty(value = "自增序号")
    private Long sequenceNumber;

    /** 预警报警距离, 单位米 */
    @ApiModelProperty(value = "预警报警距离, 单位米")
    private String alarmDistance;

    /** 补偿值, 单位厘米 */
    @ApiModelProperty(value = "补偿值, 单位厘米")
    private String compensation;

    /** 进尺数值（包含补偿值） */
    @ApiModelProperty(value = "进尺数值（包含补偿值）")
    private String footage;

    /** 采集值状态 */
    @ApiModelProperty(value = "采集值状态")
    private String state;

    /** 数据时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    @JsonIgnore
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间", hidden = true)
    @JsonIgnore
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", hidden = true)
    @JsonIgnore
    private Timestamp updatedAt;

    // ---

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableField(exist = false)
    @JsonIgnore
    private String wfId;

    /** 是否软删除 */
    @TableField(exist = false)
    @JsonIgnore
    private Integer deletedAtIsNull;

}
