package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面进尺历史值
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkingFaceProgressHistory implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 基站所在巷道名称 */
    @ApiModelProperty(value = "基站所在巷道名称")
    private String tunnelName;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableId
    private String workingFaceId;

    /** 基站编码 */
    @ApiModelProperty(value = "基站编码")
    private String stationCode;

    /** 掘进机头位置, 工作面类型: 掘进 ewf */
    @ApiModelProperty(value = "掘进机头位置 X")
    private Double machinePosX;

    /** 掘进机头位置, 工作面类型: 掘进 ewf */
    @ApiModelProperty(value = "掘进机头位置 Y")
    private Double machinePosY;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置 X")
    private Double transPosX;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置 Y")
    private Double transPosY;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置 X")
    private Double airPosX;

    /** 运输巷位置, 工作面类型: 采煤 mwf */
    @ApiModelProperty(value = "运输巷位置 Y")
    private Double airPosY;

    /** 进尺平均速度 */
    @ApiModelProperty(value = "进尺平均速度")
    private Double averageSpeed;

    /** 自增序号 */
    @ApiModelProperty(value = "自增序号")
    private Long sequenceNumber;

    /** 进尺数值（包含补偿值） */
    @ApiModelProperty(value = "进尺数值（包含补偿值）")
    private String footage;

    /** 预警报警距离, 单位米 */
    @ApiModelProperty(value = "预警报警距离, 单位米")
    private String alarmDistance;

    /** 补偿值, 单位厘米 */
    @ApiModelProperty(value = "补偿值, 单位厘米")
    private String compensation;

    /** 采集值状态 */
    @ApiModelProperty(value = "采集值状态")
    private String state;

    /** 采集源标识 0,null:实时值, 1:历史值 */
    @ApiModelProperty(value = "采集源标识")
    private String sourceFlag;

    /** 数据时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    @JsonIgnore
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    @JsonIgnore
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonIgnore
    private Timestamp updatedAt;

    // ---

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableField(exist = false)
    @JsonIgnore
    private String wfId;

    /** 是否软删除 */
    @TableField(exist = false)
    @JsonIgnore
    private Integer deletedAtIsNull;

}
