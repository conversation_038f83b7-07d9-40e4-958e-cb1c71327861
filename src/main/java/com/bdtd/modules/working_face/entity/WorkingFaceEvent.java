package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("working_face_event")
public class WorkingFaceEvent extends Model<WorkingFaceEvent>
{

    private static final long serialVersionUID = 1L;

    // 记录类型：state:状态（有结束时间)，event:事件（无结束时间)
    public static final String RECORD_TYPE_STATE = "state";
    public static final String RECORD_TYPE_EVENT = "event";

    // 事件类型
    public static final String EVENT_TYPE_WORKING_FACE_LIFE_CYCLE = "life_cycle";

    /**
     * id
     */
    @TableId("id")
    private Long id;

    /**
     * 工作面id
     */
    @TableField("work_face_id")
    private String workFaceId;

    /**
     * 工作面类型
     */
    @TableField("work_face_type")
    private String workFaceType;

    /**
     * 记录类型：state 状态（有结束时间)，event 事件（无结束时间)
     */
    @TableField("record_type")
    private String recordType;

    /**
     * 事件类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 值
     */
    @TableField("value")
    private String value;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Timestamp endTime;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Timestamp updatedAt;

    @Override
    public Serializable pkVal()
    {
        return null;
    }

    public static WorkingFaceEvent getLifeCycleWorkingFaceEvent(WorkingFaceProgressDefinition wfpd, Timestamp beginTime, String actionStr)
    {
        WorkingFaceEvent event = new WorkingFaceEvent();
        event.setEventType(WorkingFaceEvent.EVENT_TYPE_WORKING_FACE_LIFE_CYCLE);
        event.setRecordType(WorkingFaceEvent.RECORD_TYPE_STATE);
        event.setWorkFaceId(wfpd.getWorkingFaceId());
        event.setWorkFaceType(wfpd.getWorkingFaceType());
        event.setValue(wfpd.getWorkState());
        event.setBeginTime(beginTime);
        event.setDescription(actionStr);
        return event;
    }
}
