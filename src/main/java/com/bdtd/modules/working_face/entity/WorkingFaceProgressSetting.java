package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面解析配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("working_face_progress_settings")
public class WorkingFaceProgressSetting implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 集团编码 */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /** 集团名称 */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /** 二级公司编码 */
    @ApiModelProperty(value = "二级公司编码")
    private String companyCode;

    /** 二级公司名称 */
    @ApiModelProperty(value = "二级公司名称")
    private String companyName;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名 */
    @ApiModelProperty(value = "矿名")
    private String mineName;

    /** 配置 */
    @ApiModelProperty(value = "配置")
    private String settings;

    /** 配置更新时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "配置更新时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp updateTime;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    @JsonIgnore
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonIgnore
    private Timestamp updatedAt;

}
