package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 工作面监测值
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkingFaceLocatorStatus implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 集团编码（二级公司） */
    @ApiModelProperty(value = "集团编码（二级公司）")
    private String groupCode;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    private String workingFaceId;

    /** 标签卡编码 */
    @ApiModelProperty(value = "标签卡编码")
    private String labelCode;

    /** 点位ID */
    @ApiModelProperty(value = "点位ID")
    @TableId
    private String pointId;

    /** 点位描述 */
    @ApiModelProperty(value = "点位描述")
    private String pointDesc;

    /** 监测类型: 电量、信号值 */
    @ApiModelProperty(value = "监测类型")
    private String monitorType;

    /** 点位类型: 0 模拟量、1 开关量、2 多态量、3 累计量、4 文本量 */
    @ApiModelProperty(value = "点位类型")
    private Integer pointType;

    /** 值类型: float, int, string */
    @ApiModelProperty(value = "值类型")
    private String valueType;

    /** 值单位 */
    @ApiModelProperty(value = "值单位")
    private String valueUnit;

    /** 监测值 */
    @ApiModelProperty(value = "监测值")
    private String value;

    /** 采集值状态 */
    @ApiModelProperty(value = "采集值状态")
    private String state;

    /** 数据时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    @JsonIgnore
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间")
    @JsonIgnore
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonIgnore
    private Timestamp updatedAt;

    // ---

    /** 工作面ID列表 */
    @ApiModelProperty(value = "工作面ID列表")
    @TableField(exist = false)
    private List<String> workingFaceIdList;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableField(exist = false)
    @JsonIgnore
    private String wfId;

    /** 集团名称（二级公司） */
    @ApiModelProperty(value = "集团名称（二级公司）")
    @TableField(exist = false)
    private String groupName;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    @TableField(exist = false)
    private String mineName;

    /** 工作面类型: 掘进 ewf，采煤 mwf */
    @ApiModelProperty(value = "工作面类型: 掘进 ewf，采煤 mwf")
    @TableField(exist = false)
    private String workingFaceType;

    /** 是否绑定了工作模式: 0 表示绑定、1 表示未绑定、9 表示全部 */
    @ApiModelProperty(value = "是否绑定了工作模式: 0 表示绑定、1 表示未绑定、9 表示全部")
    @TableField(exist = false)
    private Integer workModeIsNull;

    /** 工作面接入状态 */
    @ApiModelProperty(value = "工作面接入状态: 0 停止接入、1 接入中、9 表示全部")
    @TableField(exist = false)
    private String systemFlag;

}
