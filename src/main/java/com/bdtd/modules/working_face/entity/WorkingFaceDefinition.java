package com.bdtd.modules.working_face.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * GIS 服务工作面基本信息
 *
 * <AUTHOR>
 * @since 2023-11-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkingFaceDefinition implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 工作面类型：采煤工作面 */
    public final static String FACE_TYPE_MINING = "mwf";
    /** 工作面类型：掘进工作面 */
    public final static String FACE_TYPE_EXCAVATION = "ewf";

    /**
     * ID
     * 采煤工作面（mwf）：0 + 原工作面 ID
     * 掘进工作面（ewf）：1 + 原工作面 ID
     * 主系统编码：990 进尺采集，991 进尺采集设备监测
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /** 工作面类型 */
    @ApiModelProperty(value = "工作面类型")
    private String faceType;

    /** （源）工作面ID */
    @ApiModelProperty(value = "（源）工作面ID")
    private String workFaceId;

    /** 工作面名称 */
    @ApiModelProperty(value = "工作面名称")
    private String workFaceName;

    /** 工作面状态 */
    @ApiModelProperty(value = "工作面状态")
    private String faceStatus;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedAt;

    public WorkingFaceDefinition()
    {
    }

    public WorkingFaceDefinition(WorkingFaceProgressDefinition wfpd)
    {
        this.faceType = wfpd.getWorkingFaceType();
        this.workFaceId = wfpd.getWorkingFaceId();
        this.workFaceName = wfpd.getWorkingFaceName();
        this.faceStatus = wfpd.getWorkState();
        this.createdAt = new Timestamp(System.currentTimeMillis());
    }

}
