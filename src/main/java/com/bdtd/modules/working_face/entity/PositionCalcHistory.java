package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.contract.ITaosEntity;
import com.bdtd.util.tdengine.entity.TaosStableEntity;
import com.bdtd.util.tdengine.entity.TaosTableEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 定位解算历史数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PositionCalcHistory implements Serializable, ITaosEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间戳")
    @TableId
    private Timestamp updateTime;

    @ApiModelProperty(value = "坐标X")
    private String x;

    @ApiModelProperty(value = "坐标Y")
    private String y;

    @ApiModelProperty(value = "电量值")
    private Integer powerValue;

    @ApiModelProperty(value = "信号值")
    private Integer signalValue;

    @ApiModelProperty(value = "是否离线")
    private String offline;

    @ApiModelProperty(value = "通道")
    private Integer channel;

    @ApiModelProperty(value = "距离")
    private Integer distance;

    @ApiModelProperty(value = "数据时间")
    private Timestamp dataTime;

    @ApiModelProperty(value = "原始消息内容")
    private String mqInfo;

    @ApiModelProperty(value = "入库时间")
    private Timestamp updatedAt;

    @ApiModelProperty(value = "标签卡编号")
    private String cardNumber;

    @ApiModelProperty(value = "定位基站编码")
    private String stationCode;

    @ApiModelProperty(value = "工作面ID")
    private String workFaceId;

    @ApiModelProperty(value = "工作面名称")
    private String workFaceName;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    /**
     * 取得超级表实体
     *
     * @return /
     */
    public static TaosStableEntity buildTaosStableEntity() {
        TaosStableEntity stable = new TaosStableEntity();
        stable.setName("pos_calc_history");
        stable.setTagNames(Arrays.asList("card_number", "station_code", "work_face_id", "work_face_name", "mine_code"));
        stable.setFieldNames(Arrays.asList(
            "ts",
            "x",
            "y",
            "power_value",
            "signal_value",
            "offline",
            "channel",
            "distance",
            "data_time",
            "mq_info",
            "updated_at"
        ));
        return stable;
    }

    /**
     * 取得普通表实体
     *
     * @return /
     */
    @Override
    public TaosTableEntity buildTaosTableEntity() {
        // 插入表内容
        TaosTableEntity table = new TaosTableEntity(
            // 超级表
            buildTaosStableEntity(),
            // 普通表名, 前缀超级表名, 替换其中的 . 为 _-_
            String.format("%s_%s_%s", this.workFaceId, this.stationCode, this.cardNumber).replace(".", "_-_")
        );

        // 标签
        Map<String, Object> tagMap = new HashMap<>(5);
        tagMap.put("card_number", this.cardNumber);
        tagMap.put("station_code", this.stationCode);
        tagMap.put("work_face_id", this.workFaceId);
        tagMap.put("work_face_name", this.workFaceName.replace("'", "\\'"));
        tagMap.put("mine_code", this.mineCode);
        table.setTags(tagMap);

        // 数据
        Map<String, Object> itemMap = new HashMap<>(10);
        itemMap.put("ts", this.updateTime);
        itemMap.put("x", this.x);
        itemMap.put("y", this.y);
        itemMap.put("power_value", this.powerValue);
        itemMap.put("signal_value", this.signalValue);
        itemMap.put("offline", this.offline);
        itemMap.put("channel", this.channel);
        itemMap.put("distance", this.distance);
        itemMap.put("data_time", this.dataTime);
        itemMap.put("mq_info", this.mqInfo);
        itemMap.put("updated_at", this.updatedAt);
        table.setItems(Stream.of(itemMap).collect(toList()));

        String tableName = TaosConnector.generateTableName(table, true, true);
        table.setName(tableName);

        return table;
    }

}
