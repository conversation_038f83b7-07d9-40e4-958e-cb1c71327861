package com.bdtd.modules.working_face.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bdtd.modules.working_face.util.excel.LocationManufacturerFeatureConverter;
import com.bdtd.util.json.JsonLocalDateTimeDeserializer;
import com.bdtd.util.json.JsonLocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 煤矿定位厂家统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ExcelIgnoreUnannotated
@ApiModel(value = "WfpMineLocationManufacturer对象", description = "煤矿定位厂家统计")
public class WfpMineLocationManufacturer implements Serializable
{

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @ApiModelProperty(value = "二级公司编码")
    private String groupCode;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "厂家ID")
    private Long manufacturerId;

    @ApiModelProperty(value = "支持信号, 0不支持，1支持")
    @ExcelProperty(value = "信号特性", converter = LocationManufacturerFeatureConverter.class, index = 5)
    private String featSignal;

    @ApiModelProperty(value = "支持电量, 0不支持，1支持")
    @ExcelProperty(value = "电量特性", converter = LocationManufacturerFeatureConverter.class, index = 6)
    private String featPower;

    @ApiModelProperty(value = "支持电量告警, 0不支持，1支持")
    @ExcelProperty(value = "电量告警特性", converter = LocationManufacturerFeatureConverter.class, index = 7)
    private String featPowerAlarm;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @TableField(jdbcType = JdbcType.TIMESTAMP, fill = FieldFill.INSERT, update = "now()")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @TableField(jdbcType = JdbcType.TIMESTAMP, fill = FieldFill.UPDATE, update = "now()")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @ApiModelProperty(value = "删除时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime deletedAt;

    // ==================================================

    @ApiModelProperty(value = "二级公司名称")
    @ExcelProperty(value = "二级公司", index = 0)
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty(value = "二级公司排序")
    @TableField(exist = false)
    private Integer groupSort;

    @ApiModelProperty(value = "煤矿名称")
    @ExcelProperty(value = "煤矿名称", index = 1)
    @TableField(exist = false)
    private String mineName;

    @ApiModelProperty(value = "煤矿排序")
    @TableField(exist = false)
    private Integer mineSort;

    @ApiModelProperty(value = "厂家名称")
    @ExcelProperty(value = "厂家名称", index = 2)
    @TableField(exist = false)
    private String manufacturerName;

    @ApiModelProperty(value = "厂家全称")
    @TableField(exist = false)
    private String manufacturerFullName;

    @ApiModelProperty(value = "联系人")
    @ExcelProperty(value = "联系人", index = 3)
    @TableField(exist = false)
    private String manufacturerContact;

    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式", index = 4)
    @TableField(exist = false)
    private String manufacturerContactTel;

    // ==================================================

    @ApiModelProperty(value = "软删除过滤，1返回正常，0返回已删除，9返回全部")
    @TableField(exist = false)
    @JsonIgnore
    Integer deletedAtIsNull;

}
