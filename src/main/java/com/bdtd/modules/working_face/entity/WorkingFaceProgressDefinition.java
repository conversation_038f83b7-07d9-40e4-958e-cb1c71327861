package com.bdtd.modules.working_face.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bdtd.util.json.GeometryPointDeserializer;
import com.bdtd.util.json.GeometryPointJSONSerializer;
import com.bdtd.util.sql.GeometryTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.postgis.Point;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 工作面进尺定义
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "working_face_progress_definition", autoResultMap = true)
public class WorkingFaceProgressDefinition implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 集团编码（二级公司） */
    @ApiModelProperty(value = "集团编码（二级公司）")
    private String groupCode;

    /** 集团名称（二级公司） */
    @ApiModelProperty(value = "集团名称（二级公司）")
    private String groupName;

    /** 矿编码 */
    @ApiModelProperty(value = "矿编码")
    private String mineCode;

    /** 矿名称 */
    @ApiModelProperty(value = "矿名称")
    private String mineName;

    /** 系统编码 */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableId
    private String workingFaceId;

    /** 工作面类型: 掘进 ewf，采煤 mwf */
    @ApiModelProperty(value = "工作面类型: 掘进 ewf，采煤 mwf")
    private String workingFaceType;

    /** 工作面名称 */
    @ApiModelProperty(value = "工作面名称")
    private String workingFaceName;

    /** 基站编码 */
    @ApiModelProperty(value = "基站编码")
    private String stationCode;

    /** 基站名称 */
    @ApiModelProperty(value = "基站名称")
    private String stationName;

    /** 基站所在巷道名称 */
    @ApiModelProperty(value = "基站所在巷道名称")
    private String tunnelName;

    /** 巷道位置 */
    @ApiModelProperty(value = "巷道位置", hidden = true)
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryPointJSONSerializer.class)
    @JsonDeserialize(using = GeometryPointDeserializer.class)
    private Point tunnelCoordinates;

    /**
     * 工作面状态
     */
    @ApiModelProperty(value = "工作面状态")
    private String workState;

    /**
     * 工作模式
     * 1：固定基站+定位标签
     * 2：固定靶标+移动基站
     * 3：靶标标签+移动基站+定位标签
     * 4：定位标签+移动双基站+靶标标签
     */
    @ApiModelProperty(value = "工作模式")
    private String workMode;

    /** 模式是否变更 */
    @ApiModelProperty(value = "模式是否变更")
    private String workModeChanged;

    /** 模式变更时间 */
    @ApiModelProperty(value = "模式变更时间")
    private Timestamp workModeChangedAt;

    /** 初次接入时间 */
    @ApiModelProperty(value = "初次接入时间")
    private Timestamp firstDataTime;

    /** 天线反装状态，0正常1反装 */
    @ApiModelProperty(value = "天线反装状态，0正常1反装")
    private Integer wrongChannelState;

    /** 天线反装开始时间 */
    @ApiModelProperty(value = "天线反装开始时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp wrongChannelStateFrom;

    /** 天线反装报警时间 */
    @ApiModelProperty(value = "天线反装报警时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp wrongChannelWarnTime;

    /** 天线反装指标ID */
    @ApiModelProperty(value = "天线反装指标ID")
    private String channelIndicatorId;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 数据时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "数据时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp dataTime;

    /** 采集时间, 格式 yyyy-MM-dd HH:mm:ss */
    @ApiModelProperty(value = "采集时间, 推送格式 yyyy-MM-dd HH:mm:ss")
    private Timestamp collectTime;

    /** 采集状态, 100正常, 90自动结束 */
    @ApiModelProperty(value = "采集状态, 100正常, 90自动结束")
    @JsonIgnore
    private Integer collectStatus;

    /** 插入时间 */
    @ApiModelProperty(value = "插入时间", hidden = true)
    @JsonIgnore
    private Timestamp createdAt;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", hidden = true)
    @JsonIgnore
    private Timestamp updatedAt;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间", hidden = true)
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private Timestamp deletedAt;

    // ---

    /** 工作面ID */
    @ApiModelProperty(value = "工作面ID")
    @TableField(exist = false)
    @JsonIgnore
    private String wfId;

    /** 工作面名称 */
    @ApiModelProperty(value = "工作面名称")
    @TableField(exist = false)
    @JsonIgnore
    private String wfName;

    /** 工作面类型 */
    @ApiModelProperty(value = "工作面类型")
    @TableField(exist = false)
    @JsonIgnore
    private String wfType;

    /** 巷道位置X */
    @ApiModelProperty(value = "巷道位置X")
    @TableField(exist = false)
    @JsonIgnore
    private Double tunnelCoordinatesX;

    /** 巷道位置Y */
    @ApiModelProperty(value = "巷道位置Y")
    @TableField(exist = false)
    @JsonIgnore
    private Double tunnelCoordinatesY;

    /** 是否软删除 */
    @TableField(exist = false)
    @JsonIgnore
    private Integer deletedAtIsNull;

}
