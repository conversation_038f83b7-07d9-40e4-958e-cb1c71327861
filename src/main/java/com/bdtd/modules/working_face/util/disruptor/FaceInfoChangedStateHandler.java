package com.bdtd.modules.working_face.util.disruptor;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bdtd.conf.disruptor.DisruptorEvent;
import com.bdtd.modules.working_face.entity.WorkingFaceDefinition;
import com.bdtd.modules.working_face.entity.WorkingFaceEvent;
import com.bdtd.modules.working_face.service.IWorkingFaceDefinitionService;
import com.bdtd.modules.working_face.service.IWorkingFaceEventService;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.WorkHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用事件处理器/消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FaceInfoChangedStateHandler<T> implements WorkHandler<DisruptorEvent<T>>, EventHandler<DisruptorEvent<T>>
{
    private final IWorkingFaceEventService workingFaceEventService;
    private final IWorkingFaceDefinitionService workingFaceDefinitionService;

    public FaceInfoChangedStateHandler(
            IWorkingFaceEventService workingFaceEventService,
            IWorkingFaceDefinitionService workingFaceDefinitionService
    )
    {
        this.workingFaceEventService = workingFaceEventService;
        this.workingFaceDefinitionService = workingFaceDefinitionService;
    }

    /**
     * 重复消费：每个消费者重复消费生产者生产的数据
     *
     * @param message    消息
     * @param sequence   当前序列号
     * @param endOfBatch 批次结束标识（常用于将多个消费着的数据依次组合到最后一个消费者统一处理）
     */
    @Override
    public void onEvent(DisruptorEvent<T> message, long sequence, boolean endOfBatch) throws InterruptedException
    {
        log.info("FaceInfoChangedStateHandler started, message: {}, sequence: {}, endOfBatch: {}", message, sequence, endOfBatch);
        this.onEvent(message);
        log.info("FaceInfoChangedStateHandler end, message: {}, sequence: {}, endOfBatch: {}", message, sequence, endOfBatch);
    }

    /**
     * 分组消费：每个生产者生产的数据只能被一个消费者消费
     *
     * @param message 消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public void onEvent(DisruptorEvent<T> message) throws InterruptedException
    {
        log.info("FaceInfoChangedStateHandler started, message: {}", message);

        List<Timestamp> timeList = (List<Timestamp>) message.getData();
        Timestamp startTime = !timeList.isEmpty() ? timeList.get(0) : null;
        Timestamp endTime = timeList.size() > 1 ? timeList.get(1) : null;

        QueryWrapper<WorkingFaceDefinition> queryWrap = new QueryWrapper<>();
        queryWrap.gt(startTime != null, "updated_at", startTime);
        queryWrap.le(endTime != null, "updated_at", endTime);

        List<WorkingFaceDefinition> updatedList = workingFaceDefinitionService.list(queryWrap);
        if (updatedList.isEmpty()) {
            log.info("FaceInfoChangedStateHandler end, nothing to check, message: {}", message);
            return;
        }

        Timestamp nowTime = new Timestamp(System.currentTimeMillis());

        List<WorkingFaceEvent> eventList = new ArrayList<>();
        for (WorkingFaceDefinition wfd : updatedList) {
            WorkingFaceEvent event = new WorkingFaceEvent();
            event.setEventType(WorkingFaceEvent.EVENT_TYPE_WORKING_FACE_LIFE_CYCLE);
            event.setRecordType(WorkingFaceEvent.RECORD_TYPE_STATE);
            event.setWorkFaceId(wfd.getWorkFaceId());
            event.setWorkFaceType(wfd.getFaceType());
            event.setValue(wfd.getFaceStatus());
            event.setBeginTime(nowTime);
            event.setDescription("工作面基本信息同步");

            eventList.add(event);
        }

        try {
            int updCount = workingFaceEventService.createRecord(eventList);
            log.info("FaceInfoChangedStateHandler end, create or update {}, message: {}", updCount, message);
        }
        catch (Exception ex) {
            log.error("FaceInfoChangedStateHandler error, message: {}", message, ex);
        }
    }

}
