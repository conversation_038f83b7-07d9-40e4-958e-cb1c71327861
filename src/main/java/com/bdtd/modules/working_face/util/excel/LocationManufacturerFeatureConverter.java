package com.bdtd.modules.working_face.util.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * EasyExcel LocationManufacturerFeature Converter Utility
 *
 * <AUTHOR>
 */
public class LocationManufacturerFeatureConverter implements Converter<String>
{

    @Override
    public Class<String> supportJavaTypeKey()
    {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey()
    {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration)
    {
        if ("支持".equals(cellData.getStringValue())) {
            return "1";
        }
        else if ("不支持".equals(cellData.getStringValue())) {
            return "0";
        }
        else {
            return null;
        }
    }

    @Override
    public CellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration)
    {
        String cellStr;
        if ("1".equals(value)) {
            cellStr = "支持";
        }
        else if ("0".equals(value)) {
            cellStr = "不支持";
        }
        else {
            cellStr = "";
        }
        return new CellData<>(cellStr);
    }

}

