package com.bdtd.modules.working_face.util.disruptor;

import com.bdtd.conf.disruptor.DisruptorEvent;
import com.bdtd.modules.working_face.service.IWorkingFaceDefinitionService;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.WorkHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

/**
 * 通用事件处理器/消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FaceInfoChangedRenameHandler<T> implements WorkHandler<DisruptorEvent<T>>, EventHandler<DisruptorEvent<T>>
{
    private final IWorkingFaceDefinitionService workingFaceDefinitionService;

    public FaceInfoChangedRenameHandler(IWorkingFaceDefinitionService workingFaceDefinitionService)
    {
        this.workingFaceDefinitionService = workingFaceDefinitionService;
    }

    /**
     * 重复消费：每个消费者重复消费生产者生产的数据
     *
     * @param message    消息
     * @param sequence   当前序列号
     * @param endOfBatch 批次结束标识（常用于将多个消费着的数据依次组合到最后一个消费者统一处理）
     */
    @Override
    public void onEvent(DisruptorEvent<T> message, long sequence, boolean endOfBatch)
    {
        log.info("FaceInfoChangedRenameHandler started, message: {}, sequence: {}, endOfBatch: {}", message, sequence, endOfBatch);
        this.onEvent(message);
        log.info("FaceInfoChangedRenameHandler end, message: {}, sequence: {}, endOfBatch: {}", message, sequence, endOfBatch);
    }

    /**
     * 分组消费：每个生产者生产的数据只能被一个消费者消费
     *
     * @param message 消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public void onEvent(DisruptorEvent<T> message)
    {
        log.info("FaceInfoChangedEventManager started, message: {}", message);
        try {
            List<Timestamp> timeList = (List<Timestamp>) message.getData();
            Timestamp startTime = !timeList.isEmpty() ? timeList.get(0) : null;
            Timestamp endTime = timeList.size() > 1 ? timeList.get(1) : null;

            int msUpdCount = workingFaceDefinitionService.updateModelSystemByUpdatedWf(startTime, endTime);
            int msaUpdCount = workingFaceDefinitionService.updateModelSystemAliveByUpdatedWf(startTime, endTime);
            int msasUpdCount = workingFaceDefinitionService.updateModelSystemAliveStatisticsByUpdatedWf(startTime, endTime);
            int dsmUpdCount = workingFaceDefinitionService.updateDataSystemMonitorByUpdatedWf(startTime, endTime);
            int wfpdUpdCount = workingFaceDefinitionService.updateWorkingFaceProgressDefinitionByUpdatedWf(startTime, endTime);

            log.info(
                    "FaceInfoChangedRenameHandler end, msUpdCount: {}, msaUpdCount: {}, dsmUpdCount: {}, msasUpdCount: {}, wfpdUpdCount: {}",
                    msUpdCount,
                    msaUpdCount,
                    dsmUpdCount,
                    msasUpdCount,
                    wfpdUpdCount
            );
        }
        catch (Exception e) {
            log.error("FaceInfoChangedEventManager error, message: {}", message, e);
        }
    }

}
