package com.bdtd.modules.working_face.util.rmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bdtd.conf.MesBusinessEnum;
import com.bdtd.conf.RabbitTopicConfig;
import com.bdtd.feign.MineBizFeignService;
import com.bdtd.modules.data_access.service.IModelSystemService;
import com.bdtd.modules.monitor.entity.ServiceCallLog;
import com.bdtd.modules.monitor.service.IModelSystemAliveService;
import com.bdtd.modules.monitor.service.IServiceCallLogService;
import com.bdtd.modules.working_face.dto.BaseMessage;
import com.bdtd.modules.working_face.dto.WorkingFaceProgressSettingUpdateMessage;
import com.bdtd.modules.working_face.service.IWorkingFaceProgressSettingService;
import com.bdtd.util.StringUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 工作面进尺配置更新消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnExpression("'rmq'.equals('${biz.working-face-setting-sync-mode:api}') or 'all'.equals('${biz.working-face-setting-sync-mode:api}')")
public class WorkingFaceProgressSettingUpdateHandler
{

    @Autowired
    MineBizFeignService mineBizFeignService;
    @Autowired
    IServiceCallLogService serviceCallLogService;

    @Autowired
    IModelSystemService modelSystemService;
    @Autowired
    IModelSystemAliveService modelSystemAliveService;
    @Autowired
    IWorkingFaceProgressSettingService workingFaceProgressSettingService;

    /**
     * 消息消费
     *
     * @param message 队列中的消息
     * @param channel 当前的消息队列
     */
    @RabbitListener(queues = RabbitTopicConfig.WORKING_FACE_PROGRESS_SETTING_UPDATE_QUEUE)
    public void workingFaceProgressSettingUpdateListener(Message message, Channel channel) throws IOException
    {
        long start = System.currentTimeMillis();
        List<ServiceCallLog> serviceCallLogs = new ArrayList<>();

        try {
            // 消息体解析
            BaseMessage<WorkingFaceProgressSettingUpdateMessage> messageObj = JSONObject.parseObject(
                    new String(message.getBody()),
                    BaseMessage.class
            );
            // 内容解析
            WorkingFaceProgressSettingUpdateMessage content = JSONObject.parseObject(
                    JSON.toJSONString(messageObj.getContent()),
                    WorkingFaceProgressSettingUpdateMessage.class
            );

            messageObj.setContent(content);

            // // 构造查询参数
            // Map<String, Object> queryMap = new HashMap<>();
            // if (StringUtil.isNotEmpty(messageObj.getContent().getGroupCode())) {
            //     queryMap.put("group_code", messageObj.getContent().getGroupCode());
            // }
            // if (StringUtil.isNotEmpty(messageObj.getContent().getCompanyCode())) {
            //     queryMap.put("company_code", messageObj.getContent().getCompanyCode());
            // }
            // if (StringUtil.isNotEmpty(messageObj.getContent().getCompanyCode())) {
            //     queryMap.put("mine_code", messageObj.getContent().getMineCode());
            // }

            log.info(
                    "received working face progress setting change message, group_code: {}, company_code: {}, mine_code: {}",
                    StringUtil.isEmpty(messageObj.getContent().getGroupCode())
                        ? "null"
                        : messageObj.getContent().getGroupCode(),
                    StringUtil.isEmpty(messageObj.getContent().getCompanyCode())
                        ? "null"
                        : messageObj.getContent().getCompanyCode(),
                    StringUtil.isEmpty(messageObj.getContent().getMineCode())
                        ? "null"
                        : messageObj.getContent().getMineCode()
            );
            serviceCallLogs.add(ServiceCallLog.createNew(
                    "配置更新订阅 接收到配置变更提醒",
                    (byte) 0,
                    StringUtil.isEmpty(messageObj.getContent().getMineCode())
                        ? "0"
                        : messageObj.getContent().getMineCode()
            ));

            // 根据获取参数拉取最新配置（共通代码）
            JSONObject result = mineBizFeignService.queryWorkingFaceProgressSettings(
                    messageObj.getContent().getGroupCode(),
                    messageObj.getContent().getCompanyCode(),
                    messageObj.getContent().getMineCode(),
                    0L,
                    MesBusinessEnum.HEADER.desc
            );
            log.debug(
                    "queryWorkingFaceProgressSettings result (groupCode: {}, companyCode: {}, mineCode: {},  lastUpdateTime: {}): {}",
                    messageObj.getContent().getGroupCode(),
                    messageObj.getContent().getCompanyCode(),
                    messageObj.getContent().getMineCode(),
                    0L,
                    (result == null ? "" : JSON.toJSONString(result))
            );

            // 解析处理配置
            if (result != null) {
                List<ServiceCallLog> processLogs = workingFaceProgressSettingService.parseProgressSettings(
                        "配置更新订阅 收到配置更新提醒后调用业务侧配置接口",
                        result,
                        messageObj.getContent().getGroupCode(),
                        messageObj.getContent().getCompanyCode(),
                        messageObj.getContent().getMineCode(),
                        null,
                        // 是否包含煤矿全部工作面
                        true,
                        // 工作面配置是否包含工作模式
                        false,
                        messageObj.getContent().getForceUpdateModelSystem()
                );

                if (processLogs != null && !processLogs.isEmpty()) {
                    serviceCallLogs.addAll(processLogs);
                }
            }
            else {
                serviceCallLogs.add(ServiceCallLog.createNew(
                        "配置更新订阅 收到配置更新提醒后调用业务侧配置接口 返回内容为空",
                        (byte) 1,
                        StringUtil.isEmpty(messageObj.getContent().getMineCode())
                            ? "0"
                            : messageObj.getContent().getMineCode()
                ));
                log.error(
                        "queryWorkingFaceProgressSettings result is null, groupCode: {}, companyCode: {}, mineCode: {}, lastUpdateTime: {}",
                        messageObj.getContent().getGroupCode(),
                        messageObj.getContent().getCompanyCode(),
                        messageObj.getContent().getMineCode(),
                        0L
                );
            }

            // 无论获取的配置是否有效都 ack
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

            log.info(
                    "[ROUTING_KEY: {}] working face progress setting change message consume success, cost {} ms",
                    message.getMessageProperties().getReceivedRoutingKey(),
                    (System.currentTimeMillis() - start)
            );
        }
        catch (Exception e) {
            log.error(
                    "[WF_ID: {}] working face progress setting change message consume failed, cost {} ms, error: {}",
                    message.getMessageProperties().getReceivedRoutingKey(),
                    (System.currentTimeMillis() - start),
                    e.getMessage(),
                    e
            );

            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            }
            catch (IOException re) {
                log.error(
                        "[ROUTING_KEY: {}] ack exception consume message error. retry it.",
                        message.getMessageProperties().getReceivedRoutingKey(),
                        re
                );
            }
        }

        try {
            if (!serviceCallLogs.isEmpty()) {
                serviceCallLogService.createLogs(serviceCallLogs);
            }
        }
        catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

}

