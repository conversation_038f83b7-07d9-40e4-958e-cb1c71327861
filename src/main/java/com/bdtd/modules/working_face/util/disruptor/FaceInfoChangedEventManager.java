package com.bdtd.modules.working_face.util.disruptor;

import com.bdtd.conf.disruptor.AbstractDisruptorEventManager;
import org.springframework.stereotype.Service;

/**
 * DisruptorRepeatEventManager
 *
 * <AUTHOR>
 */
@Service
public class FaceInfoChangedEventManager<T> extends AbstractDisruptorEventManager<T>
{
    private final FaceInfoChangedRenameHandler<T> faceInfoChangedRenameHandler;
    private final FaceInfoChangedStateHandler<T> faceInfoChangedStateHandler;

    public FaceInfoChangedEventManager(
            FaceInfoChangedRenameHandler<T> faceInfoChangedRenameHandler,
            FaceInfoChangedStateHandler<T> faceInfoChangedStateHandler
    )
    {
        this.faceInfoChangedRenameHandler = faceInfoChangedRenameHandler;
        this.faceInfoChangedStateHandler = faceInfoChangedStateHandler;
    }

    @Override
    protected void handleEvents()
    {
        /*
         * 调用 handleEventsWith，表示创建的多个消费者，每个都是独立消费的
         * 可以定义不同的消费者处理器，也可使用相同的处理器。
         * 实际场景中应该多数使用不同的处理器，因为正常来讲独立消费者做的应该是不同的事。
         * 所以本例中是定义了两个不同的消费者。
         */
        disruptor.handleEventsWith(
                faceInfoChangedRenameHandler,
                faceInfoChangedStateHandler
        );
    }
}
