package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundDefinition;
import com.bdtd.modules.mineral_pressure.service.IGroundSoundDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 地音系统基础信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@RestController
@RequestMapping("/bdtd/groundSoundDefinition")
@Api(tags = "数据接入-地音系统")
public class GroundSoundDefinitionController {
    @Autowired
    private IGroundSoundDefinitionService groundSoundDefinitionService;

    @PostMapping("/selectBySystemId")
    @ApiOperation(value = "查询地音监测测点", notes = "查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mineCode", value = "矿编号",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "mineName", value = "矿名称",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "relationTunnel", value = "关联巷道",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "passagewayNumber", value = "通道号",  dataType = "String"),

    })
    public ResponseEntity selectBySystemId(GroundSoundDefinition entity, Integer current, Integer size) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        return groundSoundDefinitionService.selectBySystemId(page, entity);
    }
}
