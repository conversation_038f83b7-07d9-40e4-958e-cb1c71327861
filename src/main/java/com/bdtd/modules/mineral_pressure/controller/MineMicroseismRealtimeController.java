package com.bdtd.modules.mineral_pressure.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.MineMicroseismRealtime;
import com.bdtd.modules.mineral_pressure.service.IMineMicroseismRealtimeService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.entity.ListOptionVo;
import com.bdtd.util.page.PageQueryLegacy;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 矿震记录表 控制器
 *
 * <AUTHOR>
 * @date 2022-05-14
 * @description 矿震记录表
 */
@Slf4j
@RestController
@RequestMapping("/mineralPressure/mineMicroseismRealtime")
@Api(value = "mineMicroseismRealtime", tags = "矿震记录表管理模块")
public class MineMicroseismRealtimeController
{

    private final IMineMicroseismRealtimeService mineMicroseismRealtimeService;

    public MineMicroseismRealtimeController(IMineMicroseismRealtimeService mineMicroseismRealtimeService) {
        this.mineMicroseismRealtimeService = mineMicroseismRealtimeService;
    }

    /**
     * 列表查询
     *
     * @param mineMicroseismRealtimeEntity 矿震记录表
     * @return Result
     */
    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping("/list")
    public ResponseEntity<Msg> getMineMicroseismRealtimeList(MineMicroseismRealtime mineMicroseismRealtimeEntity) {
        Msg msg;
        try {
            LambdaQueryWrapper<MineMicroseismRealtime> query = new LambdaQueryWrapper<>();
            query.eq(
                         StringUtils.isNotEmpty(mineMicroseismRealtimeEntity.getAreaName()),
                         MineMicroseismRealtime::getAreaName,
                         mineMicroseismRealtimeEntity.getAreaName()
                 ).like(
                         StringUtils.isNotEmpty(mineMicroseismRealtimeEntity.getPosDesc()),
                         MineMicroseismRealtime::getPosDesc,
                         mineMicroseismRealtimeEntity.getPosDesc()
                 )
                 .orderByDesc(MineMicroseismRealtime::getTimestamp);

            List<MineMicroseismRealtime> listResult = mineMicroseismRealtimeService.list(query);
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", listResult);
        }
        catch (Exception e) {
            log.error("列表查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param mineMicroseismRealtimeEntity 矿震记录表
     * @return Result
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/paged")
    public ResponseEntity<Msg> getMineMicroseismRealtimePage(PageQueryLegacy page, MineMicroseismRealtime mineMicroseismRealtimeEntity) {
        Msg msg;
        try {
            Page<MineMicroseismRealtime> p = new Page<>(page.getCurrent(), page.getSize());

            LambdaQueryWrapper<MineMicroseismRealtime> query = new LambdaQueryWrapper<>();
            query.eq(
                         StringUtils.isNotEmpty(mineMicroseismRealtimeEntity.getAreaName()),
                         MineMicroseismRealtime::getAreaName,
                         mineMicroseismRealtimeEntity.getAreaName()
                 ).like(
                         StringUtils.isNotEmpty(mineMicroseismRealtimeEntity.getPosDesc()),
                         MineMicroseismRealtime::getPosDesc,
                         mineMicroseismRealtimeEntity.getPosDesc()
                 )
                 .orderByDesc(MineMicroseismRealtime::getTimestamp);

            Page<MineMicroseismRealtime> pageResult = mineMicroseismRealtimeService.page(p, query);
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", pageResult);
        }
        catch (Exception e) {
            log.error("分页查询异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * 获取区域列表
     *
     * @return Result
     */
    @ApiOperation(value = "获取区域列表", notes = "获取区域列表")
    @RequestMapping(value = "/areaList", method = RequestMethod.GET)
    public ResponseEntity<Msg> getAreaList() {
        Msg msg;
        try {
            List<ListOptionVo<String>> listResult = mineMicroseismRealtimeService.getAreaOptionList();
            msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", listResult);
        }
        catch (Exception e) {
            log.error("删除异常, Exception: " + e.getMessage(), e);
            msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
        }
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    // /**
    //  * 通过id删除矿震记录表
    //  *
    //  * @param id id
    //  * @return Result
    //  */
    // @ApiOperation(value = "通过id删除矿震记录表", notes = "通过id删除矿震记录表")
    // @DeleteMapping("/{id}")
    // public ResponseEntity<Msg> removeById(@PathVariable Integer id) {
    //     Msg msg;
    //     try {
    //         boolean delResult = mineMicroseismRealtimeService.removeById(id);
    //         msg = ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", delResult);
    //     }
    //     catch (Exception e) {
    //         log.error("删除异常, Exception: " + e.getMessage(), e);
    //         msg = ReturnMsg.resultMsg(StatusEnum.ERROR.value, "error", null);
    //     }
    //     return new ResponseEntity<>(msg, HttpStatus.OK);
    // }
}
