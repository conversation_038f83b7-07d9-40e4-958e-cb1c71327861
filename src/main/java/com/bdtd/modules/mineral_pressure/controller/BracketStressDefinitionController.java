package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import com.bdtd.modules.mineral_pressure.service.IBracketStressDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 矿压-支架阻力 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
@RestController
@RequestMapping("/bdtd/bracketStressDefinition")
@Api(tags = "数据接入-矿压-支架阻力")
public class BracketStressDefinitionController {

    @Autowired
    private IBracketStressDefinitionService bracketStressDefinitionService;

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询支架阻力定义表", notes = "查询")
    public ResponseEntity selectBySystemId(AnchorStressDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return bracketStressDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }

}
