package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.SlightShockDefinition;
import com.bdtd.modules.mineral_pressure.service.ISlightShockDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 微震采集点信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@RestController
@RequestMapping("/bdtd/slightShockDefinition")
@Api(tags = "数据接入-微震监控系统")
public class SlightShockDefinitionController {

    @Autowired
    private ISlightShockDefinitionService slightShockDefinitionService;

    @PostMapping("/selectBySystemId")
    @ApiOperation(value = "查询微震监控系统信息", notes = "查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pointId", value = "监测点",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "areaName", value = "监测区域",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "passagewayAmount", value = "记录通道数",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "installMode", value = "安装方式",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "sensorType", value = "传感器类型",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "location", value = "安装位置",  dataType = "String"),
    })
    public ResponseEntity selectBySystemId(SlightShockDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return slightShockDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }

}
