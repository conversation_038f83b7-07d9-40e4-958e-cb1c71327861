package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.SlightShockRealtime;
import com.bdtd.modules.mineral_pressure.service.ISlightShockRealtimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@RestController
@RequestMapping("/bdtd/slightShockRealtime")
@Api(tags = "数据接入-微震监控系统")
public class SlightShockRealtimeController {

    @Autowired
    private ISlightShockRealtimeService slightShockRealtimeService;

    @PostMapping("/selectRealtimeData")
    @ApiOperation(value = "查询微震监控系统实时信息", notes = "查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "eventId", value = "事件编号",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "passagewayAmount", value = "记录地音事件的通道号",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "location", value = "微震事件位置描述",  dataType = "String"),
    })
    public ResponseEntity selectRealtimeData(SlightShockRealtime entity, Integer current, Integer size) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        return slightShockRealtimeService.selectRealtimeData(page, entity);
    }

}
