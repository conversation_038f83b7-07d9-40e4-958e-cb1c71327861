package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.TunnelDistanceStressDefinition;
import com.bdtd.modules.mineral_pressure.service.ITunnelDistanceStressDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequestMapping("/bdtd/tunnelDistanceStressDefinition")
@Api(tags = "数据接入-巷道位移表")
public class TunnelDistanceStressDefinitionController {

    @Resource
    private ITunnelDistanceStressDefinitionService iTunnelDistanceStressDefinitionService;

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询巷道位移表", notes = "查询")
    public ResponseEntity selectBySystemId(TunnelDistanceStressDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return iTunnelDistanceStressDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }
}
