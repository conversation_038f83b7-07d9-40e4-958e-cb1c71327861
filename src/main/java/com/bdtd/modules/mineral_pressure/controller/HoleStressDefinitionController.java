package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.HoleStressDefinition;
import com.bdtd.modules.mineral_pressure.service.IHoleStressDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 矿压-钻孔应力 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
@RestController
@RequestMapping("/bdtd/holeStressDefinition")
@Api(tags = "数据接入-矿压-钻孔应力")
public class HoleStressDefinitionController {

    @Autowired
    private IHoleStressDefinitionService holeStressDefinitionService;

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询钻孔应力定义表", notes = "查询")
    public ResponseEntity selectBySystemId(HoleStressDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return holeStressDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }

}
