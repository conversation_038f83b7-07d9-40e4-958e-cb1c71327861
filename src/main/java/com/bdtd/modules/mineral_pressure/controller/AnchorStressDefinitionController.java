package com.bdtd.modules.mineral_pressure.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import com.bdtd.modules.mineral_pressure.service.IAnchorStressDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 锚杆索应力定义前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping({ "/bdtd/anchorStressDefinition", "/mineralPressure/anchorStressDefinition" })
@Api(tags = "数据接入-锚索应力表")
public class AnchorStressDefinitionController
{

    private final IAnchorStressDefinitionService iAnchorStressDefinitionService;

    public AnchorStressDefinitionController(IAnchorStressDefinitionService iAnchorStressDefinitionService) {
        this.iAnchorStressDefinitionService = iAnchorStressDefinitionService;
    }

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询锚索应力定义表", notes = "查询")
    public ResponseEntity selectBySystemId(AnchorStressDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return iAnchorStressDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }

    @GetMapping("pointMonitorTypeList")
    @ApiOperation(value = "通道监测类型下拉列表", notes = "通道监测类型下拉列表")
    public ResponseEntity pointMonitorTypeList() {
        return iAnchorStressDefinitionService.pointMonitorTypeList();
    }

}
