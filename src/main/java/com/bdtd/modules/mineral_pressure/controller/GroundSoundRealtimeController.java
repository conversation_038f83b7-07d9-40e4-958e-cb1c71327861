package com.bdtd.modules.mineral_pressure.controller;


import com.bdtd.modules.hydrology.entity.WaterObservationRealtime;
import com.bdtd.modules.mineral_pressure.service.IGroundSoundRealtimeService;
import com.bdtd.util.web.Msg;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 地音系统实时数据信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@RestController
@RequestMapping("/bdtd/groundSoundRealtime")
@Api(tags = "数据接入-地音系统")
public class GroundSoundRealtimeController {
    @Autowired
    private IGroundSoundRealtimeService groundSoundRealtimeService;

    @ApiOperation("根据测点ID查询束管实时数据 - 查历史数据库")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "success", response = WaterObservationRealtime.class)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mineCode", value = "矿编号",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "relationTunnel", value = "关联巷道",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "passagewayNumber", value = "通道号",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "staTime", value = "开始时间", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "polymerize", value = "聚合度（格式：1s 或 1m）", dataType = "String"),
    })
    @PostMapping("/list")
    public ResponseEntity list(@RequestParam(required = true) String mineCode,@RequestParam(required = true) String relationTunnel,@RequestParam(required = true) String passagewayNumber, String staTime, String endTime, String polymerize) {
        Msg msg = groundSoundRealtimeService.findRealtimeList(mineCode, relationTunnel, passagewayNumber, staTime, endTime, polymerize);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

}
