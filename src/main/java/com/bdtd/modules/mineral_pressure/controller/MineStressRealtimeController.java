package com.bdtd.modules.mineral_pressure.controller;

import com.bdtd.modules.automation.entity.DataAutomationRealtime;
import com.bdtd.modules.mineral_pressure.service.IMineStressRealtimeService;
import com.bdtd.util.web.Msg;
import io.swagger.annotations.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequestMapping("/bdtd/mineStressRealtime")
@Api(tags = "数据接入-矿压实时数据表")
public class MineStressRealtimeController {

    @Resource
    private IMineStressRealtimeService mineStressRealtimeService;

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询矿压实时表", notes = "查询")
    public ResponseEntity selectBySystemId(@RequestParam(required = false,value="pointId")String  pointId, @RequestParam(required = false,value="start") String start,
                                           @RequestParam(required = false,value="end") String end,@RequestParam(required = false,value="polymerize") String polymerize) {

        return mineStressRealtimeService.selectData(pointId,start,end,polymerize);
    }


    @ApiOperation("根据测点ID查询大型设备测点值")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "success", response = DataAutomationRealtime.class)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pointId", value = "测点ID", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "staTime", value = "开始时间", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "polymerize", value = "聚合度（格式：1s 或 1m）", dataType = "String"),
    })
    @PostMapping("/list")
    public ResponseEntity list(@RequestParam(required = true) String pointId, String staTime, String endTime, String polymerize) {
        Msg msg = mineStressRealtimeService.findRealtimeList(pointId, staTime, endTime, polymerize);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
