package com.bdtd.modules.mineral_pressure.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.RoofStressDefinition;
import com.bdtd.modules.mineral_pressure.service.IRoofStressDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequestMapping("/bdtd/roofStressDefinition")
@Api(tags = "数据接入-顶板离层表")
public class RoofStressDefinitionController {

    @Resource
    private IRoofStressDefinitionService iRoofStressDefinitionService;

    @PostMapping("selectBySystemId")
    @ApiOperation(value = "查询顶板离层表", notes = "查询")
    public ResponseEntity selectBySystemId(RoofStressDefinition entity, Integer current, Integer size, Integer deletedAtIsNull) {
        Page page = new Page();
        page.setCurrent(null != current ? current : 0);
        page.setSize(null != size ? size : 10);
        // 默认获取有效数据
        if (deletedAtIsNull == null) {
            deletedAtIsNull = 1;
        }
        return iRoofStressDefinitionService.selectBySystemId(page, entity, deletedAtIsNull);
    }

    @PostMapping("selectAll")
    @ApiOperation(value = "查询矿压视图", notes = "查询矿压视图")
    public ResponseEntity selectAll() {

        return iRoofStressDefinitionService.selectAll();
    }

}
