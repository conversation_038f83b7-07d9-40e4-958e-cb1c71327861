package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.SlightShockRealtime;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface SlightShockRealtimeMapper extends BaseMapper<SlightShockRealtime> {

    IPage<Map> selectRealtimeData(Page page, SlightShockRealtime entity);
}
