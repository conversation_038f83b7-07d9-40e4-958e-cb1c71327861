package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface AnchorStressDefinitionMapper extends BaseMapper<AnchorStressDefinition> {

    IPage<Map> selectBySystemId(Page page, @Param("entity") AnchorStressDefinition entity, @Param("deletedAtIsNull") Integer deletedAtIsNull);


    List<String> pointMonitorTypeList();

}
