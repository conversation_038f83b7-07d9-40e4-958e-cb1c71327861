package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.HoleStressDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 矿压-钻孔应力 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
public interface HoleStressDefinitionMapper extends BaseMapper<HoleStressDefinition> {

    IPage<Map> selectBySystemId(Page page, @Param("entity") HoleStressDefinition entity, @Param("deletedAtIsNull") Integer deletedAtIsNull);
}
