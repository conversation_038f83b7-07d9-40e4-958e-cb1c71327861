package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.SlightShockDefinition;

import java.util.Map;

/**
 * <p>
 * 微震采集点信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface SlightShockDefinitionMapper extends BaseMapper<SlightShockDefinition> {

    IPage<Map> selectBySystemId(Page page, SlightShockDefinition entity, Integer deletedAtIsNull);
}
