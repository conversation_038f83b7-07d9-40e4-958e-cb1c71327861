package com.bdtd.modules.mineral_pressure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bdtd.modules.mineral_pressure.entity.MineStressRealtime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface MineStressRealtimeMapper extends BaseMapper<MineStressRealtime> {

    List<MineStressRealtime> selectData(@Param("pointId") String pointId,@Param("start") String start,@Param("end") String end,@Param("polymerize") String polymerize);
}
