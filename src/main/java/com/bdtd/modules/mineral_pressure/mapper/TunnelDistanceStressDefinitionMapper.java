package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.TunnelDistanceStressDefinition;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface TunnelDistanceStressDefinitionMapper extends BaseMapper<TunnelDistanceStressDefinition> {

    IPage<Map> selectBySystemId(Page page, @Param("entity") TunnelDistanceStressDefinition entity, @Param("deletedAtIsNull") Integer deletedAtIsNull);

}
