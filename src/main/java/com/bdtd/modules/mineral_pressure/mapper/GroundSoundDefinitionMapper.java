package com.bdtd.modules.mineral_pressure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundDefinition;

import java.util.Map;

/**
 * <p>
 * 地音系统基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface GroundSoundDefinitionMapper extends BaseMapper<GroundSoundDefinition> {

    IPage<Map> selectBySystemId(Page page, GroundSoundDefinition entity);
}
