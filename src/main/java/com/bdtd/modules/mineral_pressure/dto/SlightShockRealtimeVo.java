package com.bdtd.modules.mineral_pressure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: SlightShockRealtimeVo
 * @projectName convert_monitor
 * @description: TODO
 * @date 2022/1/1814:45
 */

@Data
public class SlightShockRealtimeVo {

    /**
     * 事件编号
     */
    private String eventId;

    /**
     * 记录地音事件的通道号
     */
    private String passagewayAmount;

    /**
     * 震源坐标X
     */
    private String x;

    /**
     * 震源坐标Y
     */
    private String y;

    /**
     * 震源坐标Z
     */
    private String z;

    /**
     * 震源能量
     */
    private String energy;

    /**
     * 震源震级
     */
    private String level;

    /**
     * 微震事件位置描述
     */
    private String location;

    /**
     * 最大振幅
     */
    private String maxAmplitude;

    /**
     * 振幅单位
     */
    private String amplitudeUnit;

    /**
     * 平均振幅
     */
    private String avgAmplitude;

    /**
     * 微震事件波形主频
     */
    private String dominantFrequency;

    /**
     * 微震事件波形主频单位
     */
    private String dominantFrequencyUnit;

    /**
     * 发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dataTime;

    /**
     * 事件分析结论
     */
    private String analysisResults;
}
