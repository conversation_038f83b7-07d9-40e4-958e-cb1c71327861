package com.bdtd.modules.mineral_pressure.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: GroundSoundDefinitionVo
 * @projectName convert_monitor
 * @description: TODO
 * @date 2022/1/1310:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GroundSoundDefinitionVo implements Serializable {

    /**
     * 矿编码
     */
    private String mineCode;

    /**
     * 矿名称
     */
    private String mineName;

    /**
     * 关联巷道
     */
    private String relationTunnel;

    /**
     * 通道数
     */
    private String passagewayAmount;

    /**
     * 采样长度
     */
    private String samplingLength;

    /**
     * 采样长度单位
     */
    private String samplingLengthUnit;

    /**
     * 采样频率
     */
    private String samplingFrequency;

    /**
     * 采样频率单位
     */
    private String samplingFrequencyUnit;

    /**
     * 传感器方向
     */
    private String sensorDirection;

    /**
     * 传感器方向名称
     */
    private String sensorDirectionName;

    /**
     * 安装方式
     */
    private String installMode;

    /**
     * 灵敏度
     */
    private String sensitivity;

    /**
     * 灵敏度
     */
    private String sensitivityUnit;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 频次
     */
    private String frequency;

    /**
     * 阀值
     */
    private String threshold;

    /**
     * 通道号
     */
    private String passagewayNumber;

    /**
     * 异常描述
     */
    private String describe;

    /**
     * 状态
     */
    private String status;

    /**
     * 监测值
     */
    private String value;


}
