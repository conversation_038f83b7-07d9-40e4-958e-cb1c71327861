package com.bdtd.modules.mineral_pressure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: SlightShockDefinitionVo
 * @projectName convert_monitor
 * @description: TODO
 * @date 2022/1/1814:33
 */

@Data
public class SlightShockDefinitionVo {
    /**
     * 监测点ID
     */
    private String pointId;

    /**
     * 监测区名称
     */
    private String areaName;

    /**
     * 记录通道数
     */
    private String passagewayAmount;

    /**
     * 采样长度
     */
    private String samplingLength;

    /**
     * 采样长度单位
     */
    private String samplingLengthUnit;

    /**
     * 采样频率
     */
    private String samplingFrequency;

    /**
     * 采样频率单位
     */
    private String samplingFrequencyUnit;

    /**
     * 传感器方向
     */
    private String sensorDirection;

    /**
     * 传感器方向名称
     */
    private String sensorDirectionName;

    /**
     * 安装方式
     */
    private String installMode;

    /**
     * 灵敏度
     */
    private String sensitivity;

    /**
     * 灵敏度单位
     */
    private String sensitivityUnit;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 传感器位置
     */
    private String location;

    /**
     * 传感器坐标 X
     */
    private String x;

    /**
     * 传感器坐标Y
     */
    private String y;

    /**
     * 传感器坐标 Z
     */
    private String z;

    /**
     * 安装时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date installTime;

}
