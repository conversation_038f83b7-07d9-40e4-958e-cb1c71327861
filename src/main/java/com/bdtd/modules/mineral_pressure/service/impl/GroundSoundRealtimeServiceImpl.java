package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundDefinition;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundRealtime;
import com.bdtd.modules.mineral_pressure.mapper.GroundSoundDefinitionMapper;
import com.bdtd.modules.mineral_pressure.mapper.GroundSoundRealtimeMapper;
import com.bdtd.modules.mineral_pressure.service.IGroundSoundRealtimeService;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.util.DateUtil;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 地音系统实时数据信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Slf4j
@Service
public class GroundSoundRealtimeServiceImpl extends ServiceImpl<GroundSoundRealtimeMapper, GroundSoundRealtime> implements IGroundSoundRealtimeService
{

    // @Autowired
    // private InfluxDBConnect influxDb;

    @Autowired
    private GroundSoundDefinitionMapper groundSoundDefinitionMapper;
    @Autowired
    TaosConnector taosConnector;

    @Override
    public Msg findRealtimeList(String mineCode, String relationTunnel, String passagewayNumber, String staTime, String endTime, String polymerize) {
        // 时间格式字符串
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        // 开始、结束时间字符串
        String startTimeStr = null;
        String endTimeStr = null;
        try {
            Long startTime = DateUtil.dateToStampLong(staTime, "yyyy-MM-dd HH:mm:ss") - 8 * 60 * 60 * 1000;
            Long enTime = DateUtil.dateToStampLong(endTime, "yyyy-MM-dd HH:mm:ss") - 8 * 60 * 60 * 1000;

            startTimeStr = dtf.format(LocalDateTime.ofEpochSecond(startTime / 1000, 0, ZoneOffset.ofHours(8)));
            endTimeStr = dtf.format(LocalDateTime.ofEpochSecond(enTime / 1000, 0, ZoneOffset.ofHours(8)));
        }
        catch (Exception e) {
            log.warn("startTime or endTime parsed error: {}", e.getMessage(), e);
            return ReturnMsg.fail("开始时间或结束时间格式不正确");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            return ReturnMsg.fail("聚合度无效");
        }

        // 检查点位是否存在
        QueryWrapper<GroundSoundDefinition> queryWrap = new QueryWrapper<>();
        queryWrap.eq("mine_code", mineCode);
        queryWrap.eq("relation_tunnel", relationTunnel);
        queryWrap.eq("passageway_number", passagewayNumber);
        int existedCount = groundSoundDefinitionMapper.selectCount(queryWrap);
        if (existedCount == 0) {
            return ReturnMsg.fail("查询的位置不存在");
        }

        StringBuilder sb = new StringBuilder();
        sb.append("select last(time) as `timestamp`, last(value) ");
        sb.append("from `ground_sound_history`").append(" ");
        sb.append("where `mine_code` = '").append(mineCode).append("' ");
        sb.append("and `relation_tunnel` = '").append(relationTunnel).append("' ");
        sb.append("and `passageway_number` = '").append(passagewayNumber).append("' ");
        // 开始、结束时间已做检查，避免SQL注入风险
        sb.append("and `time` >= '").append(startTimeStr).append("' ");
        sb.append("and `time` <= '").append(endTimeStr).append("' ");
        sb.append("group by `time`").append(" ");
        sb.append("order by `time` ASC");

        String sql = StringUtil.trimJoinedSql(sb.toString());

        List<Map<String, Object>> normal = new ArrayList<>();
        try {
            normal = taosConnector.query(sql);
        }
        catch (TaosDBException ex) {
            log.error("findRealtimeList error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
        }
        catch (Exception ex) {
            log.error("findRealtimeList error: " + ex.getMessage(), ex);
        }

        if (normal == null || normal.isEmpty()) {
            return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "not found data", null);
        }

        return ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", normal);
    }
}
