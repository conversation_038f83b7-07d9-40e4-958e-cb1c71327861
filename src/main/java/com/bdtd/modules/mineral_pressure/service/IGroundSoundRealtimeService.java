package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundRealtime;
import com.bdtd.util.web.Msg;

/**
 * <p>
 * 地音系统实时数据信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface IGroundSoundRealtimeService extends IService<GroundSoundRealtime> {
    Msg findRealtimeList(String mineCode, String relationTunnel, String passagewayNumber, String staTime, String endTime, String polymerize);
}
