package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.HoleStressDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 矿压-钻孔应力 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
public interface IHoleStressDefinitionService extends IService<HoleStressDefinition> {

    ResponseEntity selectBySystemId(Page page, HoleStressDefinition entity, Integer deletedAtIsNull);
}
