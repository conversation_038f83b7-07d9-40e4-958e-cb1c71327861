package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.TunnelDistanceStressDefinition;
import com.bdtd.modules.mineral_pressure.mapper.TunnelDistanceStressDefinitionMapper;
import com.bdtd.modules.mineral_pressure.service.ITunnelDistanceStressDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Service
public class TunnelDistanceStressDefinitionServiceImpl extends ServiceImpl<TunnelDistanceStressDefinitionMapper, TunnelDistanceStressDefinition> implements ITunnelDistanceStressDefinitionService {


    @Override
    public ResponseEntity selectBySystemId(Page page, TunnelDistanceStressDefinition entity, Integer deletedAtIsNull) {
        IPage<Map> list = baseMapper.selectBySystemId(page, entity, deletedAtIsNull);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);

    }


}
