package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import com.bdtd.modules.mineral_pressure.entity.BracketStressDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 矿压-支架阻力 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
public interface IBracketStressDefinitionService extends IService<BracketStressDefinition> {

    ResponseEntity selectBySystemId(Page page, AnchorStressDefinition entity, Integer deletedAtIsNull);
}
