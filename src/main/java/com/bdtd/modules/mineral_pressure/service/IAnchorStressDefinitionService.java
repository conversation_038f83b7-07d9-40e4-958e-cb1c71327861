package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface IAnchorStressDefinitionService extends IService<AnchorStressDefinition> {

    ResponseEntity selectBySystemId(Page page, AnchorStressDefinition entity, Integer deletedAtIsNull);

    ResponseEntity pointMonitorTypeList();

}
