package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.AnchorStressDefinition;
import com.bdtd.modules.mineral_pressure.entity.BracketStressDefinition;
import com.bdtd.modules.mineral_pressure.mapper.BracketStressDefinitionMapper;
import com.bdtd.modules.mineral_pressure.service.IBracketStressDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 矿压-支架阻力 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
@Service
public class BracketStressDefinitionServiceImpl extends ServiceImpl<BracketStressDefinitionMapper, BracketStressDefinition> implements IBracketStressDefinitionService {

    @Override
    public ResponseEntity selectBySystemId(Page page, AnchorStressDefinition entity, Integer deletedAtIsNull) {
        IPage<Map> list = baseMapper.selectBySystemId(page, entity, deletedAtIsNull);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }
}
