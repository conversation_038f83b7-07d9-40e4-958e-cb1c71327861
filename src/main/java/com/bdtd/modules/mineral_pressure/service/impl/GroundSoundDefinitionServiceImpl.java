package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundDefinition;
import com.bdtd.modules.mineral_pressure.mapper.GroundSoundDefinitionMapper;
import com.bdtd.modules.mineral_pressure.service.IGroundSoundDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 地音系统基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Service
public class GroundSoundDefinitionServiceImpl extends ServiceImpl<GroundSoundDefinitionMapper, GroundSoundDefinition> implements IGroundSoundDefinitionService {

    @Override
    public ResponseEntity selectBySystemId(Page page, GroundSoundDefinition entity) {
        IPage<Map> list = baseMapper.selectBySystemId(page, entity);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }
}
