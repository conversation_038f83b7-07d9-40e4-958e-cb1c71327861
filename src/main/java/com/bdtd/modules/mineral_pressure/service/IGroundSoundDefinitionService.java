package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.GroundSoundDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 地音系统基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface IGroundSoundDefinitionService extends IService<GroundSoundDefinition> {

    ResponseEntity selectBySystemId(Page page, GroundSoundDefinition entity);
}
