package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.MineMicroseismRealtime;
import com.bdtd.modules.mineral_pressure.mapper.MineMicroseismRealtimeMapper;
import com.bdtd.modules.mineral_pressure.service.IMineMicroseismRealtimeService;
import com.bdtd.util.entity.ListOptionVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 矿震记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-14
 */
@Service
public class MineMicroseismRealtimeServiceImpl extends ServiceImpl<MineMicroseismRealtimeMapper, MineMicroseismRealtime> implements IMineMicroseismRealtimeService
{
    private final MineMicroseismRealtimeMapper mineMicroseismRealtimeMapper;

    public MineMicroseismRealtimeServiceImpl(MineMicroseismRealtimeMapper mineMicroseismRealtimeMapper) {
        this.mineMicroseismRealtimeMapper = mineMicroseismRealtimeMapper;
    }

    @Override
    public List<ListOptionVo<String>> getAreaOptionList() {
        List<Map<String, Object>> resultList = mineMicroseismRealtimeMapper.selectAreaList();

        List<ListOptionVo<String>> retList = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            if (!map.containsKey("areaName") || map.get("areaName") == null) {
                continue;
            }

            ListOptionVo vo = ListOptionVo
                    .builder()
                    .value(map.get("areaName").toString())
                    .label(map.get("areaName").toString())
                    .build();
            retList.add(vo);
        }

        return retList;
    }

}
