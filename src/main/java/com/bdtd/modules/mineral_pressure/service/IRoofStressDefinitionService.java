package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.RoofStressDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 */
public interface IRoofStressDefinitionService extends IService<RoofStressDefinition> {

    ResponseEntity selectBySystemId(Page page, RoofStressDefinition entity, Integer deletedAtIsNull);

    ResponseEntity selectAll();
}
