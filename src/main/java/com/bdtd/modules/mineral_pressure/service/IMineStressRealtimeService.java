package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.MineStressRealtime;
import com.bdtd.util.web.Msg;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface IMineStressRealtimeService extends IService<MineStressRealtime> {



    ResponseEntity selectData(String pointId, String start, String end,String polymerize);

    Msg findRealtimeList(String pointId, String staTime, String endTime, String polymerize);
}
