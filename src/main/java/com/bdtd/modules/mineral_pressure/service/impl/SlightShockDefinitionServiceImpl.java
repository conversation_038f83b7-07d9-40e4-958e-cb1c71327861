package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.SlightShockDefinition;
import com.bdtd.modules.mineral_pressure.mapper.SlightShockDefinitionMapper;
import com.bdtd.modules.mineral_pressure.service.ISlightShockDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 微震采集点信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Service
public class SlightShockDefinitionServiceImpl extends ServiceImpl<SlightShockDefinitionMapper, SlightShockDefinition> implements ISlightShockDefinitionService {

    @Override
    public ResponseEntity selectBySystemId(Page page, SlightShockDefinition entity, Integer deletedAtIsNull) {
        IPage<Map> list = baseMapper.selectBySystemId(page, entity, deletedAtIsNull);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }
}
