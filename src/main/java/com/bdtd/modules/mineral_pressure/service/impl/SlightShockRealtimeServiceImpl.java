package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.SlightShockRealtime;
import com.bdtd.modules.mineral_pressure.mapper.SlightShockRealtimeMapper;
import com.bdtd.modules.mineral_pressure.service.ISlightShockRealtimeService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Service
public class SlightShockRealtimeServiceImpl extends ServiceImpl<SlightShockRealtimeMapper, SlightShockRealtime> implements ISlightShockRealtimeService {

    @Override
    public ResponseEntity selectRealtimeData(Page page, SlightShockRealtime entity) {
        IPage<Map> list = baseMapper.selectRealtimeData(page, entity);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }
}
