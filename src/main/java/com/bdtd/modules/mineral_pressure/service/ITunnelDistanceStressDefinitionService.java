package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.TunnelDistanceStressDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 */
public interface ITunnelDistanceStressDefinitionService extends IService<TunnelDistanceStressDefinition> {

    ResponseEntity selectBySystemId(Page page, TunnelDistanceStressDefinition entity, Integer deletedAtIsNull);

}
