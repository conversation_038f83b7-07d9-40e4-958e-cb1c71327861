package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.MineStressRealtime;
import com.bdtd.modules.mineral_pressure.mapper.MineStressRealtimeMapper;
import com.bdtd.modules.mineral_pressure.service.IMineStressRealtimeService;
import com.bdtd.modules.ventilation.entity.ChargingCabinetDefinition;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.StringUtil;
import com.bdtd.util.exception.BadRequestException;
import com.bdtd.util.tdengine.TaosConnector;
import com.bdtd.util.tdengine.common.TaosDBException;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Service
public class MineStressRealtimeServiceImpl extends ServiceImpl<MineStressRealtimeMapper, MineStressRealtime> implements IMineStressRealtimeService
{
    @Autowired
    private MineStressRealtimeMapper mineStressRealtimeMapper;
    @Autowired
    TaosConnector taosConnector;

    @Override
    public ResponseEntity selectData(String pointId, String start, String end, String polymerize) {

        List<MineStressRealtime> list = baseMapper.selectData(pointId, start, end, polymerize);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }

    @Override
    public Msg findRealtimeList(String pointId, String staTime, String endTime, String polymerize) {
        if (StringUtil.isEmpty(pointId)) {
            return ReturnMsg.fail("点位ID为空");
        }

        LocalDateTime lStartTime = null;
        LocalDateTime lEndTime = null;

        try {
            if (StringUtil.isNotEmpty(staTime)) {
                lStartTime = LocalDateTime.parse(staTime);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                lEndTime = LocalDateTime.parse(endTime);
            }
        }
        catch (Exception e) {
            return ReturnMsg.fail("时间格式错误");
        }

        if (!TaosConnector.isPolymerizeValid(polymerize)) {
            return ReturnMsg.fail("聚合度无效");
        }

        // 检查点位是否存在
        if (StringUtil.isNotEmpty(pointId)) {
            QueryWrapper<MineStressRealtime> queryWrap = new QueryWrapper<>();
            queryWrap.eq("point_id", pointId);
            int existedCount = mineStressRealtimeMapper.selectCount(queryWrap);
            if (existedCount == 0) {
                return ReturnMsg.fail("查询的点位不存在");
            }
        }

        StringBuffer sb = new StringBuffer();

        sb.append("select last(`time`) as `timestamp`, last(`value`) as `value`, last(`value`) as deep_monitor_value from ");
        sb.append("`mine_stress_history_").append(pointId).append("` ");
        sb.append("where ");

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        if (lStartTime != null) {
            sb.append("and `time` >= '").append(dtf.format(lStartTime)).append("' ");
        }
        if (lEndTime != null) {
            sb.append("and `time` <= '").append(dtf.format(lEndTime)).append("' ");
        }

        if (StringUtil.isNotEmpty(polymerize)) {
            sb.append("interval(").append(polymerize).append(") ");
        }

        sb.append("order by `time` desc ");

        String sql = StringUtil.trimJoinedSql(sb.toString());

        List<Map<String, Object>> returnList = new ArrayList<>();
        try {
            List<Map<String, Object>> result = taosConnector.query(sql);
            if (result != null) {
                for (Map<String, Object> map : result) {
                    if (!map.containsKey("value") || map.get("value") == null) {
                        continue;
                    }
                    map.put("point_id", pointId);
                    returnList.add(map);
                }
            }

            return ReturnMsg.success(returnList);
        }
        catch (TaosDBException ex) {
            log.error("queryHistoryList error: " + ex.getMessage() + ", SQL: " + ex.getExecuteSql(), ex);
        }
        catch (Exception ex) {
            log.error("queryHistoryList error: " + ex.getMessage(), ex);
        }

        return ReturnMsg.fail("no data found");
    }

}
