package com.bdtd.modules.mineral_pressure.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdtd.modules.mineral_pressure.entity.HoleStressDefinition;
import com.bdtd.modules.mineral_pressure.mapper.HoleStressDefinitionMapper;
import com.bdtd.modules.mineral_pressure.service.IHoleStressDefinitionService;
import com.bdtd.util.StatusEnum;
import com.bdtd.util.web.ReturnMsg;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 矿压-钻孔应力 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
@Service
public class HoleStressDefinitionServiceImpl extends ServiceImpl<HoleStressDefinitionMapper, HoleStressDefinition> implements IHoleStressDefinitionService {

    @Override
    public ResponseEntity selectBySystemId(Page page, HoleStressDefinition entity, Integer deletedAtIsNull) {
        IPage<Map> list = baseMapper.selectBySystemId(page, entity, deletedAtIsNull);
        return new ResponseEntity<>(ReturnMsg.resultMsg(StatusEnum.SUCCESS.value, "success", list), HttpStatus.OK);
    }
}
