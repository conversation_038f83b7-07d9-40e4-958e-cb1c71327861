package com.bdtd.modules.mineral_pressure.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bdtd.modules.mineral_pressure.entity.SlightShockDefinition;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 微震采集点信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface ISlightShockDefinitionService extends IService<SlightShockDefinition> {

    ResponseEntity selectBySystemId(Page page, SlightShockDefinition entity, Integer deletedAtIsNull);
}
