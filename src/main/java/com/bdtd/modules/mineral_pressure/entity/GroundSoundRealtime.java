package com.bdtd.modules.mineral_pressure.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 地音系统实时数据信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GroundSoundRealtime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 矿编码
     */
    private String mineCode;

    /**
     * 关联巷道
     */
    private String relationTunnel;

    /**
     * 传感器实时值
     */
    private Double value;

    /**
     * 通道号
     */
    private String passagewayNumber;

    /**
     * 状态
     */
    private String status;

    /**
     * 上传数据时间yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime timestamp;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
