package com.bdtd.modules.mineral_pressure.entity;

import com.bdtd.util.json.JsonLocalDateTimeDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 矿压实时数据表
 * </p>
 */
@Data
@EqualsAndHashCode()
@Accessors(chain = true)
public class MineStressRealtime implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 传感器编号
     */
    private String pointId;
    /**
     * 矿编码
     */
    private String mineCode;
    /**
     * 时间戳
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime timestamp;
    /**
     * 状态
     */
    private String state;
    /**
     * 矿井名称
     */
    private String mineName;
    /**
     * 通道号
     */
    private String channelNo;
    /**
     * 监测值
     */
    private String monitorValue;



}
