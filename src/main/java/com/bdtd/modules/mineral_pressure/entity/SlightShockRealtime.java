package com.bdtd.modules.mineral_pressure.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SlightShockRealtime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件编号
     */
    private String eventId;

    /**
     * 记录地音事件的通道号
     */
    private String passagewayAmount;

    /**
     * 震源坐标X
     */
    private String x;

    /**
     * 震源坐标Y
     */
    private String y;

    /**
     * 震源坐标Z
     */
    private String z;

    /**
     * 震源能量
     */
    private String energy;

    /**
     * 震源震级
     */
    private String level;

    /**
     * 微震事件位置描述
     */
    private String location;

    /**
     * 最大振幅
     */
    private String maxAmplitude;

    /**
     * 振幅单位
     */
    private String amplitudeUnit;

    /**
     * 平均振幅
     */
    private String avgAmplitude;

    /**
     * 微震事件波形主频
     */
    private String dominantFrequency;

    /**
     * 微震事件波形主频单位
     */
    private String dominantFrequencyUnit;

    /**
     * 发生时间
     */
    private LocalDateTime dataTime;

    /**
     * 事件分析结论
     */
    private String analysisResults;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
