package com.bdtd.modules.mineral_pressure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bdtd.util.json.JsonLocalDateTimeDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 巷道表面位移数据表
 * </p>
 */
@Data
@EqualsAndHashCode()
@Accessors(chain = true)
public class TunnelDistanceStressDefinition implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 传感器编号
     */
    private String pointId;

    /**
     * 矿编码
     */
    private String mineCode;

    /**
     * 监测区名称
     */
    private String monitoringAreaName;

    /**
     * 煤矿监测系统名称
     */
    private String systemName;
    /**
     * 监测系统型号
     */
    private String monitorType;
    /**
     * 相对位置描述
     */
    private String pointRelativePosition;

    /**
     * 时间戳
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime installTime;

    /**
     * 传感器类型（2、顶板离层；3、巷道位移 ；4、锚杆索应力）
     */
    private Integer sensorStatus;

    /**
     * 传感器名称
     */
    private String sensorType;
    /**
     * 传感器位置
     */
    private String location;

    /**
     * 坐标
     */
    private Double x;

    /**
     * 坐标
     */
    private Double y;

    /**
     * 坐标
     */
    private Double z;
    /**
     * 系统异常状态描述
     */
    private String description;
    /**
     * 单位
     */
    private String unit;
    /**
     * 系统id
     */
    private String  systemId;

    /**
     * 巷道名称
     */
    private String tunnelName;

    /**
     * 巷道名称
     */
    private String monitoringDirection;

    /**
     * 集团编码
     */
    private String groupCode;
    /**
     * 测站类型
     */
    private String stationType;
    /**
     * 测站编号
     */
    private String  stationNumber;
    /**
     * 通道监测类型
     */
    private String pointMonitorType;
    /**
     * 数据使用通道
     */
    private String channelNo;

    @TableField(exist = false)
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    /**
     * 监测值
     */
    @TableField(exist = false)
    private String monitorValue;
}
