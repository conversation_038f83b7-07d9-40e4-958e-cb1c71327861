package com.bdtd.modules.mineral_pressure.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 矿压-钻孔应力
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HoleStressDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 传感器编号
     */
    private String pointId;

    /**
     * 矿编码
     */
    private String mineCode;

    /**
     * 监测区名称
     */
    private String monitoringAreaName;

    /**
     * 煤矿监测系统名称
     */
    private String systemName;

    /**
     * 监测系统型号
     */
    private String monitorType;

    /**
     * 相对位置描述
     */
    private String pointRelativePosition;

    /**
     * 安装深度
     */
    private String installDepth;

    /**
     * 安装深度单位
     */
    private String installDepthUnit;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 传感器类型（1、支架阻力;  2、顶板离层；3、巷道位移 ；4、锚杆索应力; 5、钻孔应力）
     */
    private Integer sensorStatus;

    /**
     * 传感器名称
     */
    private String sensorType;

    /**
     * 传感器位置
     */
    private String location;

    /**
     * x坐标
     */
    private Double x;

    /**
     * y坐标
     */
    private Double y;

    /**
     * z坐标
     */
    private Double z;

    /**
     * 系统异常状态描述
     */
    private String description;

    /**
     * 单位
     */
    private String unit;

    /**
     * 系统id
     */
    private String systemId;

    /**
     * 测点类型:整形 0-模拟量 1-开关量 2- 状态量 3-累计量 4-文本量
     */
    private Integer pointType;

    /**
     * 值类型:必填项,int/float/string
     */
    private String dataType;

    /**
     * 巷道名称
     */
    private String tunnelName;

    /**
     * 监测方向
     */
    private String monitoringDirection;

    /**
     * 集团编码
     */
    private String groupCode;

    /**
     * 测站类型
     */
    private String stationType;

    /**
     * 初始应力
     */
    private String initialValue;

    /**
     * 数据采集时间格式为yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime dataTime;

    /**
     * 数据库插入时间,自动
     */
    private LocalDateTime insertDbTime;

    /**
     * 数据库更新时间,自动
     */
    private LocalDateTime updateDbTime;


}
