package com.bdtd.modules.mineral_pressure.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 地音系统基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GroundSoundDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 矿编码
     */
    private String mineCode;

    /**
     * 矿名称
     */
    private String mineName;

    /**
     * 关联巷道
     */
    private String relationTunnel;

    /**
     * 通道数
     */
    private String passagewayAmount;

    /**
     * 采样长度
     */
    private String samplingLength;

    /**
     * 采样长度单位
     */
    private String samplingLengthUnit;

    /**
     * 采样频率
     */
    private String samplingFrequency;

    /**
     * 采样频率单位
     */
    private String samplingFrequencyUnit;

    /**
     * 传感器方向
     */
    private String sensorDirection;

    /**
     * 传感器方向名称
     */
    private String sensorDirectionName;

    /**
     * 安装方式
     */
    private String installMode;

    /**
     * 灵敏度
     */
    private String sensitivity;

    /**
     * 灵敏度
     */
    private String sensitivityUnit;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 频次
     */
    private String frequency;

    /**
     * 阀值
     */
    private String threshold;

    /**
     * 通道号
     */
    private String passagewayNumber;

    /**
     * 异常描述
     */
    private String describe;


    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
