package com.bdtd.modules.mineral_pressure.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微震采集点信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SlightShockDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    private String pointId;

    /**
     * 监测区名称
     */
    private String areaName;

    /**
     * 记录通道数
     */
    private String passagewayAmount;

    /**
     * 采样长度
     */
    private String samplingLength;

    /**
     * 采样长度单位
     */
    private String samplingLengthUnit;

    /**
     * 采样频率
     */
    private String samplingFrequency;

    /**
     * 采样频率单位
     */
    private String samplingFrequencyUnit;

    /**
     * 传感器方向
     */
    private String sensorDirection;

    /**
     * 传感器方向名称
     */
    private String sensorDirectionName;

    /**
     * 安装方式
     */
    private String installMode;

    /**
     * 灵敏度
     */
    private String sensitivity;

    /**
     * 灵敏度单位
     */
    private String sensitivityUnit;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 传感器位置
     */
    private String location;

    /**
     * 传感器坐标 X
     */
    private String x;

    /**
     * 传感器坐标Y
     */
    private String y;

    /**
     * 传感器坐标 Z
     */
    private String z;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
