package com.bdtd.modules.mineral_pressure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.LocalDateTimeTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 矿震记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "矿震记录对象", description = "矿震记录表")
public class MineMicroseismRealtime implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    private Long id;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(jdbcType = JdbcType.TIMESTAMP_WITH_TIMEZONE, typeHandler = LocalDateTimeTypeHandler.class, update = "now()")
    @ApiModelProperty(value = "采集时刻")
    private LocalDateTime timestamp;

    @ApiModelProperty(value = "煤矿编码")
    private String mineCode;

    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    @ApiModelProperty(value = "工作面名称")
    private String areaName;

    @ApiModelProperty(value = "记录通道数")
    private Integer channelCount;

    @ApiModelProperty(value = "采样长度")
    private Integer sampleLength;

    @ApiModelProperty(value = "采样频率")
    private Integer sampleReq;

    @ApiModelProperty(value = "传感器方向")
    private String sensorDir;

    @ApiModelProperty(value = "安装方式")
    private String installMode;

    @ApiModelProperty(value = "灵敏度")
    private BigDecimal sensitivity;

    @ApiModelProperty(value = "传感器类型")
    private String sensorType;

    @ApiModelProperty(value = "震源位置X")
    private BigDecimal centrumX;

    @ApiModelProperty(value = "震源位置Y")
    private BigDecimal centrumY;

    @ApiModelProperty(value = "震源位置Z")
    private BigDecimal centrumZ;

    @ApiModelProperty(value = "震源能量")
    private BigDecimal energy;

    @ApiModelProperty(value = "震源震级")
    private BigDecimal level;

    @ApiModelProperty(value = "微震事件位置描述")
    private String posDesc;

    @ApiModelProperty(value = "最大振幅")
    private BigDecimal maxSwing;

    @ApiModelProperty(value = "平均振幅")
    private BigDecimal avgSwing;

    @ApiModelProperty(value = "波形频谱最大值")
    private BigDecimal basicFreq;

    @ApiModelProperty(value = "矿震事件的通道号")
    private String trigChannel;

    @ApiModelProperty(value = "系统异常状态描述")
    private String stateText;

    @ApiModelProperty(value = "备注")
    private String memo;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(jdbcType = JdbcType.TIMESTAMP_WITH_TIMEZONE, typeHandler = LocalDateTimeTypeHandler.class, update = "now()")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(jdbcType = JdbcType.TIMESTAMP_WITH_TIMEZONE, typeHandler = LocalDateTimeTypeHandler.class, update = "now()")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

}
