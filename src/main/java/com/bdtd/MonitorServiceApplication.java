package com.bdtd;

import com.github.jeffreyning.mybatisplus.conf.EnableMPP;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * MonitorServiceApplication
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.bdtd")
@MapperScan({ "com.bdtd.dao", "com.bdtd.mapper", "com.bdtd.modules.*.dao", "com.bdtd.modules.*.mapper" })
@ServletComponentScan
@EnableScheduling
@EnableMPP
public class MonitorServiceApplication implements CommandLineRunner
{
    public static void main(String[] args) {
        SpringApplication.run(MonitorServiceApplication.class, args);
    }

    // @Autowired
    // private ApplicationContext applicationContext;
    @Override
    public void run(String... args) {
        // String[] beans = applicationContext.getBeanDefinitionNames();
        // Arrays.sort(beans);
        // boolean contains = Arrays.stream(beans).anyMatch("workingFaceProgressSettingUpdateHandler"::equalsIgnoreCase);
        // if (contains) {
        //     System.out.println("+++ workingFaceProgressSettingUpdateHandler loaded");
        // }
        // else {
        //     System.out.println("+++ workingFaceProgressSettingUpdateHandler not loaded");
        // }
    }
}
