package com.bdtd.controller;

import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 *
 * <AUTHOR>
 */
@Api(tags = "服务器时间")
@RestController
@RequestMapping(value = "/time")
public class TimeController
{

    @GetMapping(value = "/now")
    public ResponseEntity<Msg> timestamp() {
        return new ResponseEntity<>(ReturnMsg.success(System.currentTimeMillis()), HttpStatus.OK);
    }

}
