package com.bdtd.controller;

import com.bdtd.modules.monitor.dto.ModelSystemAliveQueryCriteria;
import com.bdtd.modules.monitor.entity.ModelSystemAliveStatistics;
import com.bdtd.modules.monitor.mapper.ModelSystemAliveMapper;
import com.bdtd.modules.monitor.mapper.ModelSystemAliveStatisticsMapper;
import com.bdtd.util.web.Msg;
import com.bdtd.util.web.ReturnMsg;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务器工具
 *
 * <AUTHOR>
 */
@Api(tags = "服务器工具")
@RestController
@RequestMapping(value = "/tsm")
public class RequestController
{
    @Value("${dataroute.approval.minecode}")
    private Integer mineCode;

    @Autowired
    ModelSystemAliveMapper modelSystemAliveMapper;
    @Autowired
    private ModelSystemAliveStatisticsMapper modelSystemAliveStatisticsMapper;

    // @Autowired
    // PushMsgFeignService pushMsgFeignService;
    // @Autowired
    // IPushMsgService pushMsgService;

    @RequestMapping("/minecode")
    public Map<String, Object> requestMineCode()
    {
        Map<String, Object> map = new HashMap<>();
        map.put("code", "0");
        map.put("msg", mineCode);
        map.put("ts", "202408251835");
        return map;
    }

    @RequestMapping("/failed")
    public Map<String, String> requestFailed()
    {
        Map<String, String> map = new HashMap<>();
        map.put("code", "-1");
        map.put("msg", "请求错误");
        return map;
    }

    @RequestMapping("/debug")
    public ResponseEntity<Msg> requestDebug()
    {
        Map<String, Object> retMap = new HashMap<>();

        // 既存统计记录
        ModelSystemAliveQueryCriteria queryCriteria = new ModelSystemAliveQueryCriteria();
        queryCriteria.setSystemId("TEST100");
        List<ModelSystemAliveStatistics> existedStatisticsList = modelSystemAliveStatisticsMapper
                .getNowBreakageList(queryCriteria);
        retMap.put("getNowBreakageList", existedStatisticsList);

        // region 获取预警报警服务中指标的报警状态
        // List<ModelSystemAlive> msaList = modelSystemAliveMapper.queryEnabledList();
        // if (msaList == null
        //     || msaList.isEmpty()
        //     || msaList.stream().noneMatch(msa -> StringUtil.isNotEmpty(msa.getIndicatorId()))) {
        //     return new ResponseEntity<>(ReturnMsg.success(retMap), HttpStatus.OK);
        // }
        //
        // Set<String> indicatorIdSet = msaList
        //         .stream()
        //         .map(ModelSystemAlive::getIndicatorId)
        //         .filter(StringUtil::isNotEmpty)
        //         .collect(Collectors.toSet());
        // retMap.put("reqSize", indicatorIdSet.size());
        //
        // // JSONObject getRet = pushMsgFeignService.getWarningStatusByAlarmId(indicatorIdSet.iterator().next());
        // // retMap.put("getRet", getRet);
        // JSONObject queryRet = pushMsgFeignService.queryWarningStatusByAlarmIds(indicatorIdSet);
        // retMap.put("queryRet", queryRet);
        // Map<String, Integer> statusMap = pushMsgService.getIndicatorAlarmStatus(indicatorIdSet);
        // retMap.put("indicatorMap", statusMap);
        // endregion 获取预警报警服务中指标的报警状态

        return new ResponseEntity<>(ReturnMsg.success(retMap), HttpStatus.OK);
    }

}
