# FROM m.daocloud.io/docker.io/library/maven:3.9-eclipse-temurin-8-focal AS builder
FROM harbor2.qdbdtd.com:8088/bjproduct/maven:3.9-eclipse-temurin-8-focal AS builder

# Arthas
#ADD https://arthas.aliyun.com/arthas-boot.jar /tmp
COPY ./.mvn/arthas-boot.jar /tmp
# Maven settings
COPY ./.mvn/settings.xml /usr/share/maven/conf/settings.xml
# POM.xml
COPY ./pom.xml /app/

# 设置当前工作路径
WORKDIR /app

# 优化Maven构建参数
ENV MAVEN_OPTS="-Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true"
# 加载 maven 依赖包, 利用缓存提高编译速度
RUN mvn dependency:go-offline -B

# 使用.dockerignore文件排除不必要的文件
COPY ./ /app/
# Maven 打包
RUN mvn clean package -DskipTests -B -T 4C

# 运行基础镜像
# FROM m.daocloud.io/docker.io/library/ibm-semeru-runtimes:open-8-jdk-focal
FROM harbor2.qdbdtd.com:8088/bjproduct/ibm-semeru-runtimes:open-8-jdk-focal

# 替换软件源方式
RUN sed -i s@/archive.ubuntu.com/@/mirrors.aliyun.com/@g /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y less vim tzdata openssh-client \
    && apt-get clean
RUN rm /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    rm -rf /var/lib/apt/lists/*

# 改用 RESTful 方式连接
# # 安装 TDengine 客户端
# WORKDIR /home/<USER>
# COPY --from=builder /app/doc/TDengine-client-*******-Linux-x64.tar.gz /home/<USER>/TDengine-client-*******-Linux-x64.tar.gz
# RUN tar -zxvf /home/<USER>/TDengine-client-*******-Linux-x64.tar.gz
# WORKDIR /home/<USER>/TDengine-client-*******
# RUN /home/<USER>/TDengine-client-*******/install_client.sh

# 部署应用
WORKDIR /home/<USER>
WORKDIR /home/<USER>/dump
WORKDIR /home/<USER>/config
WORKDIR /home/<USER>
COPY --from=builder /app/config/*.properties /home/<USER>/config/
COPY --from=builder /app/target/*.jar /home/<USER>/iot_timescale-monitor.jar
COPY --from=builder /tmp/arthas-boot.jar /home/<USER>

ENV MES_BIZ_HOST=http://backend-mes-biz:9001
ENV GIS_BIZ_HOST=http://backend-gis-biz:9001
ENV BLADE_BACKEND_URL=http://backend-platform:9401
ENV MOS_VIDEO_URL=http://video-server:9000
ENV IVS_BACKEND_URL=http://ivs-server:9001

EXPOSE 9001

# 启动应用
#CMD ["java", "-Xms4g", "-Xmx4g", "-jar", "/home/<USER>/iot_timescale-monitor.jar"]
#CMD ["java", "-Xms4g", "-Xmx4g", "-jar", "/home/<USER>/iot_timescale-monitor.jar", "--spring.profiles.active=pro"]
#CMD ["java", "-Xms4g", "-Xmx4g", "-jar", "/home/<USER>/iot_timescale-monitor.jar", "--spring.config.location=/home/<USER>/application-pro.properties"]
CMD ["java", "-jar", "/home/<USER>/iot_timescale-monitor.jar", "-Xms4g", "-Xmx4g", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/home/<USER>/dump/heap.hprof", "-XX:+PrintGCDetails", "-XX:+PrintGCTimeStamps", "-XX:+PrintGCDateStamps", "-Xloggc:/home/<USER>/dump/gc.log", "-XX:+UseGCLogFileRotation", "-XX:NumberOfGCLogFiles=20", "-XX:GCLogFileSize=5M"]
