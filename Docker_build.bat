@echo off
setlocal EnableDelayedExpansion
echo --------------------------------------------------------
echo Build docker image from dist
echo example: Docker_build.bat [branch] [tag] [comment]
echo --------------------------------------------------------

REM Branch not assigned, print help info and exit
if "%~1"=="" (
  echo Branch name not provided.
  echo Command: Docker_build.bat [branch] [tag] [comment]
  echo Example 1: Docker_build.bat cjsch-dev cjsch-dev "test comment"
  echo Example 2: Docker_build.bat cjsch-dev cjsch-dev
  echo Example 3: Docker_build.bat cjsch-dev
  exit
)

set /p inputUbs="Press ENTER to update branch source, q to exit, others to continue: "
if "!inputUbs!"=="" (
  REM Update branch source
  git switch %~1
  git pull
) else (
  if "!inputUbs!"=="q" (
    goto END_EXIT
  )
)

REM No tag provided, only build and push image
if "%~2"=="" (
  set /p inputNbdi="Press ENTER to build the Docker image, q to exit, others to continue: "
  if "!inputNbdi!"=="" (
    REM Build docker image
    docker build -t harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~1" .
  ) else (
    if "!inputNbdi!"=="q" (
      goto END_EXIT
    )
  )

  set /p inputNpih=" Press ENTER to push the image to Harbor, q to exit, others to continue:"
  if "!inputNpih!"=="" (
    REM Push image to harbor
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~1"
  ) else (
    if "!inputNpih!"=="q" (
      goto END_EXIT
    )
  )
) else (
  REM Tag provided, build and push image, and push tag to Gitee

  set /p inputTbdi="Press ENTER to build the Docker image, q to exit, others to continue: "
  if "!inputTbdi!"=="" (
    REM Build docker image
    docker build -t harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~2" .
  ) else (
    if "!inputTbdi!"=="q" (
      goto END_EXIT
    )
  )

  set /p inputTpih="Press ENTER to push the image to Harbor, q to exit, others to continue: "
  if "!inputTpih!"=="" (
    REM Tag image's tag and branch
    docker tag harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~2" harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~1"
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~2"
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:"%~1"
  ) else (
    if "!inputTpih!"=="q" (
      goto END_EXIT
    )
  )

  set /p inputTptg="Press ENTER to push the tag to Gitee, q to exit, others to continue: "
  if "!inputTptg!"=="" (
    REM Push tag to Gitee
    git tag -a "%~2" -m "%~3"
    git push origin "%~2"
  ) else (
    if "!inputTptg!"=="q" (
      goto END_EXIT
    )
  )
)

:END_EXIT
echo.
echo Build success.
pause
